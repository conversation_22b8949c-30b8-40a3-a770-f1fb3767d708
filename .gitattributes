* text=auto eol=lf
*.blade.php diff=html
*.css diff=css
*.html diff=html
*.md diff=markdown
*.php diff=php
/.github export-ignore
CHANGELOG.md export-ignore
.styleci.yml export-ignore
public/assets/**/*.woff filter=lfs diff=lfs merge=lfs -text
public/assets/**/*.woff2 filter=lfs diff=lfs merge=lfs -text
public/assets/**/*.ttf filter=lfs diff=lfs merge=lfs -text
public/assets/**/*.eot filter=lfs diff=lfs merge=lfs -text
public/assets/**/*.otf filter=lfs diff=lfs merge=lfs -text
# Track all PNG files
*.png filter=lfs diff=lfs merge=lfs -text
# Track all GIF files
*.gif filter=lfs diff=lfs merge=lfs -text
# Track all APNG files
*.apng filter=lfs diff=lfs merge=lfs -text
