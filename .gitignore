/.phpunit.cache
/node_modules
/public/build
/public/hot
/public/storage
/public/uploads/*.jpg
/public/uploads/*.png
/public/uploads/*.mp4
/public/uploads/*.jpg
/public/uploads/*.png
/public/uploads/*.mp4
/public/uploads
/storage/*.key
/vendor
.env.backup
.env
.user.ini
.env.production
.phpunit.result.cache
Homestead.json
Homestead.yaml
auth.json
npm-debug.log
yarn-error.log
/.fleet
/.idea
/.vscode

# Ignore macOS .DS_Store files
.DS_Store
/config/database.php
composer.lock
.well-known
