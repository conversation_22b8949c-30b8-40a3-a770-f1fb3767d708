# Hướng dẫn cấu hình ngày gia hạn hệ thống

## Tổng quan
Tính năng này cho phép cấu hình ngày gia hạn hệ thống thông qua file `.env`. <PERSON><PERSON> hệ thống hết hạn, người dùng vẫn có thể đăng nhập nhưng sẽ bị chuyển về dashboard và hiển thị thông báo hết hạn, đồng thời chặn tất cả các thao tác khác.

## Cấu hình

### 1. Thêm vào file .env
```env
# System Expiration Configuration
SYSTEM_EXPIRY_DATE=14/7/2025
```

**Định dạng ngày:** `dd/mm/yyyy` (ví dụ: 14/7/2025 = 14 tháng 7 năm 2025)

### 2. Cấu hình mẫu
```env
# Hệ thống hoạt động bình thường (ngày trong tương lai)
SYSTEM_EXPIRY_DATE=31/12/2025

# Hệ thống hết hạn (ngày trong quá khứ - để test)
SYSTEM_EXPIRY_DATE=01/01/2024

# Không cấu hình (tính năng tắt)
# SYSTEM_EXPIRY_DATE=
```

## Tính năng

### 1. Hiển thị thông tin ngày gia hạn
- **Dashboard:** Badge hiển thị ngày gia hạn với màu sắc phù hợp
- **Header:** Badge hiển thị trên tất cả các trang admin
- **Animation:** Hiệu ứng nhấp nháy để thu hút sự chú ý

### 2. Khi hệ thống hết hạn
- **Popup thông báo:** Hiển thị ngay khi truy cập dashboard
- **Chặn navigation:** Không cho phép truy cập các trang khác
- **Chặn form submit:** Không cho phép gửi form
- **Chặn AJAX:** Trả về lỗi 403 cho các request AJAX
- **Cho phép logout:** Vẫn có thể đăng xuất khỏi hệ thống

### 3. Routes được phép khi hết hạn
- `admin.dashboard` - Trang dashboard
- `logout` - Đăng xuất
- `admin.dashboard.stats-api` - API thống kê dashboard
- `admin.test.expiry` - Trang test tính năng

## Kiểm tra tính năng

### 1. Truy cập trang test
Vào menu admin → "Test System Expiry" để kiểm tra:
- Thông tin ngày gia hạn hiện tại
- Trạng thái hệ thống (còn hiệu lực/hết hạn)
- Test các tính năng (AJAX, Navigation, Form Submit)

### 2. Test với ngày hết hạn
1. Đặt `SYSTEM_EXPIRY_DATE=01/01/2024` (ngày trong quá khứ)
2. Truy cập dashboard → sẽ hiển thị popup hết hạn
3. Thử click vào các menu khác → sẽ bị chặn
4. Thử submit form → sẽ bị chặn

### 3. Test với ngày còn hiệu lực
1. Đặt `SYSTEM_EXPIRY_DATE=31/12/2025` (ngày trong tương lai)
2. Truy cập dashboard → hiển thị badge cảnh báo màu vàng
3. Tất cả tính năng hoạt động bình thường

## Helper Functions

### 1. Kiểm tra hệ thống hết hạn
```php
if (is_system_expired()) {
    // Hệ thống đã hết hạn
}
```

### 2. Lấy ngày gia hạn
```php
$expiry_date = get_system_expiry_date(); // Trả về "14/7/2025"
```

## Middleware

### CheckSystemExpiry
- **Vị trí:** `app/Http/Middleware/CheckSystemExpiry.php`
- **Áp dụng:** Tất cả routes admin
- **Chức năng:** 
  - Kiểm tra ngày hết hạn
  - Chuyển hướng khi hết hạn
  - Chặn AJAX requests
  - Lưu thông tin vào session

## Giao diện

### 1. Badge ngày gia hạn
- **Màu vàng:** Hệ thống còn hiệu lực
- **Màu đỏ:** Hệ thống đã hết hạn
- **Animation:** Hiệu ứng pulse để thu hút sự chú ý

### 2. Popup thông báo hết hạn
- **Hiển thị:** Khi truy cập dashboard lần đầu sau khi hết hạn
- **Nội dung:** Thông báo hết hạn và hướng dẫn
- **Nút:** Tiếp tục xem hoặc Đăng xuất

### 3. Chặn thao tác
- **JavaScript:** Chặn click link và submit form
- **Alert:** Hiển thị thông báo khi bị chặn
- **AJAX:** Xử lý response 403 và hiển thị thông báo

## Lưu ý

### 1. Định dạng ngày
- **Bắt buộc:** dd/mm/yyyy
- **Ví dụ đúng:** 14/7/2025, 01/01/2024
- **Ví dụ sai:** 2025-07-14, 7/14/2025

### 2. Timezone
- Sử dụng timezone của server
- So sánh đến cuối ngày (23:59:59)

### 3. Cache
- Thông tin được cache trong session
- Middleware chạy trên mỗi request

### 4. Bảo mật
- Chỉ admin mới thấy thông tin ngày gia hạn
- Middleware chỉ áp dụng cho routes admin

## Troubleshooting

### 1. Không hiển thị badge
- Kiểm tra cấu hình `SYSTEM_EXPIRY_DATE` trong .env
- Kiểm tra định dạng ngày (dd/mm/yyyy)

### 2. Không chặn thao tác khi hết hạn
- Kiểm tra middleware đã được áp dụng
- Kiểm tra JavaScript trong layout admin

### 3. Lỗi parse ngày
- Kiểm tra log Laravel
- Đảm bảo định dạng ngày đúng

### 4. Popup không hiển thị
- Kiểm tra session `system_expired`
- Kiểm tra JavaScript console

## Tùy chỉnh

### 1. Thêm routes được phép
Sửa file `app/Http/Middleware/CheckSystemExpiry.php`:
```php
$allowedRoutes = [
    'admin.dashboard',
    'logout',
    'admin.dashboard.stats-api',
    'admin.test.expiry',
    'admin.your.custom.route' // Thêm route mới
];
```

### 2. Thay đổi thông báo
Sửa file `resources/views/layouts/admin.blade.php` hoặc `resources/views/admin/dashboard/index.blade.php`

### 3. Tùy chỉnh giao diện
Sửa CSS trong các file view tương ứng
