// Copy and run these commands in php artisan tinker one by one

use App\Models\Setting;
use Carbon\Carbon;

// Check if custom_script_head setting exists
$exists = Setting::where('type', 'custom_script_head')->exists();
if (!$exists) {
    Setting::create([
        'type' => 'custom_script_head',
        'description' => '',
        'created_at' => Carbon::now(),
        'updated_at' => Carbon::now()
    ]);
    echo "custom_script_head setting created\n";
} else {
    echo "custom_script_head setting already exists\n";
}

// Check if custom_script_body_start setting exists
$exists = Setting::where('type', 'custom_script_body_start')->exists();
if (!$exists) {
    Setting::create([
        'type' => 'custom_script_body_start',
        'description' => '',
        'created_at' => Carbon::now(),
        'updated_at' => Carbon::now()
    ]);
    echo "custom_script_body_start setting created\n";
} else {
    echo "custom_script_body_start setting already exists\n";
}

// Check if custom_script_footer setting exists
$exists = Setting::where('type', 'custom_script_footer')->exists();
if (!$exists) {
    Setting::create([
        'type' => 'custom_script_footer',
        'description' => '',
        'created_at' => Carbon::now(),
        'updated_at' => Carbon::now()
    ]);
    echo "custom_script_footer setting created\n";
} else {
    echo "custom_script_footer setting already exists\n";
} 