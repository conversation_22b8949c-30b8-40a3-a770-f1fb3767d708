<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Addon;
use App\Models\Permission;
use Illuminate\Support\Facades\Auth;

class AddonsController extends Controller
{
    public function index()
    {
        $page_data['addons'] = Addon::all();
        return view('admin.addons.index', $page_data);
    }

    public function toggleStatus(Request $request, $id)
    {
        try {
            $addon = Addon::findOrFail($id);
            $previousStatus = $addon->status;

            // Check if addon is paid and not purchased
            if ($addon->status == 0 && $addon->price > 0 && !$addon->is_purchased) {
                return response()->json([
                    'status' => false,
                    'message' => 'Tiện ích này có phí: ' . $addon->formatted_price . '. Vui lòng truy cập topid.vn để mua và kích hoạt.'
                ]);
            }

            // Check if current package is sufficient for this addon
            if ($addon->status == 0 && !$addon->isPackageSufficient()) {
                $packageName = '';
                if ($addon->required_package == 'FREE') {
                    $packageName = 'Miễn phí';
                } elseif ($addon->required_package == 'BASIC') {
                    $packageName = 'Basic';
                } elseif ($addon->required_package == 'PRO') {
                    $packageName = 'PRO';
                } elseif ($addon->required_package == 'PREMIUM') {
                    $packageName = 'Premium';
                } else {
                    $packageName = $addon->required_package;
                }

                return response()->json([
                    'status' => false,
                    'message' => 'Bạn cần nâng cấp lên gói ' . $packageName . ' để kích hoạt tiện ích này.'
                ]);
            }

            // Toggle status
            $addon->status = !$addon->status;
            $addon->save();

            $user_id = Auth::id();

            // Handle permissions based on the new status
            if (!empty($addon->permission_keys)) {
                $permission = Permission::where('admin_id', $user_id)->first();

                if ($permission) {
                    $permissions = json_decode($permission->permissions, true) ?? [];

                    if ($addon->status) {
                        // Add permissions when activating
                        foreach ($addon->permission_keys as $key) {
                            if (!in_array($key, $permissions)) {
                                $permissions[] = $key;
                            }
                        }
                    } else {
                        // Remove permissions when deactivating
                        foreach ($addon->permission_keys as $key) {
                            $pos = array_search($key, $permissions);
                            if ($pos !== false) {
                                array_splice($permissions, $pos, 1);
                            }
                        }
                    }

                    // Update permissions
                    $permission->permissions = json_encode($permissions);
                    $permission->save();
                } else if ($addon->status) {
                    // Create new permission entry if it doesn't exist (only when activating)
                    Permission::create([
                        'admin_id' => $user_id,
                        'permissions' => json_encode($addon->permission_keys)
                    ]);
                }
            }

            return response()->json([
                'status' => true,
                'active' => $addon->status,
                'message' => $addon->status
                    ? get_phrase('Addon activated successfully.')
                    : get_phrase('Addon deactivated successfully.')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile()
            ], 500);
        }
    }

    // Helper function to get default SVG for addons without thumbnails
    public static function getDefaultThumbnail()
    {
        return asset('assets/backend/images/img/addon_default.gif');
    }

    /**
     * Toggle the purchased status of an addon
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function togglePurchased(Request $request, $id)
    {
        try {
            // Only allow root admin to change purchase status
            if (!is_root_admin(Auth::id())) {
                return response()->json([
                    'status' => false,
                    'message' => 'Chỉ quản trị viên cấp cao mới có quyền thay đổi trạng thái mua hàng.'
                ], 403);
            }

            $addon = Addon::findOrFail($id);

            // Toggle is_purchased status
            $addon->is_purchased = !$addon->is_purchased;
            $addon->save();

            return response()->json([
                'status' => true,
                'purchased' => $addon->is_purchased,
                'message' => $addon->is_purchased
                    ? 'Tiện ích đã được đánh dấu là đã mua.'
                    : 'Tiện ích đã được đánh dấu là chưa mua.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
