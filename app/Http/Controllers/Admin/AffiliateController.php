<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Affiliate;
use App\Models\OfflinePayment;
use App\Models\Setting;
use App\Models\Withdraw;
use DB;
use Illuminate\Http\Request;
use Illuminate\Notifications\Messages\MailMessage;

class AffiliateController extends Controller
{
    public function __construct()
    {
        date_default_timezone_set('Asia/Dhaka');
    }
    public function index($type = '')
    {
        // Thống kê doanh thu affiliate từ bảng offline_payments
        $affiliateStats = OfflinePayment::select('affiliate_id')
            ->selectRaw('
                SUM(CASE WHEN status = 1 AND is_approve_affiliate = 1 THEN affiliate_amount ELSE 0 END) as approved_revenue,
                SUM(CASE WHEN status = 1 AND is_approve_affiliate = 0 THEN affiliate_amount ELSE 0 END) as pending_revenue,
                COUNT(CASE WHEN status = 1 THEN 1 END) as total_orders,
                SUM(CASE WHEN status = 1 THEN affiliate_amount ELSE 0 END) as total_revenue
            ')
            ->whereNotNull('affiliate_id')
            ->whereNotNull('affiliate_amount')
            ->where('affiliate_amount', '>', 0)
            ->groupBy('affiliate_id')
            ->with('affiliater:id,name,email')
            ->orderByDesc('total_revenue')
            ->paginate(20)
            ->withQueryString();

        $data['affiliates'] = $affiliateStats;
        return view('admin.affiliate.index', $data);
    }
    public function withdraws(Request $request)
    {
        // Thiết lập thời gian mặc định
        $start_date = strtotime('first day of this month');
        $end_date = strtotime('last day of this month 23:59:59');
        $active_tab = 'cHome';
        $hasFilter = false;

        // Xác định tab hiện tại
        if ($request->has('active_tab')) {
            // Nếu có tham số active_tab trong URL, sử dụng giá trị này
            $active_tab = $request->input('active_tab');
        } else {
            // Nếu không có active_tab nhưng có tham số date range, xác định tab từ tham số
            foreach (['cHome', 'cProfile', 'cCanceled'] as $tab) {
                $paramName = "eDateRange{$tab}";
                if ($request->has($paramName)) {
                    $active_tab = $tab;
                    break;
                }
            }
        }

        // Khởi tạo date range mặc định cho các tab
        $tabDateRanges = [];
        $defaultDateRange = date('m/d/Y', $start_date) . ' - ' . date('m/d/Y', $end_date);

        foreach (['cHome', 'cProfile', 'cCanceled'] as $tab) {
            $tabDateRanges[$tab] = $defaultDateRange;
        }

        // Xử lý date range
        $tabNames = ['cHome', 'cProfile', 'cCanceled'];
        foreach ($tabNames as $tab) {
            $paramName = "eDateRange{$tab}";
            if ($request->has($paramName)) {
                $hasFilter = true;
                $dateRange = $request->get($paramName);
                $tabDateRanges[$tab] = $dateRange; // Lưu date range cho tab này

                if ($tab === $active_tab) {
                    $dates = explode(' - ', $dateRange);
                    if (count($dates) == 2) {
                        $start_date = strtotime($dates[0]);
                        $end_date = strtotime($dates[1] . ' 23:59:59');
                    }
                }
            }
        }

        // Chuẩn bị dữ liệu cho view
        $page_data = [
            'start_date' => $start_date,
            'end_date' => $end_date,
            'active_tab' => $active_tab,
            'has_filter' => $hasFilter,
            'tab_date_ranges' => $tabDateRanges // Thêm date range cho từng tab
        ];

        // Map giữa tab và status
        $tabToStatus = [
            'cHome' => 'pending',
            'cProfile' => 'complete',
            'cCanceled' => 'canceled'
        ];

        // Các status và mã tương ứng
        $statuses = [
            'complete' => '1',
            'pending' => '0',
            'canceled' => '2'
        ];

        // Convert timestamps to MySQL datetime format
        $start_datetime = date('Y-m-d H:i:s', $start_date);
        $end_datetime = date('Y-m-d H:i:s', $end_date);

        // Xử lý query cho mỗi status
        foreach ($statuses as $key => $status) {
            $query = Withdraw::where('status', $status)
                ->with([
                    "user" => function ($query) {
                        $query->select('id', 'name', 'email');
                    }
                ]);

            // Áp dụng date range nếu không có filter hoặc là tab đang active
            if (!$hasFilter || ($hasFilter && $tabToStatus[$active_tab] == $key)) {
                $query->whereBetween('created_at', [$start_datetime, $end_datetime]);
            }

            $page_data["affiliate_payout_{$key}_total_amount"] = $query->sum('amount');

            // Tạo đối tượng phân trang và đính kèm tham số active_tab vào URL
            $paginationResults = $query->paginate(10);
            $paginationResults->appends(['active_tab' => $active_tab]);

            // Nếu có filter, thêm tham số date range vào URL phân trang
            if ($hasFilter) {
                $paramName = "eDateRange{$active_tab}";
                if ($request->has($paramName)) {
                    $paginationResults->appends([$paramName => $request->get($paramName)]);
                }
            }

            $page_data["affiliate_payout_{$key}"] = $paginationResults;
        }

        return view('admin.affiliate.payout', $page_data);
    }
    public function affiliate_setting()
    {
        // Kiểm tra và lấy dữ liệu cài đặt
        $allow_affiliate = Setting::where('type', 'allow_affiliate')->first();
        $affiliate_notification = Setting::where('type', 'affiliate_notification')->first();
        $application_note = Setting::where('type', 'affiliate_application_note')->first();
        $affiliate_revenue = Setting::where('type', 'affiliate_revenue')->first();
        $affiliate_payout_days = Setting::where('type', 'affiliate_payout_days')->first();
        $affiliate_payout_min_amount = Setting::where('type', 'affiliate_payout_min_amount')->first();

        // Tạo dữ liệu mặc định nếu không tồn tại
        if (!$allow_affiliate) {
            $allow_affiliate = Setting::create([
                'type' => 'allow_affiliate',
                'description' => '1' // Mặc định cho phép
            ]);
        }

        if (!$affiliate_notification) {
            $affiliate_notification = Setting::create([
                'type' => 'affiliate_notification',
                'description' => '1' // Mặc định cho phép
            ]);
        }

        if (!$application_note) {
            $application_note = Setting::create([
                'type' => 'affiliate_application_note',
                'description' => 'Please follow our guidelines to become an affiliate.' // Ghi chú mặc định
            ]);
        }

        if (!$affiliate_revenue) {
            $affiliate_revenue = Setting::create([
                'type' => 'affiliate_revenue',
                'description' => '10' // Mặc định 10%
            ]);
        }

        if (!$affiliate_payout_days) {
            $affiliate_payout_days = Setting::create([
                'type' => 'affiliate_payout_days',
                'description' => '7' // Mặc định 7 ngày
            ]);
        }

        if (!$affiliate_payout_min_amount) {
            $affiliate_payout_min_amount = Setting::create([
                'type' => 'affiliate_payout_min_amount',
                'description' => '10000' // Mặc định 10000 VND
            ]);
        }
        $page_data = [
            'allow_affiliate' => $allow_affiliate,
            'affiliate_notification' => $affiliate_notification,
            'application_note' => $application_note,
            'affiliate_revenue' => $affiliate_revenue,
            'affiliate_payout_days' => $affiliate_payout_days,
            'affiliate_payout_min_amount' => $affiliate_payout_min_amount
        ];

        return view('admin.affiliate.affiliate_setting', $page_data);
    }

    public function affiliate_setting_store(Request $request)
    {
        $request->validate([
            'affiliate_revenue' => 'numeric|min:0|max:100',
            'allow_affiliate' => 'in:0,1',
            'affiliate_application_note' => 'nullable|string',
            'affiliate_payout_days' => 'numeric|min:0',
            'affiliate_notification' => 'in:0,1',
            'affiliate_payout_min_amount' => 'numeric|min:10000'
        ]);

        DB::beginTransaction();
        try {
            // Cập nhật affiliate_revenue
            Setting::updateOrCreate(
                ['type' => 'affiliate_revenue'],
                ['description' => $request->affiliate_revenue]
            );

            // Cập nhật allow_affiliate
            Setting::updateOrCreate(
                ['type' => 'allow_affiliate'],
                ['description' => $request->allow_affiliate]
            );

            // Cập nhật application_note
            Setting::updateOrCreate(
                ['type' => 'affiliate_application_note'],
                ['description' => $request->affiliate_application_note]
            );

            // Cập nhật affiliate_payout_days
            Setting::updateOrCreate(
                ['type' => 'affiliate_payout_days'],
                ['description' => $request->affiliate_payout_days]
            );

            // Cập nhật affiliate_payout_min_amount
            Setting::updateOrCreate(
                ['type' => 'affiliate_payout_min_amount'],
                ['description' => $request->affiliate_payout_min_amount]
            );

            // Cập nhật affiliate_notification
            Setting::updateOrCreate(
                ['type' => 'affiliate_notification'],
                ['description' => $request->affiliate_notification]
            );

            DB::commit();
            return redirect()->back()->with('success', get_phrase('Affiliate settings updated successfully'));
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', get_phrase('An error occurred: ') . $e->getMessage());
        }
    }
    // Cập nhật trạng thái bảng affiliates
    public function updateStatus($id, $status, Request $request)
    {
        DB::beginTransaction();
        try {
            $affiliate = Affiliate::findOrFail($id);
            $user = $affiliate->user;

            if (!$user) {
                DB::rollBack();
                return redirect()->back()->with('error', get_phrase('User not found'));
            }

            // Xử lý dựa trên trạng thái mới
            switch ($status) {
                case 0: // Cancel - Hủy bỏ
                    // Chỉ có thể hủy từ trạng thái Waiting (2)
                    if ($affiliate->status != 2) {
                        DB::rollBack();
                        return redirect()->back()->with('error', get_phrase('Only waiting affiliates can be canceled'));
                    }

                    // Trừ số tiền từ earning của người dùng
                    $user->earning -= $affiliate->amount;
                    $user->save();

                    // Cập nhật trạng thái affiliate thành Canceled
                    $affiliate->status = 0;
                    $affiliate->note = $request->input('note');
                    $affiliate->save();

                    $message = 'Affiliate canceled successfully';
                    break;

                case 1: // Complete - Hoàn thành
                    // Chỉ có thể hoàn thành từ trạng thái Waiting (2)
                    if ($affiliate->status != 2) {
                        DB::rollBack();
                        return redirect()->back()->with('error', get_phrase('Only waiting affiliates can be completed'));
                    }

                    // Chuyển tiền từ earning sang balance
                    $user->earning -= $affiliate->amount;
                    $user->balance += $affiliate->amount;
                    $user->save();

                    // Cập nhật trạng thái affiliate thành Completed
                    $affiliate->status = 1;
                    $affiliate->note = $request->input('note');
                    $affiliate->save();

                    $message = 'Affiliate completed and amount transferred to balance';
                    break;

                default:
                    DB::rollBack();
                    return redirect()->back()->with('error', get_phrase('Invalid status value'));
            }

            DB::commit();
            return redirect()->back()->with('success', get_phrase($message));

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', get_phrase('An error occurred: ') . $e->getMessage());
        }
    }

    // Cập nhật trạng thái bảng withdraws xác nhận rút tiền
    public function updateWithdrawStatus($id, $status, Request $request)
    {
        DB::beginTransaction();
        try {
            $withdraw = Withdraw::findOrFail($id);

            // Chỉ xử lý trạng thái Pending (0)
            if ($withdraw->status != 0) {
                return redirect()->back()->with('error', get_phrase('Only pending withdrawals can be updated'));
            }

            // Lấy thông tin người xử lý
            $admin = auth()->user();

            // Xử lý theo status mới
            switch ($status) {
                case 1: // Completed
                    $withdraw->status = "1";
                    $withdraw->processed_date = now();
                    $withdraw->processed_by = $admin->name;
                    $withdraw->processor_id = $admin->id; // Lưu ID người xử lý
                    $withdraw->save();
                    $message = 'Payment completed successfully';
                    // Gửi mail thông báo rút tiền thành công cho người dùng
                    try {
                        $withdraw->user->CustomEmailWithdrawAffiliateNotification($withdraw);
// Lưu lịch sử gửi mail

                    } catch (\Exception $e) {

                    }
                    break;

                case 2: // Canceled
                    $withdraw->status = "2";
                    $withdraw->processed_date = now();
                    $withdraw->processed_by = $admin->name;
                    $withdraw->processor_id = $admin->id; // Lưu ID người xử lý
                    $withdraw->note = "Canceled by admin"; // Lưu lý do hủy
                    $withdraw->save();

                    // Hoàn lại tiền vào tài khoản người dùng
                    $withdraw->user()->increment('balance', $withdraw->amount);
                    $message = 'Withdrawal request canceled successfully';
                    break;

                default:
                    DB::rollBack();
                    return redirect()->back()->with('error', get_phrase('Invalid status value'));
            }

            DB::commit();
            return redirect()->back()->with('success', get_phrase($message));

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', get_phrase('An error occurred: ') . $e->getMessage());
        }
    }
}
