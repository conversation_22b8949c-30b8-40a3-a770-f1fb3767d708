<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;

class BunnyStreamController extends Controller
{
    private $libraryId;
    private $accessKey;
    private $apiUrl;
    private $tempDir;
    private $maxUploadSizeMB;
    private $full_accessKey;
    private $url_media_delivery;

    public function __construct()
    {
        // Load configuration from config/bunny.php
        $this->libraryId = Config::get('bunny.library_id');
        $this->accessKey = Config::get('bunny.access_key');
        $this->full_accessKey = Config::get('bunny.full_accessKey');
        $this->url_media_delivery = Config::get('bunny.url_media_delivery');
        $this->apiUrl = Config::get('bunny.api_url');
        $this->tempDir = Config::get('bunny.temp_dir');
        $this->maxUploadSizeMB = Config::get('bunny.max_upload_size_mb');
    }

    /**
     * Check if Bunny Stream configuration is valid
     */
    private function validateBunnyConfig()
    {
        if (empty($this->libraryId) || empty($this->accessKey)) {
            return [
                'valid' => false,
                'message' => 'Cấu hình Bunny Stream chưa được thiết lập. Vui lòng liên hệ quản trị viên để cấu hình dịch vụ video.'
            ];
        }

        return ['valid' => true];
    }

    /**
     * Handle API errors and return user-friendly messages
     */
    private function handleApiError($response, $operation = 'thao tác')
    {
        $statusCode = $response->status();

        switch ($statusCode) {
            case 401:
                return 'Khóa API không hợp lệ. Vui lòng liên hệ quản trị viên để kiểm tra cấu hình.';
            case 403:
                return 'Không có quyền truy cập. Vui lòng kiểm tra quyền của khóa API.';
            case 404:
                return 'Thư viện video không tồn tại. Vui lòng kiểm tra cấu hình Library ID.';
            case 429:
                return 'Đã vượt quá giới hạn yêu cầu. Vui lòng thử lại sau ít phút.';
            case 500:
            case 502:
            case 503:
                return 'Dịch vụ video đang gặp sự cố. Vui lòng thử lại sau.';
            default:
                return "Không thể thực hiện {$operation}. Vui lòng thử lại hoặc liên hệ hỗ trợ kỹ thuật.";
        }
    }

    public function index()
    {
        // Validate configuration before showing the page
        $configCheck = $this->validateBunnyConfig();
        if (!$configCheck['valid']) {
            return view('admin.bunny_stream.index', [
                'configError' => $configCheck['message'],
                'maxUploadSize' => '0 MB',
                'maxUploadSizeBytes' => 0,
                'libraryId' => '',
                'url_media_delivery' => ''
            ]);
        }

        $maxUploadSize = $this->getMaxUploadSize();
        $maxUploadSizeBytes = $this->getMaxUploadSizeBytes();
        $libraryId = $this->libraryId;
        $url_media_delivery = $this->url_media_delivery;
        return view('admin.bunny_stream.index', compact('maxUploadSize', 'maxUploadSizeBytes','libraryId','url_media_delivery'));
    }

    public function upload(Request $request)
    {
        // Process by step
        $step = $request->input('step', 'create');

        if ($step === 'create') {
            return $this->createVideo($request);
        } elseif ($step === 'upload') {
            return $this->uploadVideo($request);
        }

        return response()->json(['error' => 'Invalid step'], 400);
    }

    private function createVideo(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
        ]);

        // Validate configuration
        $configCheck = $this->validateBunnyConfig();
        if (!$configCheck['valid']) {
            return response()->json(['error' => $configCheck['message']], 400);
        }

        try {
            // Create video on Bunny.net
            $response = Http::withHeaders([
                'accept' => 'application/json',
                'content-type' => 'application/json',
                'AccessKey' => $this->accessKey,
            ])->post($this->apiUrl . $this->libraryId . '/videos', [
                'title' => $request->title,
            ]);

            if (!$response->successful()) {
                $errorMessage = $this->handleApiError($response, 'tạo video');
                return response()->json(['error' => $errorMessage], 400);
            }

            $videoData = $response->json();

            // Tạo thư mục tạm để lưu các phần của video
            $tempPath = storage_path('app/' . $this->tempDir . '/' . $videoData['guid']);
            if (!File::exists($tempPath)) {
                File::makeDirectory($tempPath, 0755, true);
            }

            return response()->json([
                'guid' => $videoData['guid'],
                'message' => 'Video created successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Có lỗi xảy ra khi tạo video. Vui lòng thử lại.'], 500);
        }
    }

    private function uploadVideo(Request $request)
    {
        $request->validate([
            'guid' => 'required|string',
            'video' => 'required|file|mimes:mp4,mov,avi,flv|max:' . ($this->maxUploadSizeMB * 1024), // Convert MB to KB for validation
        ]);

        try {
            $guid = $request->input('guid');
            $videoFile = $request->file('video');
            $videoContents = file_get_contents($videoFile->getRealPath());

            // Upload video lên Bunny.net
            $uploadResponse = Http::withHeaders([
                'accept' => 'application/json',
                'AccessKey' => $this->accessKey,
                'Content-Type' => 'video/mp4',
            ])->withBody(
                $videoContents, 'video/mp4'
            )->put($this->apiUrl . $this->libraryId . '/videos/' . $guid);

            if (!$uploadResponse->successful()) {
                $errorMessage = $this->handleApiError($uploadResponse, 'tải video lên');
                return response()->json(['error' => $errorMessage], 400);
            }

            return response()->json([
                'guid' => $guid,
                'message' => 'Video uploaded successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Có lỗi xảy ra khi tải video lên. Vui lòng thử lại.'], 500);
        }
    }

    public function uploadChunk(Request $request)
    {
        $request->validate([
            'guid' => 'required|string',
            'video' => 'required|file',
            'chunk' => 'required|integer',
            'totalChunks' => 'required|integer',
        ]);

        try {
            $guid = $request->input('guid');
            $chunkNumber = $request->input('chunk');
            $totalChunks = $request->input('totalChunks');

            // Validate guid is not empty
            if (empty($guid)) {
                return response()->json(['error' => 'GUID cannot be empty'], 400);
            }

            // Lưu phần file vào thư mục tạm
            $tempPath = storage_path('app/' . $this->tempDir . '/' . $guid);

            // Ensure the directory exists
            if (!File::exists($tempPath)) {
                File::makeDirectory($tempPath, 0755, true);
            }

            $chunkFile = $request->file('video');

            // Validate chunk file exists
            if (!$chunkFile || !$chunkFile->isValid()) {
                return response()->json(['error' => 'Invalid chunk file'], 400);
            }

            // Use forward slashes for path consistency across operating systems
            $chunkPath = $tempPath . '/chunk_' . $chunkNumber;

            // Check if the path is valid before writing
            if (empty($chunkPath)) {
                return response()->json(['error' => 'Invalid chunk path'], 500);
            }

            // Alternative approach to get file contents
            // Instead of using getRealPath(), use the file's path directly
            if (!$chunkFile->getPathname()) {
                return response()->json(['error' => 'Cannot get file pathname'], 500);
            }

            // Move the uploaded file directly to the destination
            if (!$chunkFile->move(dirname($chunkPath), 'chunk_' . $chunkNumber)) {
                return response()->json(['error' => 'Failed to move uploaded file'], 500);
            }

            return response()->json([
                'success' => true,
                'message' => "Chunk $chunkNumber of $totalChunks uploaded successfully",
                'chunk' => $chunkNumber,
                'totalChunks' => $totalChunks
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Có lỗi xảy ra khi tải lên phần của video. Vui lòng thử lại.'], 500);
        }
    }

    public function finalizeUpload(Request $request)
    {
        $request->validate([
            'guid' => 'required|string',
        ]);

        try {
            $guid = $request->input('guid');
            $tempPath = storage_path('app/' . $this->tempDir . '/' . $guid);
            $outputPath = $tempPath . '/complete.mp4';

            // Kết hợp tất cả các phần thành một file hoàn chỉnh
            $chunkFiles = File::glob($tempPath . '/chunk_*');
            sort($chunkFiles, SORT_NATURAL); // Sắp xếp theo thứ tự tự nhiên

            // Tạo file đầu ra
            $outputFile = fopen($outputPath, 'wb');

            // Ghi từng phần vào file đầu ra
            foreach ($chunkFiles as $chunkFile) {
                $chunk = File::get($chunkFile);
                fwrite($outputFile, $chunk);
                // Xóa phần đã xử lý
                File::delete($chunkFile);
            }

            fclose($outputFile);

            // Upload file hoàn chỉnh lên Bunny.net
            $videoContents = File::get($outputPath);

            $uploadResponse = Http::withHeaders([
                'accept' => 'application/json',
                'AccessKey' => $this->accessKey,
                'Content-Type' => 'video/mp4',
            ])->withBody(
                $videoContents, 'video/mp4'
            )->put($this->apiUrl . $this->libraryId . '/videos/' . $guid);

            // Xóa thư mục tạm
            File::deleteDirectory($tempPath);

            if (!$uploadResponse->successful()) {
                $errorMessage = $this->handleApiError($uploadResponse, 'hoàn tất tải video lên');
                return response()->json(['error' => $errorMessage], 400);
            }

            return response()->json([
                'guid' => $guid,
                'message' => 'Video uploaded successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Có lỗi xảy ra khi hoàn tất tải video lên. Vui lòng thử lại.'], 500);
        }
    }

    public function checkStatus(Request $request)
    {
        $request->validate([
            'guid' => 'required|string',
        ]);

        try {
            $guid = $request->input('guid');

            // Kiểm tra trạng thái video trên Bunny.net
            $response = Http::withHeaders([
                'accept' => 'application/json',
                'AccessKey' => $this->accessKey,
            ])->get($this->apiUrl . $this->libraryId . '/videos/' . $guid);

            if (!$response->successful()) {
                $errorMessage = $this->handleApiError($response, 'kiểm tra trạng thái video');
                return response()->json(['error' => $errorMessage], 400);
            }

            $videoData = $response->json();

            // Trạng thái 4 là đã sẵn sàng, 3 là đang xử lý, các trạng thái khác có thể là lỗi
            if ($videoData['status'] == 4) {
                return response()->json([
                    'status' => 'ready',
                    'message' => 'Video is ready'
                ]);
            } elseif ($videoData['status'] == 3) {
                return response()->json([
                    'status' => 'processing',
                    'progress' => $videoData['encodeProgress'],
                    'message' => 'Video is being processed'
                ]);
            } else {
                return response()->json([
                    'status' => 'unknown',
                    'statusCode' => $videoData['status'],
                    'message' => 'Unknown video status'
                ]);
            }
        } catch (\Exception $e) {
            return response()->json(['error' => 'Có lỗi xảy ra khi kiểm tra trạng thái video. Vui lòng thử lại.'], 500);
        }
    }

    public function getVideoList()
    {
        try {
            $response = Http::withHeaders([
                'accept' => 'application/json',
                'AccessKey' => $this->accessKey,
            ])->get($this->apiUrl . $this->libraryId . '/videos', [
                'page' => 1,
                'itemsPerPage' => 100,
                'orderBy' => 'date'
            ]);

            if (!$response->successful()) {
                $errorMessage = $this->handleApiError($response, 'tải danh sách video');
                return response()->json(['error' => $errorMessage], 400);
            }

            return $response->json();
        } catch (\Exception $e) {
            return response()->json(['error' => 'Có lỗi xảy ra khi tải danh sách video. Vui lòng thử lại.'], 500);
        }
    }

    public function deleteVideo(Request $request)
    {
        $request->validate([
            'guid' => 'required|string',
        ]);

        try {
            $guid = $request->input('guid');

            $response = Http::withHeaders([
                'accept' => 'application/json',
                'AccessKey' => $this->accessKey,
            ])->delete($this->apiUrl . $this->libraryId . '/videos/' . $guid);

            if (!$response->successful()) {
                $errorMessage = $this->handleApiError($response, 'xóa video');
                return response()->json(['error' => $errorMessage], 400);
            }

            return response()->json([
                'success' => true,
                'message' => 'Video deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Có lỗi xảy ra khi xóa video. Vui lòng thử lại.'], 500);
        }
    }

    // Change the getter method to be more Laravel-friendly
    public function getMaxUploadSize()
    {
        // Format the size for display
        if ($this->maxUploadSizeMB >= 1000) {
            return ($this->maxUploadSizeMB / 1000) . ' GB';
        } else {
            return $this->maxUploadSizeMB . ' MB';
        }
    }

    // Add a method to get the raw size in bytes for JavaScript validation
    public function getMaxUploadSizeBytes()
    {
        return $this->maxUploadSizeMB * 1024 * 1024; // Convert MB to bytes
    }

    public function getLibraryStatistics()
    {
        try {
            // Get library statistics from Bunny.net
            $response = Http::withHeaders([
                'accept' => 'application/json',
                'AccessKey' => $this->full_accessKey,
            ])->get('https://api.bunny.net/videolibrary/' . $this->libraryId);

            if (!$response->successful()) {
                $errorMessage = $this->handleApiError($response, 'tải thống kê thư viện');
                return response()->json(['error' => $errorMessage], 400);
            }

            return $response->json();
        } catch (\Exception $e) {
            return response()->json(['error' => 'Có lỗi xảy ra khi tải thống kê thư viện. Vui lòng thử lại.'], 500);
        }
    }
}
