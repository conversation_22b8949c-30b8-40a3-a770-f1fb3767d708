<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\FileUploader;
use App\Models\Lesson;
use App\Models\Quiz;
use App\Models\Section;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class CurriculumController extends Controller
{
    public function store(Request $request)
    {

        $maximum_sort_value = Section::where('course_id', $request->course_id)->orderBy('sort', 'desc')->firstOrNew()->sort;
        $request->validate([
            'title' => 'required',
        ]);

        $section = new Section();
        $section->title = $request->title;
        $section->user_id = auth()->user()->id;
        $section->course_id = $request->course_id;
        $section->sort = $maximum_sort_value + 1;
        $done = $section->save();
        Session::flash('success', get_phrase('Section added successfully'));
        return redirect()->back();
    }

    public function update(Request $request)
    {

        Section::where('id', $request->section_id)->update(['title' => $request->up_title]);
        Session::flash('success', get_phrase('update successfully'));
        return redirect()->back();
    }

    public function delete($id)
    {

        Section::where('id', $id)->delete();
        Session::flash('success', get_phrase('Delete successfully'));
        return redirect()->back();
    }

    public function section_sort(Request $request)
    {
        $sections = json_decode($request->itemJSON);
        foreach ($sections as $key => $value) {
            $updater = $key + 1;
            Section::where('id', $value)->update(['sort' => $updater]);
        }

        Session::flash('success', get_phrase('Sections sorted successfully'));

        return response()->json(['success' => true, 'message' => 'Sections sorted successfully']);
    }

    public function lesson_store(Request $request)
    {

        $maximum_sort_value = Lesson::where('course_id', $request->course_id)->orderBy('sort', 'desc')->firstOrNew()->sort;
        $paid_lesson = $request->paid_lesson == 'on' ? 1 : 0;
        $trial_lesson = $request->trial_lesson == 'on' ? 1 : 0;
        $is_important = $request->is_important == 'on' ? 1 : 0;
        $hide_title = $request->hide_title == 'on' ? 1 : 0;

        $data['paid_lesson'] = $paid_lesson;
        $data['is_important'] = $is_important;
        $data['trial_lesson'] = $trial_lesson;
        $data['hide_title'] = $hide_title;
        $data['title'] = $request->title;
        $data['user_id'] = auth()->user()->id;
        $data['course_id'] = $request->course_id;
        $data['section_id'] = $request->section_id;
        $data['sort'] = $maximum_sort_value + 1;
        $data['is_free'] = $request->free_lesson;
        $data['lesson_type'] = $request->lesson_type;
        $data['summary'] = $request->summary;
        $data['created_at'] = now();
        $data['updated_at'] = now();

        // Process thumbnail upload if exists
        if ($request->hasFile('thumbnail')) {
            $thumbnail = $request->file('thumbnail');
            $thumbnail_name = strtotime('now') . random(4) . '.' . $thumbnail->getClientOriginalExtension();

            $path = public_path('uploads/thumbnails');
            if (!File::isDirectory($path)) {
                File::makeDirectory($path, 0777, true, true);
            }

            // Use the FileUploader class to handle paths consistently
            $upload_path = 'uploads/thumbnails/' . $thumbnail_name;
            FileUploader::upload($thumbnail, $upload_path);
            $data['thumbnail'] = $thumbnail_name;
        }

        // Lưu cấu hình popup
        if ($request->has('popup_config')) {
            // Làm sạch dữ liệu, loại bỏ các mục không có popup_id
            $popupConfigs = collect($request->popup_config)->filter(function ($item) {
                return !empty($item['popup_id']);
            })->values()->toArray();

            // Lưu cấu hình nhiều popup
            $data['popup_config'] = $popupConfigs;

            // Giữ cả giá trị cũ popup_id và popup_time cho tương thích ngược
            if (count($popupConfigs) > 0) {
                $data['popup_id'] = $popupConfigs[0]['popup_id'] ?? null;
                $data['popup_time'] = $popupConfigs[0]['time'] ?? 30;
            } else {
                $data['popup_id'] = null;
                $data['popup_time'] = 30;
            }
        } else {
            $data['popup_config'] = null;
            $data['popup_id'] = null;
            $data['popup_time'] = 30;
        }

        if ($request->lesson_type == 'text') {
            $data['attachment'] = $request->text_description;
            $data['attachment_type'] = $request->lesson_provider;
        } elseif ($request->lesson_type == 'video-url') {

            $data['video_type'] = $request->lesson_provider;
            $data['lesson_src'] = $request->lesson_src;

            if (empty($request->duration)) {
                $data['duration'] = '00:00:00';
            } else {
                $duration_formatter = explode(':', $request->duration);
                $hour = sprintf('%02d', $duration_formatter[0]);
                $min = sprintf('%02d', $duration_formatter[1]);
                $sec = sprintf('%02d', $duration_formatter[2]);
                $data['duration'] = $hour . ':' . $min . ':' . $sec;
            }
        } elseif ($request->lesson_type == 'html5') {

            $data['video_type'] = $request->lesson_provider;
            $data['lesson_src'] = $request->lesson_src;
            if (empty($request->duration)) {
                $data['duration'] = '00:00:00';
            } else {
                $duration_formatter = explode(':', $request->duration);
                $hour = sprintf('%02d', $duration_formatter[0]);
                $min = sprintf('%02d', $duration_formatter[1]);
                $sec = sprintf('%02d', $duration_formatter[2]);
                $data['duration'] = $hour . ':' . $min . ':' . $sec;
            }
        } elseif ($request->lesson_type == 'document_type') {

            if ($request->attachment == '') {
                $file = '';
            } else {
                $item = $request->file('attachment');
                $file_name = strtotime('now') . random(4) . '.' . $item->getClientOriginalExtension();

                $path = public_path('uploads/lesson_file/attachment');
                if (!File::isDirectory($path)) {
                    File::makeDirectory($path, 0777, true, true);
                } else {
                    FileUploader::upload($request->attachment, 'uploads/lesson_file/attachment/' . $file_name);
                }
                $file = $file_name;
            }
            $data['attachment'] = $file;
            $data['attachment_type'] = $request->attachment_type;
        } elseif ($request->lesson_type == 'image') {

            if ($request->attachment == '') {
                $file = '';
            } else {
                $item = $request->file('attachment');
                $file_name = strtotime('now') . random(4) . '.' . $item->getClientOriginalExtension();

                $path = public_path('uploads/lesson_file/attachment');
                if (!File::isDirectory($path)) {
                    File::makeDirectory($path, 0777, true, true);
                } else {
                    FileUploader::upload($request->attachment, 'uploads/lesson_file/attachment/' . $file_name);
                }
                $file = $file_name;
            }
            $data['attachment'] = $file;
            $data['attachment_type'] = $item->getClientOriginalExtension();
        } elseif ($request->lesson_type == 'vimeo-url') {

            $data['video_type'] = $request->lesson_provider;
            $data['lesson_src'] = $request->lesson_src;
            if (empty($request->duration)) {
                $data['duration'] = '00:00:00';
            } else {
                $duration_formatter = explode(':', $request->duration);
                $hour = sprintf('%02d', $duration_formatter[0]);
                $min = sprintf('%02d', $duration_formatter[1]);
                $sec = sprintf('%02d', $duration_formatter[2]);
                $data['duration'] = $hour . ':' . $min . ':' . $sec;
            }
        } elseif ($request->lesson_type == 'iframe') {

            $data['lesson_src'] = $request->iframe_source;
        } elseif ($request->lesson_type == 'google_drive') {

            $data['video_type'] = $request->lesson_provider;
            $data['lesson_src'] = $request->lesson_src;
            if (empty($request->duration)) {
                $data['duration'] = '00:00:00';
            } else {
                $duration_formatter = explode(':', $request->duration);
                $hour = sprintf('%02d', $duration_formatter[0]);
                $min = sprintf('%02d', $duration_formatter[1]);
                $sec = sprintf('%02d', $duration_formatter[2]);
                $data['duration'] = $hour . ':' . $min . ':' . $sec;
            }
        } elseif ($request->lesson_type == 'system-video') {

            if ($request->system_video_file == '') {
                $file = '';
            } else {
                $item = $request->file('system_video_file');
                $file_name = strtotime('now') . random(4) . '.' . $item->getClientOriginalExtension();

                $path = public_path('uploads/lesson_file/videos');
                if (!File::isDirectory($path)) {
                    File::makeDirectory($path, 0777, true, true);
                }

                $type = get_player_settings('watermark_type');
                if ($type == 'ffmpeg') {
                    $watermark = get_player_settings('watermark_logo');
                    if (!$watermark) {
                        return redirect()->back()->with('error', get_phrase('Please configure watermark setting.'));
                    }

                    if (!file_exists(public_path($watermark))) {
                        return redirect()->back()->with('error', get_phrase('File doesn\'t exists.'));
                    }

                    $watermark_status = WatermarkController::encode($item, $file_name, $path);
                    if (!$watermark_status) {
                        return redirect()->back()->with('error', get_phrase('Something went wrong.'));
                    }
                }
                $file = FileUploader::upload($request->system_video_file, 'uploads/lesson_file/videos/' . $file_name);

            }

            $data['video_type'] = $request->lesson_provider;
            $data['lesson_src'] = $file;
            if (empty($request->duration)) {
                $data['duration'] = '00:00:00';
            } else {
                $duration_formatter = explode(':', $request->duration);
                $hour = sprintf('%02d', $duration_formatter[0]);
                $min = sprintf('%02d', $duration_formatter[1]);
                $sec = sprintf('%02d', $duration_formatter[2]);
                $data['duration'] = $hour . ':' . $min . ':' . $sec;
            }
        }

        Lesson::insert($data);
        Session::flash('success', get_phrase('lesson added successfully'));
        return redirect()->back();
    }

    public function lesson_sort(Request $request)
    {
        $lessons = json_decode($request->itemJSON);
        $sectionId = $request->section_id;

        foreach ($lessons as $key => $value) {
            $updater = $key + 1;
            // Only update lessons that belong to the specified section
            Lesson::where('id', $value)
                  ->where('section_id', $sectionId)
                  ->update(['sort' => $updater]);
        }

        Session::flash('success', get_phrase('Lessons sorted successfully'));

        return response()->json(['success' => true, 'message' => 'Lessons sorted successfully']);
    }

    public function lesson_move_section(Request $request)
    {
        $lessonId = $request->lesson_id;
        $newSectionId = $request->section_id;
        $lessonOrder = json_decode($request->lesson_order);

        try {
            // Update lesson's section_id
            Lesson::where('id', $lessonId)->update(['section_id' => $newSectionId]);

            // Update sort order for all lessons in the new section
            foreach ($lessonOrder as $key => $value) {
                $updater = $key + 1;
                Lesson::where('id', $value)->update(['sort' => $updater]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Lesson moved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error moving lesson: ' . $e->getMessage()
            ], 500);
        }
    }

    public function lesson_edit(Request $request)
    {

        $current_data = Lesson::find($request->id);
        $paid_lesson = $request->paid_lesson == 'on' ? 1 : 0;
        $trial_lesson = $request->trial_lesson == 'on' ? 1 : 0;
        $is_important = $request->is_important == 'on' ? 1 : 0;
        $hide_title = $request->hide_title == 'on' ? 1 : 0;
        $lesson['paid_lesson'] = $paid_lesson;
        $lesson['trial_lesson'] = $trial_lesson;
        $lesson['is_important'] = $is_important;
        $lesson['hide_title'] = $hide_title;
        $lesson['title'] = $request->title;
        $lesson['section_id'] = $request->section_id;
        $lesson['summary'] = $request->summary;

        // Process thumbnail upload if exists
        if ($request->hasFile('thumbnail')) {
            // Remove old thumbnail if exists
            if (!empty($current_data->thumbnail)) {
                remove_file('uploads/thumbnails/' . $current_data->thumbnail);
            }

            $thumbnail = $request->file('thumbnail');
            $thumbnail_name = strtotime('now') . random(4) . '.' . $thumbnail->getClientOriginalExtension();

            $path = public_path('uploads/thumbnails');
            if (!File::isDirectory($path)) {
                File::makeDirectory($path, 0777, true, true);
            }

            // Use the FileUploader class to handle paths consistently
            $upload_path = 'uploads/thumbnails/' . $thumbnail_name;
            FileUploader::upload($thumbnail, $upload_path);
            $lesson['thumbnail'] = $thumbnail_name;
        }

        // Lưu cấu hình popup
        if ($request->has('popup_config_disabled')) {
            // Nếu popup đã bị tắt, xóa hết cấu hình
            $lesson['popup_config'] = null;
            $lesson['popup_id'] = null;
            $lesson['popup_time'] = 30;
        } elseif ($request->has('popup_config')) {
            // Làm sạch dữ liệu, loại bỏ các mục không có popup_id
            $popupConfigs = collect($request->popup_config)->filter(function ($item) {
                return !empty($item['popup_id']);
            })->map(function ($item) {
                // Convert time from HH:MM:SS to seconds if seconds not provided directly
                if (isset($item['time']) && !isset($item['time_seconds'])) {
                    // If time is already in seconds (numeric), keep as is
                    if (is_numeric($item['time'])) {
                        $item['time'] = (int)$item['time'];
                    } else {
                        // Parse HH:MM:SS format
                        $timeParts = explode(':', $item['time']);
                        if (count($timeParts) === 3) {
                            $hours = (int)$timeParts[0];
                            $minutes = (int)$timeParts[1];
                            $seconds = (int)$timeParts[2];
                            $item['time'] = $hours * 3600 + $minutes * 60 + $seconds;
                        }
                    }
                } else if (isset($item['time_seconds'])) {
                    // If time_seconds is provided, use that value
                    $item['time'] = (int)$item['time_seconds'];
                    unset($item['time_seconds']);
                }
                return $item;
            })->values()->toArray();

            // Lưu cấu hình nhiều popup
            $lesson['popup_config'] = $popupConfigs;

            // Giữ cả giá trị cũ popup_id và popup_time cho tương thích ngược
            if (count($popupConfigs) > 0) {
                $lesson['popup_id'] = $popupConfigs[0]['popup_id'] ?? null;
                $lesson['popup_time'] = $popupConfigs[0]['time'] ?? 30;
            } else {
                $lesson['popup_id'] = null;
                $lesson['popup_time'] = 30;
            }
        } else {
            // Trường hợp mặc định
            $lesson['popup_config'] = null;
            $lesson['popup_id'] = null;
            $lesson['popup_time'] = 30;
        }

        if ($request->lesson_type == 'text') {
            $lesson['attachment'] = $request->text_description;
        } elseif ($request->lesson_type == 'video-url') {
            $lesson['lesson_src'] = $request->lesson_src;
            if (empty($request->duration)) {
                $lesson['duration'] = '00:00:00';
            } else {
                $duration_formatter = explode(':', $request->duration);
                $hour = sprintf('%02d', $duration_formatter[0]);
                $min = sprintf('%02d', $duration_formatter[1]);
                $sec = sprintf('%02d', $duration_formatter[2]);
                $lesson['duration'] = $hour . ':' . $min . ':' . $sec;
            }
        } elseif ($request->lesson_type == 'html5') {
            $lesson['lesson_src'] = $request->lesson_src;
            if (empty($request->duration)) {
                $lesson['duration'] = '00:00:00';
            } else {
                $duration_formatter = explode(':', $request->duration);
                $hour = sprintf('%02d', $duration_formatter[0]);
                $min = sprintf('%02d', $duration_formatter[1]);
                $sec = sprintf('%02d', $duration_formatter[2]);
                $lesson['duration'] = $hour . ':' . $min . ':' . $sec;
            }
        } elseif ($request->lesson_type == 'document_type') {
            if ($request->attachment) {
                $item = $request->file('attachment');
                $file_name = strtotime('now') . random(4) . '.' . $item->getClientOriginalExtension();

                $path = public_path('uploads/lesson_file/attachment');
                if (!File::isDirectory($path)) {
                    File::makeDirectory($path, 0777, true, true);
                } else {
                    FileUploader::upload($request->attachment, 'uploads/lesson_file/attachment/' . $file_name);
                }
                $lesson['attachment'] = $file_name;
                $lesson['attachment_type'] = $request->attachment_type;

                remove_file('uploads/lesson_file/attachment/' . $current_data->attachment);
            }
        } elseif ($request->lesson_type == 'image') {
            if ($request->attachment) {
                $item = $request->file('attachment');
                $file_name = strtotime('now') . random(4) . '.' . $item->getClientOriginalExtension();

                $path = public_path('uploads/lesson_file/attachment');
                if (!File::isDirectory($path)) {
                    File::makeDirectory($path, 0777, true, true);
                } else {
                    FileUploader::upload($request->attachment, 'uploads/lesson_file/attachment/' . $file_name);
                }
                $lesson['attachment'] = $file_name;
                $lesson['attachment_type'] = $item->getClientOriginalExtension();
                remove_file('uploads/lesson_file/attachment/' . $current_data->attachment);
            }
        } elseif ($request->lesson_type == 'vimeo-url') {
            $lesson['lesson_src'] = $request->lesson_src;
            if (empty($request->duration)) {
                $lesson['duration'] = '00:00:00';
            } else {
                $duration_formatter = explode(':', $request->duration);
                $hour = sprintf('%02d', $duration_formatter[0]);
                $min = sprintf('%02d', $duration_formatter[1]);
                $sec = sprintf('%02d', $duration_formatter[2]);
                $lesson['duration'] = $hour . ':' . $min . ':' . $sec;
            }
        } elseif ($request->lesson_type == 'iframe') {
            $lesson['lesson_src'] = $request->iframe_source;
        } elseif ($request->lesson_type == 'google_drive') {
            $lesson['lesson_src'] = $request->lesson_src;
            if (empty($request->duration)) {
                $lesson['duration'] = '00:00:00';
            } else {
                $duration_formatter = explode(':', $request->duration);
                $hour = sprintf('%02d', $duration_formatter[0]);
                $min = sprintf('%02d', $duration_formatter[1]);
                $sec = sprintf('%02d', $duration_formatter[2]);
                $lesson['duration'] = $hour . ':' . $min . ':' . $sec;
            }
        } elseif ($request->lesson_type == 'system-video') {

            if ($request->system_video_file) {
                $item = $request->file('system_video_file');
                $file_name = strtotime('now') . random(4) . '.' . $item->getClientOriginalExtension();

                $path = public_path('uploads/lesson_file/videos');
                if (!File::isDirectory($path)) {
                    File::makeDirectory($path, 0777, true, true);
                }

                $type = get_player_settings('watermark_type');
                if ($type == 'ffmpeg') {
                    $watermark = get_player_settings('watermark_logo');
                    if (!$watermark) {
                        return redirect()->back()->with('error', get_phrase('Please configure watermark setting.'));
                    }

                    if (!file_exists(public_path($watermark))) {
                        return redirect()->back()->with('error', get_phrase('File doesn\'t exists.'));
                    }

                    $watermark_status = WatermarkController::encode($item, $file_name, $path);
                    if (!$watermark_status) {
                        return redirect()->back()->with('error', get_phrase('Something went wrong.'));
                    }
                }
                FileUploader::upload($request->system_video_file, 'uploads/lesson_file/videos/' . $file_name);

                $file = str_replace(public_path('/'), '', $path) . '/' . $file_name;
                $lesson['lesson_src'] = $file;
                remove_file($current_data->lesson_src);
            }

            if (empty($request->duration)) {
                $lesson['duration'] = '00:00:00';
            } else {
                $duration_formatter = explode(':', $request->duration);
                $hour = sprintf('%02d', $duration_formatter[0]);
                $min = sprintf('%02d', $duration_formatter[1]);
                $sec = sprintf('%02d', $duration_formatter[2]);
                $lesson['duration'] = $hour . ':' . $min . ':' . $sec;
            }
        }

        Lesson::where('id', $request->id)->update($lesson);

        Session::flash('success', get_phrase('lesson update successfully'));
        return redirect()->back();
    }

    public function lesson_delete($id)
    {
        $current_data = Lesson::find($id);
        remove_file($current_data->lesson_src);
        remove_file('uploads/lesson_file/attachment/' . $current_data->attachment);

        // Remove thumbnail if exists
        if (!empty($current_data->thumbnail)) {
            remove_file('uploads/thumbnails/' . $current_data->thumbnail);
        }

        Lesson::where('id', $id)->delete();
        Session::flash('success', get_phrase('Delete successfully'));
        return redirect()->back();
    }

    /**
     * Get curriculum data for a specific course (for AJAX)
     */
    public function getCurriculum($courseId)
    {
        try {
            $sections = Section::where('course_id', $courseId)
                ->orderBy('sort', 'asc')
                ->with(['lessons' => function($query) {
                    $query->orderBy('sort', 'asc');
                }])
                ->get();

            return response()->json([
                'success' => true,
                'curriculum' => $sections
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Không thể tải nội dung khóa học'
            ]);
        }
    }

    /**
     * Generate AI outline for course
     */
    public function generateAiOutline(Request $request)
    {
        try {
            // Debug log
            \Log::info('generateAiOutline called', $request->all());

            $request->validate([
                'course_description' => 'required|string|min:10',
                'target_audience' => 'required|string',
                'course_duration' => 'required|string',
                'course_id' => 'required|exists:courses,id'
            ]);

            // Create professional prompt for AI
            $prompt = $this->createAiPrompt(
                $request->course_description,
                $request->target_audience,
                $request->course_duration,
                $request->course_goals
            );

            // Call OpenAI API
            $outline = $this->callOpenAiApi($prompt);

            if (!$outline) {
                // Return sample outline for testing
                $outline = [
                    [
                        'title' => 'Giới thiệu về ' . substr($request->course_description, 0, 30) . '...',
                        'lessons' => [
                            'Tổng quan và mục tiêu khóa học',
                            'Chuẩn bị kiến thức cơ bản',
                            'Thiết lập môi trường học tập'
                        ]
                    ],
                    [
                        'title' => 'Kiến thức cơ bản',
                        'lessons' => [
                            'Khái niệm và thuật ngữ quan trọng',
                            'Nguyên lý hoạt động',
                            'Các công cụ cần thiết'
                        ]
                    ],
                    [
                        'title' => 'Thực hành và ứng dụng',
                        'lessons' => [
                            'Bài tập thực hành đầu tiên',
                            'Dự án thực tế',
                            'Tổng kết và đánh giá'
                        ]
                    ]
                ];
            }

            return response()->json([
                'success' => true,
                'outline' => $outline
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('Validation error in generateAiOutline', ['errors' => $e->errors()]);
            return response()->json([
                'success' => false,
                'message' => 'Dữ liệu không hợp lệ: ' . implode(', ', $e->validator->errors()->all())
            ], 422);
        } catch (\Exception $e) {
            \Log::error('Error in generateAiOutline', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test method for AI functionality
     */
    public function testAi(Request $request)
    {
        return response()->json([
            'success' => true,
            'message' => 'AI routes are working!',
            'data' => $request->all()
        ]);
    }

    /**
     * Add AI generated outline to course
     */
    public function addAiOutline(Request $request)
    {
        try {
            $request->validate([
                'course_id' => 'required|exists:courses,id',
                'outline_data' => 'required|string'
            ]);

            $outlineData = json_decode($request->outline_data, true);

            if (!$outlineData || !is_array($outlineData)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Dữ liệu outline không hợp lệ'
                ]);
            }

            $courseId = $request->course_id;
            $userId = auth()->user()->id;

            // Get current max sort value for sections
            $maxSectionSort = Section::where('course_id', $courseId)->max('sort') ?? 0;

            foreach ($outlineData as $sectionData) {
                if (empty($sectionData['title']) || empty($sectionData['lessons'])) {
                    continue;
                }

                // Create section
                $section = new Section();
                $section->title = $sectionData['title'];
                $section->user_id = $userId;
                $section->course_id = $courseId;
                $section->sort = ++$maxSectionSort;
                $section->save();

                // Get current max sort value for lessons in this course
                $maxLessonSort = Lesson::where('course_id', $courseId)->max('sort') ?? 0;

                // Create lessons for this section
                foreach ($sectionData['lessons'] as $lessonTitle) {
                    if (empty($lessonTitle)) {
                        continue;
                    }

                    $lesson = new Lesson();
                    $lesson->title = $lessonTitle;
                    $lesson->user_id = $userId;
                    $lesson->course_id = $courseId;
                    $lesson->section_id = $section->id;
                    $lesson->lesson_type = 'video'; // Default type
                    $lesson->is_free = 0;
                    $lesson->sort = ++$maxLessonSort;
                    $lesson->save();
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Đã thêm outline thành công!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Copy lessons from another course
     */
    public function copyLessonsFromCourse(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'source_course_id' => 'required|exists:courses,id',
                'target_course_id' => 'required|exists:courses,id',
                'sections' => 'required|array',
                'sections.*' => 'exists:sections,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Dữ liệu không hợp lệ'
                ]);
            }

            $sourceCourseId = $request->source_course_id;
            $targetCourseId = $request->target_course_id;
            $selectedSections = $request->sections ?? [];
            $selectedLessons = $request->lessons ?? [];

            // Get maximum sort values for target course
            $maxSectionSort = Section::where('course_id', $targetCourseId)->max('sort') ?? 0;
            $maxLessonSort = Lesson::where('course_id', $targetCourseId)->max('sort') ?? 0;

            $copiedSections = 0;
            $copiedLessons = 0;

            foreach ($selectedSections as $sectionId) {
                // Get source section
                $sourceSection = Section::find($sectionId);
                if (!$sourceSection || $sourceSection->course_id != $sourceCourseId) {
                    continue;
                }

                // Check if section with same name already exists in target course
                $existingSection = Section::where('course_id', $targetCourseId)
                    ->where('title', $sourceSection->title)
                    ->first();

                if ($existingSection) {
                    // Use existing section
                    $targetSection = $existingSection;
                } else {
                    // Create new section
                    $targetSection = new Section();
                    $targetSection->title = $sourceSection->title;
                    $targetSection->user_id = auth()->user()->id;
                    $targetSection->course_id = $targetCourseId;
                    $targetSection->sort = ++$maxSectionSort;
                    $targetSection->save();
                    $copiedSections++;
                }

                // Copy lessons for this section
                $sourceLessons = Lesson::where('section_id', $sectionId)
                    ->whereIn('id', $selectedLessons)
                    ->orderBy('sort', 'asc')
                    ->get();

                foreach ($sourceLessons as $sourceLesson) {
                    // Check if lesson with same name already exists in target section
                    $existingLesson = Lesson::where('section_id', $targetSection->id)
                        ->where('title', $sourceLesson->title)
                        ->first();

                    if ($existingLesson) {
                        continue; // Skip if lesson already exists
                    }

                    // Create new lesson
                    $newLesson = new Lesson();
                    $newLesson->title = $sourceLesson->title;
                    $newLesson->user_id = auth()->user()->id;
                    $newLesson->course_id = $targetCourseId;
                    $newLesson->section_id = $targetSection->id;
                    $newLesson->lesson_type = $sourceLesson->lesson_type;
                    $newLesson->duration = $sourceLesson->duration;
                    $newLesson->lesson_src = $sourceLesson->lesson_src;
                    $newLesson->attachment = $sourceLesson->attachment;
                    $newLesson->attachment_type = $sourceLesson->attachment_type;
                    $newLesson->video_type = $sourceLesson->video_type;
                    $newLesson->thumbnail = $sourceLesson->thumbnail;
                    $newLesson->is_free = $sourceLesson->is_free;
                    $newLesson->sort = ++$maxLessonSort;
                    $newLesson->description = $sourceLesson->description;
                    $newLesson->summary = $sourceLesson->summary;
                    $newLesson->status = $sourceLesson->status;
                    $newLesson->total_mark = $sourceLesson->total_mark;
                    $newLesson->pass_mark = $sourceLesson->pass_mark;
                    $newLesson->retake = $sourceLesson->retake;
                    $newLesson->paid_lesson = $sourceLesson->paid_lesson;
                    $newLesson->trial_lesson = $sourceLesson->trial_lesson;
                    $newLesson->is_important = $sourceLesson->is_important;
                    $newLesson->hide_title = $sourceLesson->hide_title;
                    $newLesson->popup_id = $sourceLesson->popup_id;
                    $newLesson->popup_time = $sourceLesson->popup_time;
                    $newLesson->popup_config = $sourceLesson->popup_config;
                    $newLesson->save();
                    $copiedLessons++;
                }
            }

            return response()->json([
                'success' => true,
                'message' => "Đã sao chép thành công {$copiedSections} sections và {$copiedLessons} bài học"
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi sao chép: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Create professional prompt for AI
     */
    private function createAiPrompt($description, $targetAudience, $duration, $goals = null)
    {
        $audienceMap = [
            'beginner' => 'người mới bắt đầu, chưa có kinh nghiệm',
            'intermediate' => 'người có kinh nghiệm trung bình',
            'advanced' => 'người có kinh nghiệm cao, chuyên sâu',
            'all' => 'tất cả các trình độ từ cơ bản đến nâng cao'
        ];

        $durationMap = [
            'short' => '5-10 bài học, tập trung vào những kiến thức cốt lõi nhất',
            'medium' => '15-25 bài học, bao quát đầy đủ các chủ đề chính',
            'long' => '30+ bài học, chi tiết và toàn diện từ cơ bản đến nâng cao'
        ];

        $audienceText = $audienceMap[$targetAudience] ?? 'người học';
        $durationText = $durationMap[$duration] ?? 'phù hợp với nội dung';

        $prompt = "Bạn là một chuyên gia thiết kế khóa học e-learning chuyên nghiệp. Hãy tạo một outline chi tiết cho khóa học sau:

**Mô tả khóa học:** {$description}

**Đối tượng học viên:** {$audienceText}

**Thời lượng:** {$durationText}";

        if ($goals) {
            $prompt .= "\n\n**Mục tiêu khóa học:** {$goals}";
        }

        $prompt .= "\n\n**Yêu cầu:**
1. Tạo outline có cấu trúc rõ ràng với các sections (chương) và lessons (bài học)
2. Mỗi section nên có 3-8 bài học
3. Tên bài học phải cụ thể, hấp dẫn và dễ hiểu
4. Sắp xếp theo trình tự logic từ cơ bản đến nâng cao
5. Phù hợp với đối tượng học viên và thời lượng đã chọn

**QUAN TRỌNG:** Chỉ trả về JSON array với format chính xác sau đây, không thêm bất kỳ text nào khác:

[
  {
    \"title\": \"Tên Section 1\",
    \"lessons\": [
      \"Tên bài học 1\",
      \"Tên bài học 2\",
      \"Tên bài học 3\"
    ]
  },
  {
    \"title\": \"Tên Section 2\",
    \"lessons\": [
      \"Tên bài học 4\",
      \"Tên bài học 5\"
    ]
  }
]";

        return $prompt;
    }

    /**
     * Call OpenAI API
     */
    private function callOpenAiApi($prompt)
    {
        try {
            $ch = curl_init();

            curl_setopt_array($ch, [
                CURLOPT_URL => 'https://api.v3.cm/v1/chat/completions',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_POST => true,
                CURLOPT_HTTPHEADER => [
                    'Authorization: Bearer sk-MJ40iVOjgpDUTemzF7A41d1aC03b42Ea917f6c39Cf1f955e',
                    'Content-Type: application/json'
                ],
                CURLOPT_POSTFIELDS => json_encode([
                    'max_tokens' => 3980,
                    'model' => 'gpt-4o-mini',
                    'temperature' => 0.7,
                    'top_p' => 1,
                    'presence_penalty' => 0,
                    'frequency_penalty' => 0,
                    'messages' => [
                        [
                            'content' => $prompt,
                            'role' => 'user'
                        ]
                    ],
                    'stream' => false
                ]),
                CURLOPT_TIMEOUT => 60,
                CURLOPT_SSL_VERIFYPEER => false
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            if (curl_errno($ch)) {
                curl_close($ch);
                return null;
            }

            curl_close($ch);

            if ($httpCode !== 200) {
                return null;
            }

            $data = json_decode($response, true);

            if (!isset($data['choices'][0]['message']['content'])) {
                return null;
            }

            $content = trim($data['choices'][0]['message']['content']);

            // Remove markdown code blocks if present
            $content = preg_replace('/^```json\s*/', '', $content);
            $content = preg_replace('/\s*```$/', '', $content);

            // Remove any text before the JSON array
            $content = preg_replace('/^.*?(\[.*\]).*$/s', '$1', $content);

            $outline = json_decode($content, true);

            if (!$outline || !is_array($outline)) {
                return null;
            }

            // Validate structure
            foreach ($outline as $section) {
                if (!isset($section['title']) || !isset($section['lessons']) || !is_array($section['lessons'])) {
                    return null;
                }
            }

            return $outline;

        } catch (\Exception $e) {
            return null;
        }
    }
}
