<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\Enrollment;
use App\Models\Lesson;
use App\Models\OfflinePayment;
use App\Models\Payment_history;
use App\Models\User;
use App\Models\Withdraw;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DashboardController extends Controller
{
    public function index()
    {
        // Lấy số liệu thống kê tổng quan
        $statistics = $this->getStatistics();

        // Lấy doanh thu theo tháng
        $monthly_amount = $this->getMonthlyRevenue();

        // L<PERSON>y danh sách rút tiền đang chờ
        $pending_withdraws = $this->getPendingWithdraws();

        // L<PERSON>y thông tin khóa học theo trạng thái
        $course_statistics = $this->getCourseStatistics();

        // Lấy top affiliate theo doanh thu và số đơn hàng
        $top_affiliates = [];
        if (function_exists('addon_check') && addon_check('my.affiliate')) {
            $top_affiliates = $this->getTopAffiliates();
        }

        // Force clear và recheck system expiry để tránh cache cũ
        session()->forget(['expiry_date', 'system_expired']);

        $expiry_date = null;
        $system_expired = false;

        // Force check system expiry
        if (env('SYSTEM_EXPIRY_DATE')) {
            $expiryDateString = env('SYSTEM_EXPIRY_DATE');
            try {
                $expiryDate = \Carbon\Carbon::createFromFormat('d/m/Y', $expiryDateString)->endOfDay();
                $currentDate = \Carbon\Carbon::now();
                $isExpired = $currentDate->gt($expiryDate);

                session(['system_expired' => $isExpired, 'expiry_date' => $expiryDateString]);
                $expiry_date = $expiryDateString;
                $system_expired = $isExpired;


            } catch (\Exception $e) {
                // Ignore parsing errors
            }
        }

        $page_data = [
            'statistics' => $statistics,
            'monthly_amount' => $monthly_amount,
            'pending_withdraws' => $pending_withdraws,
            'course_statistics' => $course_statistics,
            'top_affiliates_amount' => $top_affiliates['by_amount'] ?? collect(),
            'top_affiliates_orders' => $top_affiliates['by_orders'] ?? collect(),
            'expiry_date' => $expiry_date,
            'system_expired' => $system_expired,
        ];

        return view('admin.dashboard.index', $page_data);
    }

    /**
     * Clear storage cache and recalculate
     */
    public function clearStorageCache()
    {
        try {
            Cache::forget('dashboard_storage_usage');
            Cache::forget('dashboard_statistics');

            // Force recalculate
            $this->getStatistics();

            return response()->json([
                'success' => true,
                'message' => 'Cache dung lượng đã được xóa và tính lại thành công'
            ]);
        } catch (\Exception $e) {
            Log::error('Error clearing storage cache: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi khi xóa cache: ' . $e->getMessage()
            ], 500);
        }
    }




    private function getStatistics(): array
    {

        return Cache::remember('dashboard_statistics', 3600, function () {
            // Cache storage usage separately with longer duration since it changes less frequently
            $storageUsage = Cache::remember('dashboard_storage_usage', 7200, function () {
                return $this->calculateStorageUsage();
            });

            // Ensure storage usage is always an array
            if (!is_array($storageUsage) || empty($storageUsage)) {
                $storageUsage = [
                    'project_size' => 0,
                    'database_size' => 0,
                    'total_size' => 0,
                    'formatted_total' => '0 B',
                    'formatted_project' => '0 B',
                    'formatted_database' => '0 B',
                ];
            }

            return [
                'course_count' => Course::count(),
                'lesson_count' => Lesson::count(),
                'enrollment_count' => Enrollment::count(),
                'student_count' => User::where('role', 'student')->count(),
                'instructor_count' => User::where('role', 'instructor')->count(),
                'storage_usage' => $storageUsage,
            ];
        });
    }

    /**
     * Tính dung lượng sử dụng (code + database)
     */
    private function calculateStorageUsage(): array
    {
        $defaultResult = [
            'project_size' => 0,
            'database_size' => 0,
            'total_size' => 0,
            'formatted_total' => '0 B',
            'formatted_project' => '0 B',
            'formatted_database' => '0 B',
        ];

        try {
            // Tính dung lượng thư mục dự án
            $projectSize = 0;
            try {
                $projectSize = $this->getDirectorySize(base_path());
            } catch (\Exception $e) {
                Log::warning('Could not calculate project size: ' . $e->getMessage());
            }

            // Tính dung lượng database MySQL
            $databaseSize = 0;
            try {
                $databaseSize = $this->getDatabaseSize();
            } catch (\Exception $e) {
                Log::warning('Could not calculate database size: ' . $e->getMessage());
            }

            // Tổng dung lượng
            $totalSize = $projectSize + $databaseSize;

            return [
                'project_size' => $projectSize,
                'database_size' => $databaseSize,
                'total_size' => $totalSize,
                'formatted_total' => $this->formatBytes($totalSize),
                'formatted_project' => $this->formatBytes($projectSize),
                'formatted_database' => $this->formatBytes($databaseSize),
            ];
        } catch (\Exception $e) {
            Log::error('Error calculating storage usage: ' . $e->getMessage());
            return $defaultResult;
        }
    }

    /**
     * Tính dung lượng thư mục
     */
    private function getDirectorySize($directory): int
    {
        $size = 0;

        if (!is_dir($directory)) {
            return 0;
        }

        // Danh sách thư mục bỏ qua để tăng tốc độ
        $excludeDirs = [
            'node_modules',
            '.git',
            'vendor/composer',
            'storage/logs',
            'storage/framework/cache',
            'storage/framework/sessions',
            'storage/framework/views',
        ];

        try {
            $iterator = new \RecursiveIteratorIterator(
                new \RecursiveDirectoryIterator($directory, \RecursiveDirectoryIterator::SKIP_DOTS)
            );

            foreach ($iterator as $file) {
                // Bỏ qua các thư mục không cần thiết
                $relativePath = str_replace($directory . DIRECTORY_SEPARATOR, '', $file->getPathname());
                $shouldSkip = false;

                foreach ($excludeDirs as $excludeDir) {
                    if (strpos($relativePath, $excludeDir) === 0) {
                        $shouldSkip = true;
                        break;
                    }
                }

                if (!$shouldSkip && $file->isFile()) {
                    $size += $file->getSize();
                }
            }
        } catch (\Exception $e) {
            Log::error('Error calculating directory size: ' . $e->getMessage());
        }

        return $size;
    }

    /**
     * Tính dung lượng database MySQL
     */
    private function getDatabaseSize(): int
    {
        try {
            $databaseName = config('database.connections.mysql.database');

            $result = DB::select("
                SELECT
                    SUM(data_length + index_length) as size_bytes
                FROM information_schema.tables
                WHERE table_schema = ?
            ", [$databaseName]);

            return $result[0]->size_bytes ?? 0;
        } catch (\Exception $e) {
            Log::error('Error calculating database size: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Format bytes thành đơn vị dễ đọc (ưu tiên MB/GB)
     */
    private function formatBytes($bytes, $precision = 1): string
    {
        if ($bytes == 0) {
            return '0 B';
        }

        // Chuyển đổi thành MB
        $mb = $bytes / (1024 * 1024);

        // Nếu >= 1024 MB thì chuyển thành GB
        if ($mb >= 1024) {
            $gb = $mb / 1024;
            return round($gb, $precision) . ' GB';
        }

        // Nếu >= 1 MB thì hiển thị MB
        if ($mb >= 1) {
            return round($mb, $precision) . ' MB';
        }

        // Nếu < 1 MB thì hiển thị KB
        $kb = $bytes / 1024;
        if ($kb >= 1) {
            return round($kb, $precision) . ' KB';
        }

        // Nếu < 1 KB thì hiển thị bytes
        return $bytes . ' B';
    }


    private function getMonthlyRevenue(): array
    {
        // Lấy năm hiện tại
        $current_year = date('Y');

        // Tạo mảng chứa các tháng trong năm để query một lần
        $months = [];
        for ($i = 1; $i <= 12; $i++) {
            $month = str_pad($i, 2, '0', STR_PAD_LEFT);
            $start_date = "$current_year-$month-01 00:00:00";
            $end_date = date('Y-m-t 23:59:59', strtotime($start_date));

            $months[$i] = [
                'start' => $start_date,
                'end' => $end_date
            ];
        }

        // Mảng kết quả với cấu trúc mới
        $result = [
            'labels' => ["", "Tháng 1", "Tháng 2", "Tháng 3", "Tháng 4", "Tháng 5", "Tháng 6",
                         "Tháng 7", "Tháng 8", "Tháng 9", "Tháng 10", "Tháng 11", "Tháng 12"],
            'total_revenue' => [0], // Bắt đầu với index 0 trống
            'affiliate_amount' => [0], // Bắt đầu với index 0 trống
            'actual_revenue' => [0]  // Doanh thu thực tế = total_revenue - affiliate_amount
        ];

        // Query tổng doanh thu theo từng tháng
        foreach ($months as $month_num => $period) {
            // Query các giao dịch trong tháng
            $transactions = OfflinePayment::whereBetween('created_at', [$period['start'], $period['end']])
                ->where('status', 1)
                ->select('total_amount', 'affiliate_amount')
                ->get();

            // Tính tổng doanh thu và hoa hồng affiliate
            $total_revenue = 0;
            $total_affiliate = 0;

            foreach ($transactions as $transaction) {
                $total_revenue += (float) $transaction->total_amount;
                $total_affiliate += (float) ($transaction->affiliate_amount ?: 0);
            }

            // Tính doanh thu thực (sau khi trừ hoa hồng)
            $actual_revenue = $total_revenue - $total_affiliate;

            // Lưu vào mảng kết quả
            $result['total_revenue'][$month_num] = $total_revenue;
            $result['affiliate_amount'][$month_num] = $total_affiliate;
            $result['actual_revenue'][$month_num] = $actual_revenue;
        }

        return $result;
    }


    private function getPendingWithdraws()
    {
        return Withdraw::where('status', '0')
            ->with([
                "user" => function ($query) {
                    $query->select('id', 'name', 'email', 'photo');
                }
            ])
            ->orderBy('created_at', 'DESC')
            ->paginate(10);
    }


    private function getCourseStatistics(): array
    {

        return Cache::remember('course_statistics', 3600, function () {
            $courses = Course::select('status')
                ->get()
                ->groupBy('status');

            return [
                'active' => $courses->get('active')?->count() ?? 0,
                'upcoming' => $courses->get('upcoming')?->count() ?? 0,
                'pending' => $courses->get('pending')?->count() ?? 0,
                'private' => $courses->get('private')?->count() ?? 0,
                'draft' => $courses->get('draft')?->count() ?? 0,
                'inactive' => $courses->get('inactive')?->count() ?? 0,
            ];
        });
    }


    private function getTopAffiliates(): array
    {

        $by_amount = OfflinePayment::select('affiliate_id')
            ->selectRaw('SUM(affiliate_amount) as total_amount, COUNT(id) as total_orders')
            ->where('status', 1)
            ->whereNotNull('affiliate_id')
            ->groupBy('affiliate_id')
            ->orderByDesc('total_amount')
            ->limit(10)
            ->get();

        // Lấy top 10 affiliate theo số đơn hàng
        $by_orders = OfflinePayment::select('affiliate_id')
            ->selectRaw('COUNT(id) as total_orders, SUM(affiliate_amount) as total_amount')
            ->where('status', 1)
            ->whereNotNull('affiliate_id')
            ->groupBy('affiliate_id')
            ->orderByDesc('total_orders')
            ->limit(10)
            ->get();

        // Lấy thông tin người dùng cho các affiliate
        $affiliate_ids = $by_amount->pluck('affiliate_id')
            ->merge($by_orders->pluck('affiliate_id'))
            ->unique()
            ->values()
            ->toArray();

        $users = User::whereIn('id', $affiliate_ids)
            ->select('id', 'name')
            ->get()
            ->keyBy('id');


        $by_amount->each(function ($item) use ($users) {
            $item->user = $users->get($item->affiliate_id);
        });

        $by_orders->each(function ($item) use ($users) {
            $item->user = $users->get($item->affiliate_id);
        });

        return [
            'by_amount' => $by_amount,
            'by_orders' => $by_orders
        ];
    }

    /**
     * API endpoint for real-time dashboard stats
     */
    public function getStatsApi()
    {
        try {
            $statistics = $this->getStatistics();
            $monthly_amount = $this->getMonthlyRevenue();
            $course_statistics = $this->getCourseStatistics();

            // Get recent activities (last 10)
            $recent_activities = [];

            // Get notifications
            $notifications = [];

            return response()->json([
                'success' => true,
                'data' => [
                    'statistics' => $statistics,
                    'chartData' => [
                        'monthly_amount' => $monthly_amount
                    ],
                    'course_statistics' => $course_statistics,
                    'activities' => $recent_activities,
                    'notifications' => $notifications,
                    'storage_usage' => $statistics['storage_usage'] ?? null,
                    'timestamp' => now()->toISOString()
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Không thể lấy dữ liệu dashboard',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
