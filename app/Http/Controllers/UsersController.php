<?php

namespace App\Http\Controllers;

use App\Models\Application;
use App\Models\Course;
use App\Models\Enrollment;
use App\Models\FileUploader;
use App\Models\OfflinePayment;
use App\Models\Payout;
use App\Models\Permission;
use App\Models\Setting;
use App\Models\Message;
use App\Models\MessageThread;
use App\Models\User;
use App\Models\WatchDuration;
use App\Models\Watch_history;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Cache;

class UsersController extends Controller
{

    public function admin_index(Request $request)
    {
        $query = User::where('role', 'admin');
        if (isset($_GET['search']) && $_GET['search'] != '') {
            $search = $_GET['search'];
            $query = $query->where(function($q) use ($search) {
                $q->where('name', 'LIKE', '%' . $search . '%')
                  ->orWhere('email', 'LIKE', '%' . $search . '%')
                  ->orWhere('phone', 'LIKE', '%' . $search . '%');
            });
        }
        $page_data['admins'] = $query->paginate(10);
        return view('admin.admin.index', $page_data);
    }

    public function admin_create()
    {
        return view('admin.admin.create_admin');
    }

    public function admin_store(Request $request)
    {

        $validated = $request->validate([
            'name' => "required",
            'email' => 'required|email|unique:users',
            'password' => "required|min:8",
        ]);

        $data['name'] = $request->name;
        $data['about'] = $request->about;
        $data['phone'] = $request->phone;
        $data['address'] = $request->address;
        $data['email'] = $request->email;
        $data['password'] = Hash::make($request->password);
        $data['facebook'] = $request->facebook;
        $data['twitter'] = $request->twitter;
        $data['website'] = $request->website;
        $data['linkedin'] = $request->linkedin;
        $data['role'] = 'admin';
        $data['status'] = '1';

        if (isset($request->photo) && $request->hasFile('photo')) {
            $path = "uploads/users/instructor/" . nice_file_name($request->name, $request->photo->extension());
            FileUploader::upload($request->photo, $path, 400, null, 200, 200);
            $data['photo'] = $path;
        }

        $done = User::insert($data);

        if ($done) {
            $admin_id = User::latest('id')->first();
            Permission::insert(['admin_id' => $admin_id->id]);
        }
        Session::flash('success', get_phrase('Admin add successfully'));
        return redirect()->route('admin.admins.index');
    }

    public function admin_edit($id)
    {
        $page_data['admin'] = User::where('id', $id)->first();
        return view('admin.admin.edit_admin', $page_data);
    }

    public function admin_update(Request $request, $id)
    {
        $validated = $request->validate([
            'name' => 'required|max:255',
            'email' => "required|email|unique:users,email,$id",
        ]);

        $data['name'] = $request->name;
        $data['about'] = $request->about;
        $data['phone'] = $request->phone;
        $data['address'] = $request->address;
        $data['email'] = $request->email;
        $data['facebook'] = $request->facebook;
        $data['twitter'] = $request->twitter;
        $data['website'] = $request->website;
        $data['linkedin'] = $request->linkedin;

        // Update password if provided
        if ($request->filled('password')) {
            $data['password'] = Hash::make($request->password);
        }

        if (isset($request->photo) && $request->hasFile('photo')) {
            remove_file(User::where('id', $id)->first()->photo);
            $path = "uploads/users/instructor/" . nice_file_name($request->name, $request->photo->extension());
            FileUploader::upload($request->photo, $path, 400, null, 200, 200);
            $data['photo'] = $path;
        }

        User::where('id', $request->id)->update($data);
        Session::flash('success', get_phrase('Admin update successfully'));
        return redirect()->route('admin.admins.index');
    }

    public function admin_delete($id)
    {
        $threads = MessageThread::where('contact_one', $id)
            ->orWhere('contact_two', $id)
            ->pluck('id');

        if ($threads->isNotEmpty()) {
            Message::whereIn('thread_id', $threads)->delete();
            MessageThread::whereIn('id', $threads)->delete();
        }

        $done = User::where('id', $id)->delete();
        if ($done) {
            Permission::where('admin_id', $id)->delete();
        }
        Session::flash('success', get_phrase('Admin delete successfully'));
        return redirect()->back();
    }

    public function admin_permission($user_id)
    {
        $page_data['admin'] = User::where('id', $user_id)->firstOrNew();
        return view('admin.admin.permission', $page_data);
    }

    public function admin_permission_store(Request $request)
    {
        $user_id = $request->user_id;

        $permission = Permission::where('admin_id', $user_id)->first();

        if ($permission) {
            $set_permission = json_decode($permission->permissions, true) ?? [];
            if (in_array($request->permission, $set_permission)) {
                $pos = array_search($request->permission, $set_permission);
                array_splice($set_permission, $pos, 1);
            } else {
                array_push($set_permission, $request->permission);
            }
            Permission::where('admin_id', $user_id)->update(['permissions' => $set_permission]);
            return true;
        } else {
            $set_per = json_encode([$request->permission]);
            Permission::insert(['admin_id' => $user_id, 'permissions' => $set_per]);
            return true;
        }
    }

    public function instructor_index()
    {
        $query = User::where('role', 'instructor');
        if (isset($_GET['search']) && $_GET['search'] != '') {
            $search = $_GET['search'];
            $query = $query->where(function($q) use ($search) {
                $q->where('name', 'LIKE', '%' . $search . '%')
                  ->orWhere('email', 'LIKE', '%' . $search . '%')
                  ->orWhere('phone', 'LIKE', '%' . $search . '%');
            });
        }
        $page_data['instructors'] = $query->paginate(10);
        return view('admin.instructor.index', $page_data);
    }

    public function instructor_create()
    {
        return view('admin.instructor.create_instructor');
    }

    public function instructor_edit($id = '')
    {
        $page_data['instructor'] = User::where('id', $id)->first();
        return view('admin.instructor.edit_instructor', $page_data);
    }

    public function instructor_store(Request $request, $id = '')
    {
        $validated = $request->validate([
            'name' => "required|max:255",
            'email' => 'required|email|unique:users',
            'password' => "required|min:8",
        ]);

        if (get_settings('student_email_verification') != 1) {
            $data['email_verified_at'] = date('Y-m-d H:i:s');
        }

        $data['name'] = $request->name;
        $data['about'] = $request->about;
        $data['phone'] = $request->phone;
        $data['address'] = $request->address;
        $data['email'] = $request->email;
        $data['facebook'] = $request->facebook;
        $data['twitter'] = $request->twitter;
        $data['website'] = $request->website;
        $data['linkedin'] = $request->linkedin;
        $data['paymentkeys'] = json_encode($request->paymentkeys);
        $data['status'] = '1';

        $data['password'] = Hash::make($request->password);
        $data['role'] = 'instructor';

        if (isset($request->photo) && $request->hasFile('photo')) {
            $path = "uploads/users/instructor/" . nice_file_name($request->name, $request->photo->extension());
            FileUploader::upload($request->photo, $path, 400, null, 200, 200);
            $data['photo'] = $path;
        }
        $user = User::create($data);

        if (get_settings('student_email_verification') == 1) {
            $user->sendEmailVerificationNotification();
        }

        Session::flash('success', get_phrase('Instructor add successfully'));

        return redirect()->route('admin.instructor.index');
    }

    public function instructor_update(Request $request, $id = '')
    {

        $validated = $request->validate([
            'name' => 'required|max:255',
            'email' => "required|email|unique:users,email,$id",
        ]);

        $data['name'] = $request->name;
        $data['about'] = $request->about;
        $data['phone'] = $request->phone;
        $data['address'] = $request->address;
        $data['email'] = $request->email;
        $data['facebook'] = $request->facebook;
        $data['twitter'] = $request->twitter;
        $data['website'] = $request->website;
        $data['linkedin'] = $request->linkedin;
        $data['paymentkeys'] = json_encode($request->paymentkeys);

        // Update password if provided
        if ($request->filled('password')) {
            $data['password'] = Hash::make($request->password);
        }

        if (isset($request->photo) && $request->hasFile('photo')) {
            remove_file(User::where('id', $id)->first()->photo);
            $path = "uploads/users/instructor/" . nice_file_name($request->name, $request->photo->extension());
            FileUploader::upload($request->photo, $path, 400, null, 200, 200);
            $data['photo'] = $path;
        }

        User::where('id', $id)->update($data);
        Session::flash('success', get_phrase('Instructor update successfully'));
        return redirect()->route('admin.instructor.index');
    }

    public function instructor_delete($id)
    {
        $threads = MessageThread::where('contact_one', $id)
            ->orWhere('contact_two', $id)
            ->pluck('id');

        if ($threads->isNotEmpty()) {
            Message::whereIn('thread_id', $threads)->delete();
            MessageThread::whereIn('id', $threads)->delete();
        }

        User::where('id', $id)->delete();
        Session::flash('success', get_phrase('Instructor delete successfully'));
        return redirect()->back();
    }

    public function instructor_view_course(Request $request)
    {
        $course = Course::where('user_id', $request->id)->get();
    }

    public function instructor_payout(Request $request)
    {
        $start_date = strtotime('first day of this month');
        $end_date = strtotime('last day of this month');
        $page_data['start_date'] = $start_date;
        $page_data['end_date'] = $end_date;
        $page_data['instructor_payout_complete'] = Payout::where('status', 1)->where('created_at', '>=', date('Y-m-d H:i:s', $start_date))
            ->where('created_at', '<=', date('Y-m-d H:i:s', $end_date))->paginate(10);
        $page_data['instructor_payout_incomplete'] = Payout::where('status', 0)->where('created_at', '>=', date('Y-m-d H:i:s', $start_date))
            ->where('created_at', '<=', date('Y-m-d H:i:s', $end_date))->paginate(10);
        return view('admin.instructor.payout', $page_data);
    }

    public function instructor_payout_filter(Request $request)
    {

        $date = explode('-', $request->eDateRange);
        $start_date = strtotime($date[0] . ' 00:00:00');
        $end_date = strtotime($date[1] . ' 23:59:59');
        $page_data['start_date'] = $start_date;
        $page_data['end_date'] = $end_date;

        $page_data['instructor_payout_complete'] = Payout::where('status', 1)->where('created_at', '>=', date('Y-m-d H:i:s', $start_date))
            ->where('created_at', '<=', date('Y-m-d H:i:s', $end_date))->paginate(10);
        $page_data['instructor_payout_incomplete'] = Payout::where('status', 0)->paginate(10);

        return view('admin.instructor.payout', $page_data);
    }

    public function instructor_payout_invoice($id = '')
    {
        if ($id != '') {
            $page_data['invoice_info'] = Payout::where('status', 1)->first();
            $page_data['invoice_data'] = Payout::where('status', 1)->get();
            $page_data['invoice_id'] = $id;

            return view('admin.instructor.instructor_invoice', $page_data);
        }
    }

    public function instructor_payment(Request $request)
    {
        $id = $request->user_id;
        $payable_amount = $request->amount;
        $start_timestamp = time();
        $end_timestamp = time();

        $payment_details = [
            'items' => [
                [
                    'id' => $id,
                    'title' => get_phrase('Pay for instructor payout'),
                    'subtitle' => get_phrase(''),
                    'price' => $payable_amount,
                    'discount_price' => $payable_amount,
                    'discount_percentage' => 0,
                ],
            ],
            'custom_field' => [
                'start_date' => date('Y-m-d H:i:s', $start_timestamp),
                'end_date' => date('Y-m-d H:i:s', $end_timestamp),
                'user_id' => auth()->user()->id,
                'payout_id' => $request->payout_id,

            ],
            'success_method' => [
                'model_name' => 'InstructorPayment',
                'function_name' => 'instructor_payment',
            ],
            'tax' => 0,
            'coupon' => null,
            'payable_amount' => $payable_amount,
            'cancel_url' => route('admin.instructor.payout'),
            'success_url' => route('payment.success'),
        ];
        session(['payment_details' => $payment_details]);
        return redirect()->route('payment');
    }

    public function instructor_setting()
    {
        $page_data['allow_instructor'] = Setting::where('type', 'allow_instructor')->first();
        $page_data['application_note'] = Setting::where('type', 'instructor_application_note')->first();
        $page_data['instructor_revenue'] = Setting::where('type', 'instructor_revenue')->first();
        return view('admin.instructor.instructor_setting', $page_data);
    }

    public function instructor_setting_store(Request $request)
    {

        if ($request->first == 'item_1') {

            $key_found = Setting::where('type', 'instructor_application_note')->exists();
            if ($key_found) {
                $data['description'] = $request->instructor_application_note;

                Setting::where('type', 'instructor_application_note')->update($data);
            } else {
                $data['type'] = 'instructor_application_note';
                $data['description'] = $request->instructor_application_note;

                Setting::insert($data);
            }

            $key_founds = Setting::where('type', 'allow_instructor')->exists();
            if ($key_founds) {
                $data['description'] = $request->allow_instructor;

                Setting::where('type', 'allow_instructor')->update($data);
            } else {

                $data['type'] = 'allow_instructor';
                $data['description'] = $request->allow_instructor;

                Setting::insert($data);
            }
        }
        if ($request->second == 'item_2') {

            $key_found = Setting::where('type', 'instructor_revenue')->exists();
            if ($key_found) {
                $data['description'] = $request->instructor_revenue;

                Setting::where('type', 'instructor_revenue')->update($data);
            } else {
                $data['type'] = 'instructor_revenue';
                $data['description'] = $request->instructor_revenue;

                Setting::insert($data);
            }
        }

        Session::flash('success', get_phrase('Instructor setting updated'));
        return redirect()->back();
    }

    public function instructor_application()
    {
        return view('admin.instructor.application');
    }

    public function instructor_application_approve($id)
    {
        $query = Application::where('id', $id);
        $update_status = $query->update(['status' => 1]);
        if ($update_status) {
            $user_id = $query->first();
            User::where('id', $user_id->user_id)->update(['role' => 'instructor']);
            Session::flash('success', get_phrase('Application approve successfully'));
        }
        return redirect()->back();
    }

    public function instructor_application_delete($id)
    {
        Application::where('id', $id)->delete();
        Session::flash('success', get_phrase('Application delete successfully'));
        return redirect()->back();
    }

    public function instructor_application_download($id)
    {
        $path = Application::where('id', $id)->first();

        if (file_exists(public_path($path->document))) {
            return response()->download(public_path($path->document));
        } else {
            Session::flash('error', get_phrase('File does not exists'));
            return redirect()->back();
        }
    }

    /**
     * Get source analytics data with efficient database querying and caching
     * This method handles large datasets efficiently
     */
    private function getSourceAnalytics()
    {
        $cacheKey = 'source_stats_' . date('Y-m-d');

        return Cache::remember($cacheKey, now()->addHours(6), function () {
            // 1. Get source counts in a single efficient query with indexing in mind
            $sourceStats = DB::table('users')
                ->select('source', DB::raw('count(*) as total'))
                ->where('role', 'student')
                ->whereNotNull('source')
                ->groupBy('source')
                ->orderBy('total', 'desc')
                ->limit(10) // Limit to top 10 sources for performance
                ->get();

            if ($sourceStats->isEmpty()) {
                return [];
            }

            // 2. Get conversion statistics in a single efficient query
            $conversionData = DB::table('users')
                ->select('users.source', DB::raw('COUNT(DISTINCT lead_opportunities.id) as converted'))
                ->leftJoin('lead_opportunities', function ($join) {
                    $join->on('users.id', '=', 'lead_opportunities.user_id')
                        ->where('lead_opportunities.status', '=', 'won');
                })
                ->whereIn('users.source', $sourceStats->pluck('source'))
                ->where('users.role', 'student')
                ->groupBy('users.source')
                ->get()
                ->keyBy('source');

            // 3. Calculate conversion rates and prepare the result
            $sourceConversion = [];
            foreach ($sourceStats as $source) {
                $sourceName = $source->source ?: 'Không xác định';
                $totalFromSource = $source->total;

                // Use the precalculated conversion count
                $wonDealsFromSource = isset($conversionData[$source->source])
                    ? $conversionData[$source->source]->converted : 0;

                $conversionRate = $totalFromSource > 0
                    ? round(($wonDealsFromSource / $totalFromSource) * 100, 2) : 0;

                $sourceConversion[] = [
                    'source' => $sourceName,
                    'total' => $totalFromSource,
                    'converted' => $wonDealsFromSource,
                    'conversion_rate' => $conversionRate
                ];
            }

            // 4. Sort by conversion rate
            usort($sourceConversion, function($a, $b) {
                return $b['conversion_rate'] <=> $a['conversion_rate'];
            });

            return $sourceConversion;
        });
    }

    public function student_index()
    {
        $query = User::where('role', 'student');

        // Sắp xếp mặc định từ mới đến cũ
        $sort_by = request('sort_by', 'created_at');
        $sort_order = request('sort_order', 'desc');

        // Áp dụng sắp xếp
        $query->orderBy($sort_by, $sort_order);

        if (isset($_GET['search']) && $_GET['search'] != '') {
            $search = $_GET['search'];
            $query = $query->where(function($q) use ($search) {
                $q->where('name', 'LIKE', '%' . $search . '%')
                  ->orWhere('email', 'LIKE', '%' . $search . '%')
                  ->orWhere('phone', 'LIKE', '%' . $search . '%');
            });
        }

        // Lọc theo trạng thái lead nếu có
        if (isset($_GET['lead_status']) && $_GET['lead_status'] != '') {
            $leadStatus = $_GET['lead_status'];

            $query = $query->whereHas('crmActivities', function($q) use ($leadStatus) {
                $q->where('lead_status', $leadStatus)
                  ->whereIn('id', function($sub) {
                      $sub->selectRaw('MAX(id)')
                          ->from('crm_activities')
                          ->groupBy('user_id');
                  });
            });
        }

        // Lọc theo nhân viên sale phụ trách
        if (isset($_GET['sale_id']) && $_GET['sale_id'] != '') {
            $query = $query->whereHas('crmActivities', function($q) {
                $q->where('sale_id', $_GET['sale_id']);
            });
        }

        // Lọc theo ngày follow up
        if (isset($_GET['follow_up_date']) && $_GET['follow_up_date'] != '') {
            $followUpDate = date('Y-m-d', strtotime($_GET['follow_up_date']));

            $query = $query->whereHas('crmActivities', function($q) use ($followUpDate) {
                $q->whereDate('next_follow_up', $followUpDate);
            });
        }

        // Lọc theo ngày đăng ký (từ ngày)
        if (isset($_GET['registration_date_from']) && $_GET['registration_date_from'] != '') {
            $registrationDateFrom = date('Y-m-d', strtotime($_GET['registration_date_from']));
            $query = $query->whereDate('created_at', '>=', $registrationDateFrom);
        }

        // Lọc theo ngày đăng ký (đến ngày)
        if (isset($_GET['registration_date_to']) && $_GET['registration_date_to'] != '') {
            $registrationDateTo = date('Y-m-d', strtotime($_GET['registration_date_to']));
            $query = $query->whereDate('created_at', '<=', $registrationDateTo);
        }

        $students = $query->paginate(10)->appends(request()->query());

        // Load doanh thu cho từng học viên để tránh N+1 query
        $studentIds = $students->pluck('id')->toArray();
        $studentRevenues = \App\Models\OfflinePayment::where('status', 1)
            ->whereIn('user_id', $studentIds)
            ->selectRaw('user_id, SUM(total_amount) as total_revenue, COUNT(*) as payment_count')
            ->groupBy('user_id')
            ->get()
            ->keyBy('user_id');

        // Gán dữ liệu doanh thu vào từng học viên
        foreach ($students as $student) {
            $revenueData = $studentRevenues->get($student->id);
            $student->total_revenue = $revenueData ? $revenueData->total_revenue : 0;
            $student->payment_count = $revenueData ? $revenueData->payment_count : 0;
        }

        $page_data['students'] = $students;
        $page_data['sort_by'] = $sort_by;
        $page_data['sort_order'] = $sort_order;

        // Lấy danh sách nhân viên sale
        $page_data['sales'] = User::where('role', 'admin')->get();

        // Lấy danh sách trạng thái lead
        $page_data['lead_statuses'] = \App\Models\CrmActivity::getLeadStatusOptions();

        // Lấy danh sách loại hoạt động
        $page_data['activity_types'] = \App\Models\CrmActivity::getActivityTypeOptions();

        // Lấy danh sách kết quả hoạt động
        $page_data['activity_results'] = \App\Models\CrmActivity::getResultOptions();
        $total_lead = User::where('role', 'student')->count();
        // Tính tổng doanh thu từ offline_payments (đã duyệt)
        $total_revenue = \App\Models\OfflinePayment::where('status', 1)->sum('total_amount');

        // Tính số học viên đã mua khóa học (có payment đã duyệt)
        $paid_students = \App\Models\OfflinePayment::where('status', 1)
            ->distinct('user_id')
            ->count('user_id');

        // Tính doanh thu bình quân mỗi học viên
        $avg_revenue_per_student = $paid_students > 0 ? round($total_revenue / $paid_students, 0) : 0;

        // Lấy thống kê
        $page_data['stats'] = [
            'total_leads' =>$total_lead,
            'new_leads' => User::whereDoesntHave('crmActivities')->where('role', 'student')->count(),
            'contacted_leads' => User::whereHas('crmActivities', function($q) {
                $q->where('lead_status', 'contacted')
                  ->whereIn('id', function($sub) {
                      $sub->selectRaw('MAX(id)')
                          ->from('crm_activities')
                          ->groupBy('user_id');
                  });
            })->count(),
            'opportunities' => \App\Models\LeadOpportunity::where('status', 'open')->count(),
            'won_deals' => \App\Models\LeadOpportunity::where('status', 'won')->count(),
            'conversion_rate' => \App\Models\LeadOpportunity::count() > 0
                ? round((\App\Models\LeadOpportunity::where('status', 'won')->count() / $total_lead) * 100, 2)
                : 0,
            'total_value' => number_format($total_revenue, 0, ',', '.') . ' VNĐ',
            'avg_revenue_per_student' => number_format($avg_revenue_per_student, 0, ',', '.') . ' VNĐ',
            'paid_students' => $paid_students
        ];

        // Get source analytics data using the optimized method
        $page_data['source_stats'] = $this->getSourceAnalytics();

        return view('admin.student.index', $page_data);
    }

    public function student_create()
    {
        return view('admin.student.create_student');
    }

    public function student_edit($id = '')
    {
        $page_data['student'] = User::where('id', $id)->first();
        return view('admin.student.edit_student', $page_data);
    }

    public function student_status(Request $request, $id = '')
    {

        $validated = $request->validate([
            'status' => 'required|in:0,1',
            'id' => 'required|exists:users,id',
        ]);

        $data = ['status' => (int)$request->status];

        // Reset account violations when activating account
        if ((int)$request->status === 1) {
            $data['account_violations'] = 0;
        }

        User::where('id', (int)$request->id)->update($data);

        Session::flash('success', get_phrase('Student status updated successfully'));
        return redirect()->back();
    }

    public function student_store(Request $request, $id = '')
    {

        $validated = $request->validate([
            'name' => 'required|max:255',
            'email' => 'required|email|unique:users',
            'password' => 'required',
        ]);

        if (get_settings('student_email_verification') != 1) {
            $data['email_verified_at'] = date('Y-m-d H:i:s');
        }

        $data['name'] = $request->name;
        $data['about'] = $request->about;
        $data['phone'] = $request->phone;
        $data['address'] = $request->address;
        $data['email'] = $request->email;
        $data['facebook'] = $request->facebook;
        $data['twitter'] = $request->twitter;
        $data['website'] = $request->website;
        $data['linkedin'] = $request->linkedin;
        $data['paymentkeys'] = json_encode($request->paymentkeys);
        $data['status'] = '1';

        $data['password'] = Hash::make($request->password);
        $data['role'] = 'student';

        if (isset($request->photo) && $request->hasFile('photo')) {
            $path = "uploads/users/student/" . nice_file_name($request->name, $request->photo->extension());
            FileUploader::upload($request->photo, $path, 400, null, 200, 200);
            $data['photo'] = $path;
        }

        $user = User::create($data);

        if (get_settings('student_email_verification') == 1) {
            $user->sendEmailVerificationNotification();
        }

        Session::flash('success', get_phrase('Student add successfully'));

        return redirect()->route('admin.student.index');
    }

    public function student_update(Request $request, $id = '')
    {
        $validated = $request->validate([
            'name' => 'required|max:255',
            'email' => "required|email|unique:users,email,$id",
        ]);

        $data['name'] = $request->name;
        $data['about'] = $request->about;
        $data['phone'] = $request->phone;
        $data['address'] = $request->address;
        $data['email'] = $request->email;
        $data['facebook'] = $request->facebook;
        $data['twitter'] = $request->twitter;
        $data['website'] = $request->website;
        $data['linkedin'] = $request->linkedin;
        $data['paymentkeys'] = json_encode($request->paymentkeys);

        // Update password if provided
        if ($request->filled('password')) {
            $data['password'] = Hash::make($request->password);
        }

        if (isset($request->photo) && $request->hasFile('photo')) {
            remove_file(User::where('id', $id)->first()->photo);
            $path = "uploads/users/student/" . nice_file_name($request->name, $request->photo->extension());
            FileUploader::upload($request->photo, $path, 400, null, 200, 200);
            $data['photo'] = $path;
        }

        User::where('id', $id)->update($data);
        Session::flash('success', get_phrase('Student update successfully'));
        return redirect()->route('admin.student.index');
    }

    public function student_delete($id)
    {
        $threads = MessageThread::where('contact_one', $id)
            ->orWhere('contact_two', $id)
            ->pluck('id');

        if ($threads->isNotEmpty()) {
            Message::whereIn('thread_id', $threads)->delete();
            MessageThread::whereIn('id', $threads)->delete();
        }

        $query = User::where('id', $id);
        remove_file($query->first()->photo);
        $query->delete();
        return redirect(route('admin.student.index'))->with('success', get_phrase('User deleted successfully'));
    }

    public function student_enrollments($id)
    {
        $page_data['student'] = User::where('id', $id)->first();
        $page_data['enrollments'] = Enrollment::where('user_id', $id)
            ->with('course')
            ->orderBy('created_at', 'desc')
            ->paginate(10);
        return view('admin.student.enrollments', $page_data);
    }

    public function student_learning_progress($id)
    {
        $student = User::where('id', $id)->first();
        if (!$student) {
            return redirect()->route('admin.student.index')->with('error', 'Học viên không tồn tại');
        }

        // Lấy tất cả khóa học mà học viên đã xem từ bảng watch_durations
        $watched_course_ids = WatchDuration::where('watched_student_id', $id)
            ->distinct()
            ->pluck('watched_course_id')
            ->toArray();

        $learning_progress = [];

        foreach ($watched_course_ids as $course_id) {
            $course = Course::with(['lessons', 'sections'])->where('id', $course_id)->first();
            if (!$course) continue;

            // Lấy thông tin enrollment nếu có (có thể null nếu chỉ xem mà không đăng ký)
            $enrollment = Enrollment::where('user_id', $id)
                ->where('course_id', $course->id)
                ->first();

            // Lấy ngày xem đầu tiên từ watch_durations
            $first_watch_date = WatchDuration::where('watched_student_id', $id)
                ->where('watched_course_id', $course->id)
                ->min('created_at');

            // Lấy lần xem cuối cùng từ watch_durations
            $last_watch_date = WatchDuration::where('watched_student_id', $id)
                ->where('watched_course_id', $course->id)
                ->max('updated_at');

            // Lấy tất cả bài học của khóa học
            $total_lessons = $course->lessons()->count();
            $total_duration_seconds = $course->lessons()->sum(\DB::raw('TIME_TO_SEC(duration)'));

            // Lấy lịch sử xem của học viên cho khóa học này
            $watch_history = Watch_history::where('student_id', $id)
                ->where('course_id', $course->id)
                ->first();

            $completed_lessons = [];
            $course_progress = 0;

            if ($watch_history && $watch_history->completed_lesson) {
                $completed_lessons = json_decode($watch_history->completed_lesson, true) ?: [];
                $course_progress = $watch_history->course_progress ?: 0;
            }

            // Lấy thời gian xem chi tiết từ watch_durations
            $watch_durations = WatchDuration::where('watched_student_id', $id)
                ->where('watched_course_id', $course->id)
                ->get();

            $total_watched_seconds = 0;
            $lesson_progress = [];

            foreach ($watch_durations as $duration) {
                $lesson = $course->lessons()->where('id', $duration->watched_lesson_id)->first();
                if ($lesson) {
                    $lesson_duration_seconds = $this->timeToSeconds($lesson->duration);
                    $watched_percentage = $lesson_duration_seconds > 0 ?
                        min(100, ($duration->current_duration / $lesson_duration_seconds) * 100) : 0;

                    $lesson_progress[$duration->watched_lesson_id] = [
                        'lesson' => $lesson,
                        'watched_seconds' => $duration->current_duration,
                        'total_seconds' => $lesson_duration_seconds,
                        'percentage' => round($watched_percentage, 1),
                        'is_completed' => in_array($duration->watched_lesson_id, $completed_lessons)
                    ];

                    $total_watched_seconds += $duration->current_duration;
                }
            }

            // Tính phần trăm tổng thể
            $overall_percentage = $total_duration_seconds > 0 ?
                min(100, ($total_watched_seconds / $total_duration_seconds) * 100) : 0;

            $learning_progress[] = [
                'enrollment' => $enrollment, // Có thể null nếu chỉ xem mà không đăng ký
                'course' => $course,
                'first_watch_date' => $first_watch_date, // Ngày xem đầu tiên
                'last_watch_date' => $last_watch_date, // Lần xem cuối cùng
                'total_lessons' => $total_lessons,
                'completed_lessons_count' => count($completed_lessons),
                'completed_lessons' => $completed_lessons,
                'course_progress' => $course_progress,
                'overall_percentage' => round($overall_percentage, 1),
                'total_duration_seconds' => $total_duration_seconds,
                'total_watched_seconds' => $total_watched_seconds,
                'lesson_progress' => $lesson_progress,
                'sections' => $course->sections()->with('lessons')->orderBy('sort', 'asc')->get()
            ];
        }

        // Sắp xếp theo lần xem cuối cùng (gần nhất)
        usort($learning_progress, function($a, $b) {
            return strtotime($b['last_watch_date']) - strtotime($a['last_watch_date']);
        });

        $page_data = [
            'student' => $student,
            'learning_progress' => $learning_progress
        ];

        return view('admin.student.learning_progress', $page_data);
    }

    private function timeToSeconds($time)
    {
        if (!$time) return 0;

        $parts = explode(':', $time);
        if (count($parts) == 3) {
            return ($parts[0] * 3600) + ($parts[1] * 60) + $parts[2];
        } elseif (count($parts) == 2) {
            return ($parts[0] * 60) + $parts[1];
        }

        return (int)$time;
    }

    public function manageDevices($id)
    {
        $user = User::findOrFail($id);
        $devices = \App\Models\DeviceIp::where('user_id', $id)
            ->orderBy('updated_at', 'desc')
            ->get();

        // Debug - in log biến đang được truyền
        \Log::info('User: ' . $user->name . ', Devices count: ' . $devices->count());

        return view('admin.student.manage_device', [
            'user' => $user,
            'devices' => $devices
        ]);
    }

    public function deleteDevice($id, $user_id)
    {
        \App\Models\DeviceIp::where('id', $id)->delete();
        Session::flash('success', get_phrase('Device removed successfully'));
        return redirect()->route('admin.student.devices', ['id' => $user_id]);
    }

    public function resetViolations($user_id)
    {
        $user = User::findOrFail($user_id);
        $user->account_violations = 0;
        $user->save();

        Session::flash('success', get_phrase('Account violations reset successfully'));
        return redirect()->route('admin.student.devices', ['id' => $user_id]);
    }

    public function student_enrol()
    {
        return view('admin.enroll.course_enrollment');
    }

    public function student_get(Request $request)
    {
        $searchVal = $request->searchVal ?? '';

        $user = User::where('role', 'student')
            ->where('status', 1)
            ->where(function($query) use ($searchVal) {
                $query->where('name', 'LIKE', '%' . $searchVal . '%')
                      ->orWhere('email', 'LIKE', '%' . $searchVal . '%')
                      ->orWhere('phone', 'LIKE', '%' . $searchVal . '%');
            })
            ->orderBy('name', 'asc')
            ->limit(50) // Giới hạn kết quả để tối ưu performance
            ->get();

        $response = [];
        foreach ($user as $row) {
            $response[] = [
                'id' => $row->id,
                'text' => $row->name . ' (' . $row->email . ')'
            ];
        }
        return json_encode($response);
    }

    public function student_post(Request $request)
    {
        // Validate input
        $request->validate([
            'amount_received' => 'required|numeric|min:0',
            'course_id' => 'required|exists:courses,id',
            'user_id' => 'required|array|min:1',
            'user_id.*' => 'exists:users,id',
            'duration_type' => 'required|in:lifetime,months,course_default',
            'duration_months' => 'required_if:duration_type,months|nullable|integer|min:1|max:60'
        ]);

        $course_details = get_course_info($request->course_id);

        for ($i = 0; $i < count($request->user_id); $i++) {
            // Xóa enrollment cũ nếu có (để tránh duplicate)
            Enrollment::where('course_id', $request->course_id)
                ->where('user_id', $request->user_id[$i])
                ->delete();

            $data = [
                'user_id' => $request->user_id[$i],
                'course_id' => $request->course_id,
                'entry_date' => time(),
                'enrollment_type' => 'paid',
                'paid_amount' => $request->amount_received,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Xử lý thời gian hết hạn theo lựa chọn
            switch ($request->duration_type) {
                case 'lifetime':
                    $data['expiry_date'] = null;
                    break;

                case 'months':
                    $months = (int)$request->duration_months;
                    $data['expiry_date'] = strtotime("+" . $months . " months");
                    break;

                case 'course_default':
                default:
                    if ($course_details->expiry_period > 0) {
                        $months = $course_details->expiry_period;
                        $data['expiry_date'] = strtotime("+" . $months . " months");
                    } else {
                        $data['expiry_date'] = null;
                    }
                    break;
            }

            // Tạo enrollment mới (đã xóa enrollment cũ ở trên)
            Enrollment::create($data);

            // Tạo record trong bảng offline_payments
            $offline_payment_data = [
                'user_id' => $request->user_id[$i],
                'item_type' => 'course',
                'items' => json_encode([$request->course_id]),
                'tax' => 0,
                'total_amount' => $request->amount_received,
                'payment_type' => 'offline',
                'course_id' => $request->course_id,
                'status' => 1, // Đã thanh toán
                'doc' => null,
                'coupon' => null,
                'phone_no' => null,
                'bank_no' => null,
                'affiliate_amount' => null,
                'affiliate_id' => null,
                'is_approve_affiliate' => true,
                'is_add_earning_affiliate' => 0,
                'transaction_content' => 'Enroll offline - Admin: ' . auth()->user()->name
            ];

            OfflinePayment::create($offline_payment_data);
        }

        $student_count = count($request->user_id);
        $formatted_amount = number_format($request->amount_received, 0, ',', '.');

        // Tạo thông báo thành công với thông tin thời gian
        $duration_text = '';
        switch ($request->duration_type) {
            case 'lifetime':
                $duration_text = 'trọn đời';
                break;
            case 'months':
                $duration_text = $request->duration_months . ' tháng';
                break;
            case 'course_default':
                if ($course_details->expiry_period > 0) {
                    $duration_text = $course_details->expiry_period . ' tháng (mặc định khóa học)';
                } else {
                    $duration_text = 'trọn đời (mặc định khóa học)';
                }
                break;
        }

        Session::flash('success', "Đã đăng ký thành công {$student_count} học viên vào khóa học '{$course_details->title}' với thời gian {$duration_text}. Số tiền thu: {$formatted_amount} VNĐ");
        return redirect()->route('admin.enroll.history');
    }

    public function available_students(Request $request)
    {
        $course_ids = $request->course_ids;
        $search = $request->search ?? '';

        // Get all students with search functionality
        $students = User::where('role', 'student')
            ->where('status', 1) // Chỉ lấy học viên active
            ->when($search, function($query, $search) {
                return $query->where(function($q) use ($search) {
                    $q->where('name', 'LIKE', '%' . $search . '%')
                      ->orWhere('email', 'LIKE', '%' . $search . '%');
                });
            })
            ->orderBy('name', 'asc');

        // Get students who are already enrolled in any of the selected courses
        $enrolledStudentIds = Enrollment::whereIn('course_id', $course_ids)
            ->pluck('user_id')
            ->unique()
            ->toArray();

        // Get all students (both enrolled and not enrolled)
        $allStudents = $students->get();

        // Mark which students are already enrolled
        $studentsWithStatus = $allStudents->map(function($student) use ($enrolledStudentIds) {
            $student->is_enrolled = in_array($student->id, $enrolledStudentIds);
            return $student;
        });

        return response()->json([
            'students' => $studentsWithStatus
        ]);
    }

    public function search_students(Request $request)
    {
        $search = $request->search ?? '';
        $limit = $request->limit ?? 20;

        $students = User::where('role', 'student')
            ->where('status', 1)
            ->when($search, function($query, $search) {
                return $query->where(function($q) use ($search) {
                    $q->where('name', 'LIKE', '%' . $search . '%')
                      ->orWhere('email', 'LIKE', '%' . $search . '%')
                      ->orWhere('phone', 'LIKE', '%' . $search . '%');
                });
            })
            ->orderBy('name', 'asc')
            ->limit($limit)
            ->get(['id', 'name', 'email']);

        return response()->json([
            'students' => $students
        ]);
    }

    public function enroll_history(Request $request)
    {
        if ($request->eDateRange) {
            $date = explode('-', $request->eDateRange);
            $start_date = strtotime($date[0] . ' 00:00:00');
            $end_date = strtotime($date[1] . ' 23:59:59');
            $page_data['start_date'] = $start_date;
            $page_data['end_date'] = $end_date;
            $page_data['enroll_history'] = Enrollment::where('entry_date', '>=', $start_date)
                ->where('entry_date', '<=', $end_date)
                ->paginate(10)->appends($request->query());
        } else {
            $start_date = strtotime('first day of this month ');
            $end_date = strtotime('last day of this month');
            $page_data['start_date'] = $start_date;
            $page_data['end_date'] = $end_date;
            $page_data['enroll_history'] = Enrollment::where('entry_date', '>=', $start_date)
                ->where('entry_date', '<=', $end_date)->paginate(10);
        }
        return view('admin.enroll.enroll_history', $page_data);
    }

    public function enroll_history_delete($id)
    {

        Enrollment::where('id', $id)->delete();
        Session::flash('success', get_phrase('Enroll delete successfully'));
        return redirect()->back();
    }

    public function manage_profile()
    {
        return view('admin.profile.index');
    }

    public function manage_profile_update(Request $request)
    {
        if ($request->type == 'general') {
            $profile['name'] = $request->name;
            $profile['email'] = $request->email;
            $profile['facebook'] = $request->facebook;
            $profile['linkedin'] = $request->linkedin;
            $profile['twitter'] = $request->twitter;
            $profile['about'] = $request->about;
            $profile['skills'] = $request->skills;
            $profile['biography'] = $request->biography;

            if ($request->photo) {
                if (isset($request->photo) && $request->photo != '') {
                    $profile['photo'] = "uploads/users/admin/" . nice_file_name($request->title, $request->photo->extension());
                    FileUploader::upload($request->photo, $profile['photo'], 400, null, 200, 200);
                }
            }
            User::where('id', auth()->user()->id)->update($profile);
        } else {
            $old_pass_check = Auth::attempt(['email' => auth()->user()->email, 'password' => $request->current_password]);

            if (!$old_pass_check) {
                Session::flash('error', get_phrase('Current password wrong.'));
                return redirect()->back();
            }

            if ($request->new_password != $request->confirm_password) {
                Session::flash('error', get_phrase('Confirm password not same'));
                return redirect()->back();
            }

            $password = Hash::make($request->new_password);
            User::where('id', auth()->user()->id)->update(['password' => $password]);
        }
        Session::flash('success', get_phrase('Your changes has been saved.'));
        return redirect()->back();
    }
}
