<?php

namespace App\Http;

use Illuminate\Foundation\Http\Kernel as HttpKernel;

class <PERSON>el extends HttpKernel
{
    /**
     * The application's global HTTP middleware stack.
     *
     * These middleware are run during every request to your application.
     *
     * @var array<int, class-string|string>
     */
    protected $middleware = [
        // \App\Http\Middleware\TrustHosts::class,
        \App\Http\Middleware\TrustProxies::class,
        \Illuminate\Http\Middleware\HandleCors::class,
        \App\Http\Middleware\PreventRequestsDuringMaintenance::class,
        \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
        \App\Http\Middleware\TrimStrings::class,
        \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
    ];

    /**
     * The application's route middleware groups.
     *
     * @var array<string, array<int, class-string|string>>
     */
    protected $middlewareGroups = [
        'web' => [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \App\Http\Middleware\CheckUserStatus::class,
        ],

        'api' => [
            \Illuminate\Routing\Middleware\ThrottleRequests::class . ':api',
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
        ],
    ];

    /**
     * The application's middleware aliases.
     *
     * Aliases may be used to conveniently assign middleware to routes and groups.
     *
     * @var array<string, class-string|string>
     */
    protected $middlewareAliases = [
        'allow-embedding' => \App\Http\Middleware\AllowEmbedding::class,
        'auth.token' => \App\Http\Middleware\HandleAuthToken::class,
        'auth' => \App\Http\Middleware\Authenticate::class,
        'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        'auth.session' => \Illuminate\Session\Middleware\AuthenticateSession::class,
        'cache.headers' => \Illuminate\Http\Middleware\SetCacheHeaders::class,
        'can' => \Illuminate\Auth\Middleware\Authorize::class,
        'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
        'password.confirm' => \Illuminate\Auth\Middleware\RequirePassword::class,
        'signed' => \App\Http\Middleware\ValidateSignature::class,
        'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        'verified' => \Illuminate\Auth\Middleware\EnsureEmailIsVerified::class,
        'admin' => \App\Http\Middleware\Admin::class,
        'instructor' => \App\Http\Middleware\Instructor::class,
        'instructorBlogPermission' => \App\Http\Middleware\InstructorBlogPermission::class,
        'enrollCheck' => \App\Http\Middleware\EnrollCheck::class,
        'blog.visibility' => \App\Http\Middleware\BlogVisibility::class,
        'permissioncheck' => \App\Http\Middleware\ParmissionCheckRoute::class,
        'becomeaninstructor' => \App\Http\Middleware\Becomeaninstructor::class,
        'ip.detector' => \App\Http\Middleware\IpDetectorMiddleware::class,
        'record.exists' => \App\Http\Middleware\RecordVerification::class,
        'webConfig' => \App\Http\Middleware\WebConfig::class,
        'affiliate' => \App\Http\Middleware\AffiliateHandler::class,
        'check.user.status' => \App\Http\Middleware\CheckUserStatus::class,
        'cors' => \App\Http\Middleware\Cors::class,
        'check.system.expiry' => \App\Http\Middleware\CheckSystemExpiry::class,
    ];
}
