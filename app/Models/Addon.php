<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Addon extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'author',
        'name_addons',
        'status',
        'description',
        'thumbnail',
        'permission_keys',
        'price',
        'required_package',
        'is_purchased'
    ];

    protected $casts = [
        'status' => 'boolean',
        'is_purchased' => 'boolean',
    ];

    /**
     * Get the permission keys as array
     *
     * @return array
     */
    public function getPermissionKeysAttribute($value)
    {
        return json_decode($value, true) ?? [];
    }

    /**
     * Format price with currency
     *
     * @return string
     */
    public function getFormattedPriceAttribute()
    {
        if ($this->price == 0) {
            return get_phrase('Miễn phí');
        }

        return number_format($this->price, 0, ',', '.') . ' đ';
    }

    /**
     * Check if current package is sufficient to use this addon
     *
     * @return boolean
     */
    public function isPackageSufficient()
    {
        // Get current package from config
        $currentPackage = strtoupper(config('app.package_name', env('PACKAGE_NAME', 'BASIC')));

        // Package hierarchy
        $packageHierarchy = [
            'FREE' => 1,
            'BASIC' => 2,
            'PRO' => 3,
            'PREMIUM' => 4
        ];

        // Get required package level
        $requiredLevel = $packageHierarchy[$this->required_package] ?? 3; // Default to PRO level if not found

        // Get current package level
        $currentLevel = $packageHierarchy[$currentPackage] ?? 2; // Default to BASIC level if not found

        // Return true if current package level is >= required level
        return $currentLevel >= $requiredLevel;
    }
}
