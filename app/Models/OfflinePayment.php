<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OfflinePayment extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'invoice',
        'payment_type',
        'item_type',
        'items',
        'tax',
        'total_amount',
        'coupon',
        'phone_no',
        'bank_no',
        'doc',
        'status',
        'course_id',
        'affiliate_amount',
        'affiliate_id',
        'is_approve_affiliate',
        'is_add_earning_affiliate',
        'transaction_content',
        'pricing_plan_id'

    ];
    public function affiliater()
    {
        return $this->belongsTo(User::class, 'affiliate_id', 'id');
    }
    public function course()
    {
        return $this->belongsTo(Course::class, 'course_id', 'id');
    }
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
}
