```markdown
# <PERSON><PERSON> chú tích hợp Backend cho Template `home-spa-dieu-thu`

<PERSON><PERSON><PERSON> là tài liệu ghi chú lại các điểm cần kết nối với backend khi tích hợp template HTML `home-spa-dieu-thu.blade.php` vào hệ thống <PERSON>.

## 1. <PERSON><PERSON><PERSON><PERSON> lý <PERSON> (CSS, JS, Hình ảnh)

Toàn bộ các đường dẫn đến file tĩnh cần được chuyển đổi để sử dụng helper `asset()` của Laravel để đảm bảo đường dẫn luôn chính xác.

- **Ví dụ:**
  - `src="assets/frontend/dieu-thu/assets/images/logo.png"` -> `src="{{ asset('assets/frontend/dieu-thu/assets/images/logo.png') }}"`
  - Các file CSS và JS cũng cần được áp dụng tương tự.
  - <PERSON><PERSON><PERSON> ảnh động từ CSDL (như avatar ng<PERSON>ời dùng) cần helper riêng, ví dụ: `get_image()`.

## 2. <PERSON><PERSON><PERSON> thực người dùng (Authentication)

Template có các khu vực hiển thị khác nhau tùy thuộc vào trạng thái đăng nhập của người dùng.

- **Kiểm tra trạng thái đăng nhập:** Sử dụng `Auth()->check()`.
  ```php
  @if(!Auth()->check())
      // Hiển thị nút "ĐĂNG NHẬP"
  @else
      // Hiển thị thông tin người dùng và dropdown menu
  @endif
  ```
- **Hiển thị thông tin người dùng:** Truy cập thông tin người dùng đã đăng nhập qua `Auth()->user()`.
  - `Auth()->user()->photo`: Avatar
  - `ucfirst(Auth()->user()->name)`: Tên người dùng
  - `Auth()->user()->email`: Email
- **Form Đăng nhập/Đăng ký:** Nút "ĐĂNG NHẬP" kích hoạt một modal (`#modal-auth`). Form bên trong modal này cần phải:
  - Có action trỏ đến route xử lý đăng nhập/đăng ký.
  - Có CSRF token: `@csrf`.
  - Xử lý validation và trả về lỗi nếu có.
- **Đăng xuất:** Link đăng xuất cần trỏ đến route xử lý việc logout.
  - `href="{{ route('logout.course') }}"`

## 3. Phân quyền (Authorization)

Menu người dùng có các mục hiển thị tùy theo vai trò (role) của người dùng.

- **Kiểm tra vai trò:**
  ```php
  @if (in_array(auth()->user()->role, ['admin', 'instructor']))
      // Hiển thị link Dashboard
  @endif

  @if (Auth()->user()->role != 'admin')
      // Hiển thị các link của học viên
  @endif
  ```

## 4. Định tuyến (Routing)

Các link điều hướng trong trang cần được thay thế bằng helper `route()` của Laravel.

- **Ví dụ:**
  - `href="{{ route('my.courses') }}"`
  - `href="{{ route('my.profile') }}"`
  - `href="{{ route(auth()->user()->role . '.dashboard') }}"` (Route động dựa trên vai trò)
- Các link anchor (`href="#gioi-thieu"`) dùng để cuộn trang thì giữ nguyên.

## 5. Nội dung động từ Database

Các phần nội dung chính của trang cần được đổ dữ liệu từ cơ sở dữ liệu.

- **Trạng thái đăng ký khóa học:**
  - `enroll_status($course_details->id, Auth()->user()->id)`: Cần một biến `$course_details` được truyền từ Controller vào view. Hàm `enroll_status` sẽ thực hiện logic kiểm tra trong database.
- **Danh sách khóa học (`#khoa-hoc`):**
  - Cần một vòng lặp `@foreach` để duyệt qua danh sách các khóa học được lấy từ `Course` model.
  - Mỗi khóa học sẽ hiển thị các thông tin như: hình ảnh, tên, mô tả, giá, giảng viên...
- **Đánh giá của học viên (`#danh-gia`):**
  - Tương tự, dùng `@foreach` để hiển thị các đánh giá (reviews) từ `Review` model, bao gồm avatar, tên học viên, nội dung đánh giá, xếp hạng sao.
- **Form Liên hệ (`#lien-he`):**
  - Form cần có action trỏ đến route xử lý việc lưu thông tin liên hệ vào database hoặc gửi email.
  - Cần có CSRF token và validation cho các trường dữ liệu.

## 6. Đa ngôn ngữ (Localization)

Các chuỗi văn bản tĩnh (static text) nên được quản lý bằng hệ thống localization của Laravel để dễ dàng dịch thuật.

- **Sử dụng helper `get_phrase()` hoặc `__()`:**
  - `{{ get_phrase('Dashboard') }}`
  - `{{ get_phrase('My Courses') }}`

## 7. Các Helper Function

Cần định nghĩa các hàm helper để tái sử dụng logic.

- `get_image($path)`: Hàm để lấy đường dẫn ảnh chính xác, có thể bao gồm logic cho ảnh mặc định.
- `enroll_status($course_id, $user_id)`: Hàm kiểm tra xem người dùng đã mua khóa học hay chưa.
- `get_phrase($key)`: Hàm để dịch thuật.

## Tóm tắt quy trình tích hợp:

1.  **Setup Route:** Định nghĩa tất cả các route cần thiết trong `routes/web.php`.
2.  **Tạo Controller:** Tạo một Controller (ví dụ: `HomepageController`) để xử lý logic, lấy dữ liệu từ Model và truyền xuống View.
3.  **Chỉnh sửa Blade View:**
    - Thay thế các đường dẫn tĩnh bằng `asset()`.
    - Thay thế các link bằng `route()`.
    - Sử dụng các directive của Blade (`@if`, `@foreach`, `@auth`, `@guest`) để xử lý logic hiển thị.
    - Lấy dữ liệu động được truyền từ Controller để hiển thị.
    - Thêm `@csrf` cho tất cả các form.
4.  **Tạo/Cập nhật Model:** Đảm bảo các Model (User, Course, Review,...) có đủ các thuộc tính và quan hệ cần thiết.
5.  **Tạo Helper:** Tạo các hàm helper chung trong `app/Helpers`.
```