<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test GrapesJS Layout Blocks</title>

    <!-- GrapesJS CSS -->
    <link rel="stylesheet" href="https://unpkg.com/grapesjs/dist/css/grapes.min.css">

    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100vh;
            font-family: Arial, sans-serif;
        }

        .header {
            background: #2c3e50;
            color: white;
            padding: 10px 20px;
            text-align: center;
        }

        #gjs {
            height: calc(100vh - 60px);
            border: none;
        }

        /* Custom styling for GrapesJS blocks */
        .gjs-block {
            border-radius: 8px;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .gjs-block:hover {
            border-color: #007bff;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }

        /* Block category styling */
        .gjs-blocks-c .gjs-block-category .gjs-title {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            font-weight: 600;
            padding: 10px 15px;
            border-radius: 6px 6px 0 0;
        }

        /* Canvas styling */
        .gjs-cv-canvas {
            background: #f8f9fa;
        }
    </style>
</head>
<body>
<div class="header">
    <h2>Test GrapesJS với Layout Blocks</h2>
</div>

<div id="gjs"></div>

<!-- GrapesJS JavaScript -->
<script src="https://unpkg.com/grapesjs"></script>
<script src="https://unpkg.com/grapesjs-preset-webpage"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Initializing GrapesJS test...');

        try {
            const editor = grapesjs.init({
                container: '#gjs',
                height: '100%',
                width: 'auto',
                storageManager: false,

                // Use preset-webpage for full default functionality including configurable rows/columns
                plugins: ['gjs-preset-webpage'],
                pluginsOpts: {
                    'gjs-preset-webpage': {
                        modalImportTitle: 'Nhập mã HTML',
                        modalImportLabel: '<div style="margin-bottom: 10px; font-size: 13px;">Dán mã HTML/CSS của bạn và nhấn Nhập</div>',
                        modalImportContent: function(editor) {
                            return editor.getHtml() + '<style>' + editor.getCss() + '</style>';
                        },
                        filestackOpts: false,
                        aviaryOpts: false,
                        blocksBasicOpts: {
                            // Enable default layout blocks with Vietnamese labels
                            blocks: ['column1', 'column2', 'column3', 'column3-7', 'text', 'link', 'image', 'video'],
                            flexGrid: 1,
                            labelColumn1: '1 Cột',
                            labelColumn2: '2 Cột',
                            labelColumn3: '3 Cột',
                            labelColumn37: '2 Cột (3/7)',
                            labelText: 'Văn bản',
                            labelLink: 'Liên kết',
                            labelImage: 'Hình ảnh',
                            labelVideo: 'Video',
                        }
                    }
                },

                // Canvas configuration with Bootstrap
                canvas: {
                    styles: [
                        'https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css'
                    ]
                }
            });

            console.log('GrapesJS initialized successfully with default blocks');

            // Wait for blocks to load
            setTimeout(() => {
                console.log('Available blocks:', editor.BlockManager.getAll().models.map(block => ({
                    id: block.get('id'),
                    label: block.get('label'),
                    category: block.get('category')
                })));
            }, 1000);

        } catch (error) {
            console.error('Error initializing GrapesJS:', error);
        }
    });
</script>
</body>
</html>
