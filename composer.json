{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1", "elfsundae/laravel-hashid": "^1.7", "firebase/php-jwt": "^6.10", "guzzlehttp/guzzle": "^7.2", "intervention/image": "^2.7", "jenssegers/agent": "^2.6", "laravel/framework": "^11.0", "laravel/sanctum": "^4.0", "laravel/socialite": "^5.18", "laravel/tinker": "^2.8", "paytm/paytmchecksum": "^1.1", "pbmedia/laravel-ffmpeg": "^8.6", "php-ffmpeg/php-ffmpeg": "^1.3", "razorpay/razorpay": "^2.8", "simplesoftwareio/simple-qrcode": "^4.2", "stripe/stripe-php": "^12.5"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.15", "laravel/breeze": "^2.0", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^10.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "paytm\\paytmchecksum\\": "paytmchecksum/"}, "files": ["app/Helpers/Common_helper.php", "app/Helpers/Api_helper.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}