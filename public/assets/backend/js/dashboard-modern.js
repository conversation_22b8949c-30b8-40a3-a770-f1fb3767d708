/**
 * Modern Dashboard JavaScript Enhancements
 * Provides interactive features and animations for the dashboard
 */

document.addEventListener('DOMContentLoaded', function() {

    // Initialize dashboard enhancements
    initializeCounterAnimations();
    initializeCardHoverEffects();
    initializeTooltips();
    initializeProgressBars();
    initializeLazyLoading();

    // Counter Animation for Statistics
    function initializeCounterAnimations() {
        const counters = document.querySelectorAll('.stats-number');

        const animateCounter = (counter) => {
            // Check if this is a storage counter with separate number and unit
            const storageNumber = counter.querySelector('.storage-number');
            const storageUnit = counter.querySelector('.storage-unit');

            if (storageNumber && storageUnit) {
                // For storage counter, animate only the number part
                const targetText = storageNumber.textContent.replace(/,/g, '');
                const target = parseFloat(targetText);
                const duration = 2000; // 2 seconds
                const step = target / (duration / 16); // 60fps
                let current = 0;

                const timer = setInterval(() => {
                    current += step;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }

                    // Update only the number part, keep the unit
                    storageNumber.textContent = current.toLocaleString('vi-VN', {
                        minimumFractionDigits: target % 1 !== 0 ? 1 : 0,
                        maximumFractionDigits: 1
                    });
                }, 16);
            } else {
                // For regular counters, animate the whole content
                const target = parseInt(counter.textContent.replace(/,/g, ''));
                const duration = 2000; // 2 seconds
                const step = target / (duration / 16); // 60fps
                let current = 0;

                const timer = setInterval(() => {
                    current += step;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }

                    // Format number with commas
                    counter.textContent = Math.floor(current).toLocaleString('vi-VN');
                }, 16);
            }
        };

        // Use Intersection Observer for performance
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounter(entry.target);
                    observer.unobserve(entry.target);
                }
            });
        });

        counters.forEach(counter => observer.observe(counter));
    }

    // Enhanced Card Hover Effects
    function initializeCardHoverEffects() {
        const cards = document.querySelectorAll('.stats-card, .chart-container, .pie-chart-container');

        cards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.12)';
                this.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.boxShadow = '';
            });
        });
    }

    // Modern Tooltips
    function initializeTooltips() {
        const tooltipElements = document.querySelectorAll('[data-bs-toggle="tooltip"]');

        tooltipElements.forEach(element => {
            new bootstrap.Tooltip(element, {
                customClass: 'modern-tooltip',
                delay: { show: 300, hide: 100 }
            });
        });
    }

    // Animated Progress Bars
    function initializeProgressBars() {
        const progressBars = document.querySelectorAll('.progress-bar');

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const progressBar = entry.target;
                    const width = progressBar.getAttribute('aria-valuenow');

                    progressBar.style.width = '0%';
                    setTimeout(() => {
                        progressBar.style.transition = 'width 1.5s ease-in-out';
                        progressBar.style.width = width + '%';
                    }, 100);

                    observer.unobserve(progressBar);
                }
            });
        });

        progressBars.forEach(bar => observer.observe(bar));
    }

    // Lazy Loading for Charts
    function initializeLazyLoading() {
        const chartContainers = document.querySelectorAll('canvas');

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('chart-loaded');
                    observer.unobserve(entry.target);
                }
            });
        });

        chartContainers.forEach(container => observer.observe(container));
    }

    // Smooth Scroll for Internal Links
    function initializeSmoothScroll() {
        const links = document.querySelectorAll('a[href^="#"]');

        links.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));

                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    // Real-time Clock Update
    function initializeRealTimeClock() {
        const timeElements = document.querySelectorAll('.real-time-clock');

        if (timeElements.length > 0) {
            setInterval(() => {
                const now = new Date();
                const timeString = now.toLocaleTimeString('vi-VN', {
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });

                timeElements.forEach(element => {
                    element.textContent = timeString;
                });
            }, 1000);
        }
    }

    // Enhanced Table Interactions
    function initializeTableEnhancements() {
        const tables = document.querySelectorAll('.eTable');

        tables.forEach(table => {
            const rows = table.querySelectorAll('tbody tr');

            rows.forEach(row => {
                row.addEventListener('click', function() {
                    // Remove active class from other rows
                    rows.forEach(r => r.classList.remove('table-row-active'));
                    // Add active class to clicked row
                    this.classList.add('table-row-active');
                });
            });
        });
    }

    // Notification System
    function showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-message">${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto remove
        setTimeout(() => {
            notification.remove();
        }, duration);

        // Manual close
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.remove();
        });
    }

    // Performance Monitoring
    function initializePerformanceMonitoring() {
        // Monitor page load time
        window.addEventListener('load', () => {
            const loadTime = performance.now();
            console.log(`Dashboard loaded in ${loadTime.toFixed(2)}ms`);

            // Show performance badge if load time is good
            if (loadTime < 2000) {
                const badge = document.createElement('div');
                badge.className = 'performance-badge';
                badge.textContent = '⚡ Fast Load';
                badge.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
                    color: white;
                    padding: 8px 16px;
                    border-radius: 20px;
                    font-size: 12px;
                    font-weight: 600;
                    z-index: 9999;
                    opacity: 0;
                    transition: opacity 0.3s ease;
                `;

                document.body.appendChild(badge);

                setTimeout(() => {
                    badge.style.opacity = '1';
                }, 100);

                setTimeout(() => {
                    badge.style.opacity = '0';
                    setTimeout(() => badge.remove(), 300);
                }, 3000);
            }
        });
    }

    // Initialize all enhancements
    initializeSmoothScroll();
    initializeRealTimeClock();
    initializeTableEnhancements();
    initializePerformanceMonitoring();

    // Expose utility functions globally
    window.DashboardUtils = {
        showNotification,
        animateCounter: initializeCounterAnimations
    };
});

// CSS for notifications
const notificationStyles = `
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        min-width: 300px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        z-index: 9999;
        transform: translateX(100%);
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border-left: 4px solid #667eea;
    }

    .notification.notification-success {
        border-left-color: #12c093;
    }

    .notification.notification-error {
        border-left-color: #dc3545;
    }

    .notification.notification-warning {
        border-left-color: #ffc107;
    }

    .notification-content {
        padding: 16px 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .notification-message {
        font-weight: 500;
        color: #2d3748;
    }

    .notification-close {
        background: none;
        border: none;
        font-size: 18px;
        color: #718096;
        cursor: pointer;
        padding: 0;
        margin-left: 12px;
    }

    .table-row-active {
        background-color: rgba(102, 126, 234, 0.1) !important;
        border-left: 3px solid #667eea;
    }
`;

// Inject styles
const styleSheet = document.createElement('style');
styleSheet.textContent = notificationStyles;
document.head.appendChild(styleSheet);
