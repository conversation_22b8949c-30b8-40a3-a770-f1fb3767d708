<svg width="1217" height="904" viewBox="0 0 1217 904" fill="none" xmlns="http://www.w3.org/2000/svg">
<g opacity="0.8">
<g filter="url(#filter0_f_31_395)">
<ellipse cx="384.384" cy="466.207" rx="344.384" ry="368.327" fill="url(#paint0_radial_31_395)"/>
</g>
<g filter="url(#filter1_f_31_395)">
<ellipse cx="791.787" cy="452" rx="385.213" ry="412" fill="url(#paint1_radial_31_395)"/>
</g>
</g>
<defs>
<filter id="filter0_f_31_395" x="0" y="57.8801" width="768.768" height="816.654" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="20" result="effect1_foregroundBlur_31_395"/>
</filter>
<filter id="filter1_f_31_395" x="366.574" y="0" width="850.426" height="904" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="20" result="effect1_foregroundBlur_31_395"/>
</filter>
<radialGradient id="paint0_radial_31_395" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(384.384 466.207) rotate(90) scale(368.327 344.384)">
<stop stop-color="#D1D6FF"/>
<stop offset="1" stop-color="#D7DBFF" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint1_radial_31_395" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(791.787 452) rotate(90) scale(412 385.213)">
<stop stop-color="#D1D6FF"/>
<stop offset="1" stop-color="#D1D6FF" stop-opacity="0"/>
</radialGradient>
</defs>
</svg>
