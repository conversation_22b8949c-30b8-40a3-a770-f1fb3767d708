.text-23px {
    font-size: 23px !important;
}

.text-20px {
    font-size: 20px !important;
}

.text-19px {
    font-size: 19px !important;
}

.text-18px {
    font-size: 18px !important;
}

.text-17px {
    font-size: 17px !important;
}

.text-16px {
    font-size: 16px !important;
}

.text-15px {
    font-size: 15px !important;
}

.text-14px {
    font-size: 14px !important;
}

.text-13px {
    font-size: 13px !important;
}

.text-12px {
    font-size: 12px !important;
}

.text-11px {
    font-size: 11px !important;
}

.text-10px {
    font-size: 10px !important;
}

.text-9px {
    font-size: 9px !important;
}

.text-8px {
    font-size: 8px !important;
}


.card_preview_text {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    height: 53px;
}

.card_preview_text_blog {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    height: 53px;
}

.card_preview_text_course {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    height: 78px;
}

.active-cat {
    color: var(--color-1) !important;
    font-weight: 600 !important
}


.child_category_menu {
    left: calc(100% + 0px);
    padding: 10px 0px;
    top: -5px !important;
}

.mega_list li a {
    border-radius: 5px;
}

.primary-menu li {
    padding: 4px 20px;
}

.mega_list li {
    padding: 0px 15px;
}

.main-mega-menu {
    left: 150px;
    padding: 17px 0px;
    transition: .3s;
}

.mega_list li a span i {
    margin-top: 5px;
    display: inline-block;
}

.cart-top-number {
    width: 16px;
    height: 16px;
    position: absolute;
    top: -14px;
    right: -8px;
    background-color: #d336e7;
    color: white;
    border-radius: 50px;
    padding: 0px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nice-select.form-select ul.list {
    width: auto;
    border-radius: 8px;
    border: 1px solid #fff;
    padding: 5px 5px;
    margin: 0px;
}

.nice-select.form-select ul li {
    border-radius: 5px;
    border: 1px solid #fff;
    padding: 2px 10px;
    margin: 0px;
}



/* Modal custom animation */
.fade-scale {
    transform: scale(0);
    opacity: 0;
    -webkit-transition: all .2s cubic-bezier(0.87, 0.94, 0.68, 1.1);
    -o-transition: all .2s cubic-bezier(0.87, 0.94, 0.68, 1.1);
    transition: all .2s cubic-bezier(0.87, 0.94, 0.68, 1.1);
}

.fade-scale.in {
    opacity: 1;
    transform: scale(1);
}

.fade-in-effect {
    opacity: 1;
    animation-name: fadeInOpacity;
    animation-iteration-count: 1;
    animation-timing-function: ease-in;
    animation-duration: .1s;
}

@keyframes fadeInOpacity {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

/* Modal custom animation Ended*/




/* 8 home page code start */


/* Common Css */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --skinColor1: #13BE95;
    --skinColor2: #5363D2;
    --skinColor3: #00907f;
    --skinColor4: #f95c16;
    --skinColor5: #264871;
    --skinColor6: #ff2458;
    --whiteColor: #FFF;
    --blackColor: #000;
}

body {
    padding: 0;
    margin: 0;
    color: #000;
    font-family: 'Inter';
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    background-color: #fff;
}

ul,
ol {
    margin: 0;
    padding: 0;
    list-style: none;
}

h1,
h2,
h3,
h4,
h5,
h6,
p,
label {
    padding: 0;
    margin: 0;
}

a {
    text-decoration: none;
    display: inline-block;
    color: inherit;
    transition: .3s;
    outline: none;
}

.btn:focus,
button:focus,
.form-control:focus {
    box-shadow: none;
}

button {
    border: none;
    outline: none;
}

img {
    max-width: 100%;
    max-height: 100%;
    display: block;
}

i[class^="fi-rr-"]:before,
i[class=" fi-rr-"]:before,
span[class^="fi-rr-"]:before,
span[class="fi-rr-"]:before {
    line-height: unset;
}

/* old */
.row-20 {
    row-gap: 20px;
    --bs-gutter-x: 20px;
}

.row-28 {
    row-gap: 28px;
    --bs-gutter-x: 28px;
}

.row-30 {
    row-gap: 30px;
    --bs-gutter-x: 30px;
}

/* new */
.g-20px {
    row-gap: 20px;
    --bs-gutter-x: 20px;
}

.g-28px {
    row-gap: 28px;
    --bs-gutter-x: 28px;
}

.gx-30px {
    --bs-gutter-x: 30px;
}

.gy-20px {
    row-gap: 20px;
}

.gy-30px {
    row-gap: 30px;
}

/* flex gap */
.gap-6px {
    gap: 6px !important;
}

.gap-12px {
    gap: 12px !important;
}

.gap-20px {
    gap: 20px !important;
}

.gap-30px {
    gap: 30px !important;
}

/* old */
.mb-10 {
    margin-bottom: 10px;
}

.mb-12 {
    margin-bottom: 12px;
}

.mb-14 {
    margin-bottom: 14px;
}

.mb-20 {
    margin-bottom: 20px;
}

.mb-24 {
    margin-bottom: 24px;
}

.mb-30 {
    margin-bottom: 30px;
}

.mb-40 {
    margin-bottom: 40px;
}

.mb-50 {
    margin-bottom: 50px;
}

.mb-60 {
    margin-bottom: 60px;
}

.mb-80 {
    margin-bottom: 80px;
}

.mb-100 {
    margin-bottom: 100px;
}

.mb-110 {
    margin-bottom: 110px;
}

/* new */
.mb-2px {
    margin-bottom: 2px !important;
}

.mb-4px {
    margin-bottom: 4px !important;
}

.mb-5px {
    margin-bottom: 5px !important;
}

.mb-6px {
    margin-bottom: 6px !important;
}

.mb-8px {
    margin-bottom: 8px !important;
}

.mb-10px {
    margin-bottom: 10px !important;
}

.mb-12px {
    margin-bottom: 12px !important;
}

.mb-14px {
    margin-bottom: 14px !important;
}

.mb-16px {
    margin-bottom: 16px !important;
}

.mb-18px {
    margin-bottom: 18px !important;
}

.mb-20px {
    margin-bottom: 20px !important;
}

.mb-26px {
    margin-bottom: 26px !important;
}

.mb-28px {
    margin-bottom: 28px !important;
}

.mb-30px {
    margin-bottom: 30px !important;
}

.mb-40px {
    margin-bottom: 40px !important;
}

.mb-50px {
    margin-bottom: 50px !important;
}

.mb-80px {
    margin-bottom: 80px !important;
}

.mb-100px {
    margin-bottom: 100px !important;
}

/* padding */
.p-10px {
    padding: 10px !important;
}

.p-12px {
    padding: 12px !important;
}

.p-14px {
    padding: 14px !important;
}

.p-16px {
    padding: 16px !important;
}

.p-18px {
    padding: 18px !important;
}

.p-20px {
    padding: 20px !important;
}

.p-26px {
    padding: 26px !important;
}

.p-40px {
    padding: 40px !important;
}

/* font size */
.fs-12px {
    font-size: 12px !important;
}

.fs-13px {
    font-size: 13px !important;
}

.fs-14px {
    font-size: 14px !important;
}

.fs-15px {
    font-size: 15px !important;
}

.fs-16px {
    font-size: 16px !important;
}

.fs-17px {
    font-size: 17px !important;
}

.fs-18px {
    font-size: 18px !important;
}

.fs-20px {
    font-size: 20px !important;
}

.fs-28px {
    font-size: 28px !important;
}

.fs-32px {
    font-size: 32px !important;
}

.fs-34px {
    font-size: 34px !important;
}

.fs-40px {
    font-size: 40px !important;
}

.fs-44px {
    font-size: 44px !important;
}

.fs-48px {
    font-size: 48px !important;
}

.fs-56px {
    font-size: 56px !important;
}

.fs-82px {
    font-size: 82px !important;
}


/* line hight */
.lh-normal {
    line-height: normal !important;
}

.lh-19px {
    line-height: 19px !important;
}

.lh-20px {
    line-height: 20px !important;
}

.lh-22px {
    line-height: 22px !important;
}

.lh-23px {
    line-height: 23px !important;
}

.lh-24px {
    line-height: 24px !important;
}

.lh-25px {
    line-height: 25px !important;
}

.lh-26px {
    line-height: 26px !important;
}

.lh-28px {
    line-height: 28px !important;
}

.lh-29px {
    line-height: 29px !important;
}

.lh-30px {
    line-height: 30px !important;
}

.lh-36px {
    line-height: 36px !important;
}

.lh-37px {
    line-height: 37px !important;
}

.lh-38px {
    line-height: 38px !important;
}

.lh-42px {
    line-height: 42px !important;
}

.lh-44px {
    line-height: 44px !important;
}

.lh-48px {
    line-height: 48px !important;
}

.lh-52px {
    line-height: 52px !important;
}

.lh-60px {
    line-height: 60px !important;
}

.lh-107px {
    line-height: 107px !important;
}

/* title */
.title-1 {

    color: #030531;
    font-weight: 500;
    line-height: 1.3;
}

.italic-1 {
    font-style: italic;
    background: linear-gradient(133deg, #264871 18.22%, #076785 89.38%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    padding-right: 3px;
}

.subtitle-1 {
    font-family: 'Rubik';
    font-weight: 400;
    color: #86868d;
    line-height: 1.5;
}

.title-2 {

    color: #062320;
    font-weight: 600;
    line-height: 1.3;
}

.title-3 {
    font-family: 'Lexend Deca';
    font-weight: 500;
    line-height: 1.3;
    color: #0f101a;
}

.subtitle-2 {
    font-family: 'Outfit';
    font-weight: 400;
    line-height: 1.5;
    color: #93949e;
}

.subtitle-3 {

    font-weight: 400;
    line-height: 1.5;
    color: #8f919b;
}

.subtitle-4 {

    font-weight: 400;
    line-height: 1.5;
    color: #838b95;
}

.title-4 {
    font-family: 'Lato';
    font-weight: 600;
    line-height: 1.3;
    color: #1d242d;
}

.title-5 {

    font-weight: 600;
    line-height: 1.3;
    color: #121421;
}

.subtitle-5 {

    font-weight: 400;
    line-height: 1.5;
    color: #93949e;
}

/* Text Color */
.text-danger-2 {
    color: var(--skinColor6);
}

.rounded-20px {
    border-radius: 20px !important;
}

.rounded-10px {
    border-radius: 10px !important;
}

/* ===================================================================
                            Header Footer CSS Start
====================================================================*/
/* Header Area Css Start ***
**************************/
.Login-btn-1 {
    border-radius: 10px;
    background: var(--skinColor1);

    font-weight: 600;
    font-size: 15px;
    color: #fff;
    padding: 12px 30px;
    transition: .3s;
}

.Login-btn-1:hover {
    background: #039572;
}

.header-wrap {
    padding: 27px 0;
}

.nav-wrap {
    column-gap: 50px;
}

.nav-area ul {
    column-gap: 50px;
}

.nav-area ul li a {

    font-size: 15px;
    font-weight: 500;
    transition: .3s;
    position: relative;
    color: #858c8a;
    z-index: 3;
}

.nav-area ul li a svg {
    display: none;
}

.nav-area nav>ul>li>a::after {
    position: absolute;
    content: " ";
    left: 0;
    bottom: -2px;
    height: 2px;
    width: 0px;
    background: var(--skinColor1);
    border-radius: 3px;
    transition: .3s;
    z-index: -1;
}

.nav-area ul li a.active,
.nav-area ul li a:hover {
    color: #0d221d;
}

.nav-area ul li a.active::after,
.nav-area ul li a:hover::after {
    width: 20px;
}

/* Dropdown */
.nav-area nav>ul>li {
    position: relative;
}

.nav-area ul li .menu-dropdown {
    position: absolute;
    border-radius: 10px;
    background: var(--whiteColor);
    box-shadow: 0px 8px 38px 0px rgba(16, 16, 16, 0.08);
    padding: 8px;
    min-width: 220px;
    left: 0;
    top: calc(100% + 25px);
    transform: translateX(-37%);
    visibility: hidden;
    opacity: 0;
    transition: .5s;
    z-index: 2;
}

.nav-area nav>ul li:hover .menu-dropdown {
    top: calc(100% + 16px);
    visibility: visible;
    opacity: 1;
}

.nav-area ul li .menu-dropdown a {
    padding: 8px;
    border-radius: 7px;
    width: 100%;
    transition: .3s;
}

.nav-area ul li .menu-dropdown a:hover {
    background: rgba(19, 190, 149, 0.08);
    color: var(--skinColor1);
}

.nav-area ul li .menu-dropdown::after {
    content: "";
    position: absolute;
    width: 0px;
    height: 0px;
    background: transparent;
    border: 17px solid transparent;
    border-bottom-color: var(--whiteColor);
    top: -34px;
    left: 50%;
    transform: translateX(-50%);
    filter: drop-shadow(0px -2px 38px rgba(16, 16, 16, 0.08));
}

/* Language Dropdown */
.language-dropdown .nice-select .list {
    border-radius: 10px;
    background: var(--whiteColor);
    box-shadow: 0px 8px 38px 0px rgba(16, 16, 16, 0.08);
    padding: 8px;
    min-width: 115px;
    left: -50%;
    transform: translateX(-10%);
}


.language-dropdown .nice-select .option.selected {
    font-weight: 500;
}

.language-dropdown .nice-select .option {

    color: #858C8A;
    font-size: 15px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    padding: 8px;
    border-radius: 7px;
}

/* Nice Select */
.language-dropdown .nice-select {
    margin: 0;
    padding-right: 26px;
    padding-left: 0;
    background: transparent;
    color: #192335;

    font-size: 15px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    border: none;
    display: flex;
    align-items: center;
}

.language-dropdown .nice-select:after {
    border: none;
    background-image: url(../image/angle-down-black-20.svg);
    background-repeat: no-repeat;
    background-size: 20px;
    transform-origin: 10px 0;
    right: 0px;
    height: 20px;
    width: 20px;
    top: 50%;
    transform: rotate(0deg) translateY(-50%);
    margin: 0;
    margin-top: 1px;
}

.language-dropdown .nice-select.open:after {
    transform: rotate(180deg) translateY(-50%);
}

/* Search */
.nav-btn-area .nav-search {
    margin-right: 20px;
}

.nav-search .search-form {
    right: 200px;
    top: 200px;
}

.nav-search .search-field {
    background-color: transparent;
    background-image: url(../image/search-black-20.svg);
    background-position: 94% 9px;
    background-repeat: no-repeat;
    background-size: 20px 20px;
    border: none;
    cursor: pointer;
    padding: 8px 14px;
    border-radius: 20px;
    position: relative;
    -webkit-transition: width 400ms ease, background 400ms ease;
    transition: width 400ms ease, background 400ms ease;
    width: 0px;
    cursor: pointer;

    color: #858C8A;

    font-size: 15px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
}

.nav-search .search-field::placeholder {
    color: #858C8A;
}

.nav-search .search-field:focus {
    background-color: var(--whiteColor);
    border: 1px solid #EDF0F7;
    cursor: text;
    outline: 0;
    width: 262px;
    color: #858C8A;
    background-image: url(../image/search-gray-20.svg);
    opacity: 1;
}

.nav-search .search-form .search-submit {
    display: none;
}

/* clears the ‘X’ from Internet Explorer */
.nav-search input[type=search]::-ms-clear {
    display: none;
    width: 0;
    height: 0;
}

.nav-search input[type=search]::-ms-reveal {
    display: none;
    width: 0;
    height: 0;
}

/* clears the ‘X’ from Chrome */
.nav-search input[type="search"]::-webkit-search-decoration,
.nav-search input[type="search"]::-webkit-search-cancel-button,
.nav-search input[type="search"]::-webkit-search-results-button,
.nav-search input[type="search"]::-webkit-search-results-decoration {
    display: none;
}

.nav-search input:-webkit-autofill,
.nav-search input:-webkit-autofill:focus {
    transition: background-color 600000s 0s, color 600000s 0s;
}

.nav-search input[data-autocompleted] {
    background-color: transparent !important;
}

.nav-btn-area .language-dropdown {
    padding: 0 30px 0 20px;
    position: relative;
}

.nav-btn-area .language-dropdown::after {
    position: absolute;
    content: "";
    height: 20px;
    width: 1px;
    background: #EDF0F7;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
}

/* Off Canvas Css Start */
.header-wrap .mobile-menu {
    border: none;
    padding: 0;
    background: transparent;
}

/* Off Canvas Css End */
/* Header Area Css End ***
************************/


/* Footer Area Css End ***
************************/
.footer-section {
    background: linear-gradient(180deg, #1e3764 0%, #192335 100%);
}

.footer-main-wrap {
    padding: 50px 0;
    column-gap: 20px;
    row-gap: 30px;
    justify-content: space-between;
    flex-wrap: wrap;
}

.footer-logo-area {
    max-width: 292px;
    width: 100%;
}

.footer-logo-area .info {

    font-weight: 500;
    font-size: 13px;
    line-height: 27px;
    color: #e4e4e4;
}

.footer-social-list {
    gap: 16px;
}

.footer-social-list li a {
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    width: 32px;
    height: 32px;
}

.footer-social-list li a:hover {
    border-color: #2F57EF;
}

.contact-us-btn1 {
    border-radius: 10px;
    border: 1px solid #2F57EF;
    padding: 12px 30px;
    display: flex;
    align-items: center;
    column-gap: 8px;

    font-weight: 600;
    font-size: 15px;
    color: var(--whiteColor);
    position: relative;
    z-index: 1;
    background: transparent;
    background-clip: padding-box;
    max-width: max-content;
    transition: .3s;
}

.contact-us-btn1:hover {
    color: #436aff;
}

.contact-us-btn1 svg {
    display: block;
}

.contact-us-btn1 path {
    transition: .3s;
}

.contact-us-btn1:hover path {
    fill: #436aff;
}

/* Footer Nav */
.footer-nav-wrap .title {
    font-family: 'Euclid Circular A';
    font-weight: 600;
    font-size: 18px;
    color: var(--whiteColor);
}

.footer-nav-wrap li a {

    font-weight: 400;
    font-size: 15px;
    color: #dedede;
    display: block;
}

.footer-nav-wrap li a:hover {
    color: #436aff;
}

.footer-nav-wrap li:not(:last-child) {
    margin-bottom: 16px;
}

.footer-contact-subscribe {
    max-width: 360px;
    width: 100%;
}

.footer-contact-area {
    margin-bottom: 36px;
}

.footer-contact-area .title {
    font-family: 'Euclid Circular A';
    font-weight: 600;
    font-size: 18px;
    color: var(--whiteColor);
}

.footer-contact-area .info {

    font-weight: 400;
    font-size: 15px;
    color: #dedede;
    word-break: break-all;
}

.footer-contact-area .info:not(:last-child) {
    margin-bottom: 16px;
}

.footer-newsletter-area .title {
    font-family: 'Euclid Circular A';
    font-weight: 600;
    font-size: 18px;
    color: var(--whiteColor);
}

.footer-newsletter-area .info {

    font-weight: 400;
    font-size: 15px;
    color: #dedede;
    margin-bottom: 16px;
}

.footer-search-group {
    width: 100%;
    position: relative;
}

.footer-search-group .form-control {
    padding: 10px 15px;

    font-weight: 400;
    font-size: 13px;
    color: #dedede;
    border-radius: 10px 0 0 10px;
    background: transparent;
    border: 1px solid #2f57ef;
    border-right: none;
}

.footer-search-group .form-control::placeholder {

    font-weight: 400;
    font-size: 13px;
    color: #dedede;
}

.footer-search-group .form-control:focus {
    color: #dedede;
    border-color: #2f57ef;
}

.footer-search-group .submit {
    border-radius: 0 10px 10px 0;
    background: linear-gradient(155deg, #2f57ef 0%, #c464fd 93.75%);

    font-weight: 600;
    font-size: 13px;
    color: #dedede;
    padding: 9px 25px 10px 24px;
    position: relative;
    overflow: hidden;
    position: relative;
    z-index: 1;
}

/* Footer Bottom */
.footer-bottom-area {
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.footer-bottom-wrap {
    padding: 30px 0;
    column-gap: 40px;
    row-gap: 10px;
}

.footer-bottom-nav {
    column-gap: 40px;
    row-gap: 7px;
}

.footer-bottom-nav a {

    font-weight: 400;
    font-size: 13px;
    color: #dedede;
}

.footer-bottom-nav a:hover {
    color: #436aff;
}

.footer-bottom-wrap .copyright {

    font-weight: 400;
    font-size: 13px;
    color: #dedede;
}

/* Footer Area Css End ***
************************/
/* ===================================================================
                            Header Footer CSS End
====================================================================*/


/* ===================================================================
                    Elegant Homepage 1 CSS Start
====================================================================*/
.home1-banner-content {
    padding: 30px 0;
}

.home1-banner-content .light {
    font-family: 'Inter';
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0.15em;
    color: var(--skinColor1);
    margin-bottom: 4px;
}

.home1-banner-content .title {
    font-family: 'Ubuntu';
    font-weight: 700;
    font-size: 48px;
    line-height: 64px;
    color: #0d221d;
    margin-bottom: 27px;
}

.home1-banner-content .title .highlight {
    color: var(--skinColor1);
    position: relative;
    display: inline-block;
}

.home1-banner-content .title .highlight::after {
    position: absolute;
    content: "";
    left: 7px;
    bottom: -7px;
    width: calc(100% + 11.69px);
    aspect-ratio: 219 / 14.64;
    background: url(../image/highlight-shape1.svg) no-repeat scroll center center / cover;
}

.home1-banner-content .info {
    font-family: 'Inter';
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: #858c8a;
}

.theme-btn1 {
    border-radius: 8px;
    padding: 12px 18px;
    background: var(--skinColor1);
    font-family: 'Inter';
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    color: var(--whiteColor);
    transition: .3s;
}

.theme-btn1:hover {
    background: #039572;
}

.border-btn1 {
    border: 1px solid var(--skinColor1);
    border-radius: 8px;
    padding: 11px 17px;
    font-family: 'Inter';
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    color: var(--skinColor1);
    transition: .3s;
}

.border-btn1:hover {
    background: var(--skinColor1);
    color: var(--whiteColor);
}

.home1-banner-content .buttons {
    gap: 12px;
}

.home1-banner-section {
    overflow: hidden;
    position: relative;
    z-index: 1;
}

.home1-banner-section:after {
    position: absolute;
    content: "";
    right: 0;
    top: 47.5px;
    width: 473px;
    aspect-ratio: 473 / 342;
    background: url(../image/home1-banner-shape1.svg) no-repeat scroll center center / cover;
}

.home1-banner-section::before {
    position: absolute;
    content: "";
    left: 0;
    top: 143px;
    width: 155px;
    aspect-ratio: 340 / 454;
    background: url(../image/home1-banner-shape1.png) no-repeat scroll center center / cover;
    z-index: -1;
}

/* Crash Course */
/* Section Title */
.home1-section-title {
    max-width: 621px;
    width: 100%;
    margin: 0 auto 30px auto;
}

.home1-section-title .title {
    font-family: 'Ubuntu';
    font-weight: 500;
    font-size: 32px;
    line-height: 36px;
    color: #0d221d;
    text-align: center;
}

.home1-section-title .info {
    font-family: 'Inter';
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    text-align: center;
    color: #858c8a;
    text-align: center;
}

/* Course Card */
.course-card1-link {
    display: block;
    width: 100%;
    height: 100%;
    border-radius: 12px;
    padding: 14px;
    box-shadow: 0 14px 32px 0 rgba(147, 148, 158, 0.2);
    background: var(--whiteColor);
}

.course-card1-link .banner {
    width: 100%;
    aspect-ratio: 242 / 190;
    margin-bottom: 14px;
}

.course-card1-link .banner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
}

.course-card1-details .rating-reviews {
    gap: 6px;
    margin-bottom: 4px;
}

.course-card1-details .rating-reviews .rating {
    gap: 4px;
}

.course-card1-details .rating-reviews .reviews {
    font-family: 'Inter';
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #858c8a;
}

.course-card1-details .title-info {
    margin-bottom: 12px;
}

.course-card1-details .title-info .title {
    font-family: 'Ubuntu';
    font-weight: 500;
    font-size: 20px;
    line-height: 28px;
    color: #0d221d;
    margin-bottom: 2px;
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}

.course-card1-details .title-info .info {
    font-family: 'Inter';
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    color: #858c8a;
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}

.course-card1-leasons-students {
    column-gap: 12px;
    row-gap: 8px;
}

.course-card1-leasons-students .leasons-students {
    column-gap: 4px;
    margin-bottom: 8px;
}

.course-card1-leasons-students .leasons-students .total {
    font-family: 'Inter';
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #858c8a;
}

.course-card1-author-price {
    column-gap: 20px;
}

.course-card1-author-price .author {
    column-gap: 8px;
}

.course-card1-author-price .author .profile {
    width: 30px;
    height: 30px;
}

.course-card1-author-price .author .profile img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.course-card1-author-price .author .name {
    font-family: 'Ubuntu';
    font-weight: 500;
    font-size: 12px;
    line-height: 16px;
    color: #0d221d;
}

.course-card1-author-price .prices .new-price {
    font-family: 'Inter';
    font-weight: 700;
    font-size: 20px;
    line-height: 28px;
    color: var(--skinColor1);
    text-align: right;
}

.course-card1-author-price .prices .old-price {
    font-family: 'Inter';
    font-weight: 600;
    font-size: 14px;
    line-height: 20px;
    text-decoration: line-through;
    color: #858c8a;
    text-align: right;
}

/* Why Choose */
.why-choose-section1 {
    background: url(../image/choose1-background.svg) no-repeat scroll center center / cover;
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.why-choose-section1::after {
    position: absolute;
    content: "";
    right: 0;
    top: 39.09px;
    width: 482px;
    aspect-ratio: 482 / 263;
    background: url(../image/choose-shape-1.svg) no-repeat scroll center center / cover;
    z-index: -1;
}

.why-choose-section1::before {
    position: absolute;
    content: "";
    left: 0;
    bottom: 39.09px;
    width: 482px;
    aspect-ratio: 482 / 263;
    background: url(../image/choose-shape-2.svg) no-repeat scroll center center / cover;
    z-index: -1;
}

.why-choose-area1 {
    padding: 60px 0px;
}

.why-choose-area1>.title {
    font-family: 'Ubuntu';
    font-weight: 700;
    font-size: 32px;
    line-height: 36px;
    color: #0d221d;
    text-align: center;
}

.why-choose-wrap1 {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    row-gap: 30px;
}

.why-choose1-single {
    justify-self: center;
    position: relative;
    width: 100%;
}

.why-choose1-single:not(:last-child):after {
    position: absolute;
    content: "";
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    height: 77px;
    width: 1px;
    background: rgba(133, 140, 138, 0.3);
}

.why-choose1-single .total {
    font-family: 'Ubuntu';
    font-weight: 700;
    font-size: 72px;
    line-height: 76px;
    color: #0d221d;
    margin-bottom: 12px;
    text-align: center;
}

.why-choose1-single .info {
    font-family: 'Inter';
    font-weight: 500;
    font-size: 18px;
    line-height: 28px;
    color: #0d221d;
    text-align: center;
}

/* Testimonial */
.elegant-testimonial-title {
    margin-bottom: 0;
}

.elegant-testimonial-2 {
    padding-top: 0px;
    padding-bottom: 30px;
}

.elegant-testimonial-1 {
    padding-top: 30px;
    padding-bottom: 16px;
}

.elegant-testimonial-2 .swiper-slide {
    height: auto;
}

.elegant-testimonial-1 .swiper-slide {
    height: auto;
}

.elegant-testimonial-slide {
    background: var(--whiteColor);
    box-shadow: 0px 14px 32px rgba(147, 148, 158, 0.2);
    border-radius: 12px;
    padding: 24px;
    width: 100%;
    height: 100%;
}

.ele-testimonial-profile-area {
    column-gap: 12px;
    margin-bottom: 16px;
}

.ele-testimonial-profile-area .profile {
    min-width: 64px;
    width: 64px;
    height: 64px;
}

.ele-testimonial-profile-area .profile img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.ele-testimonial-profile-name .name {
    font-family: 'Ubuntu';
    font-weight: 700;
    font-size: 16px;
    line-height: 24px;
    color: #0d221d;
}

.ele-testimonial-profile-name .time {
    font-family: 'Inter';
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    color: #858c8a;
    margin-bottom: 2px;
}

.ele-testimonial-profile-name .rating {
    column-gap: 5.32px;
}

.elegant-testimonial-slide .review {
    font-family: 'Inter';
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: #858c8a;
}


/* QNA Accordion */
.qnaaccordion-one .accordion-item {
    border: none;
}

.qnaaccordion-one .accordion-item:not(:last-child) {
    border-bottom: 1px solid rgba(133, 140, 138, 0.3);
}

.qnaaccordion-one .accordion-item,
.qnaaccordion-one .accordion-button {
    border-radius: 0;
}

.qnaaccordion-one .accordion-button {
    padding: 0;
    padding-right: 17px;
    transition: .3s;
    font-family: 'Ubuntu';
    font-weight: 700;
    font-size: 18px;
    line-height: 28px;
    color: #0d221d;
}

.qnaaccordion-one .accordion-item:not(:last-child) .accordion-button {
    padding-bottom: 20px;
}

.qnaaccordion-one .accordion-item:not(:first-child) .accordion-button {
    padding-top: 20px;
}

.qnaaccordion-one .accordion-button:focus {
    box-shadow: none;
}

.qnaaccordion-one .accordion-button:not(.collapsed) {
    background-color: inherit;
    color: #0d221d;
    box-shadow: none;
    padding-bottom: 12px !important;
}

.qnaaccordion-one .accordion-body {
    padding: 0;
    padding-bottom: 28px;
    padding-right: 30px;
}

.qnaaccordion-one .accordion-body .answer {
    font-family: 'Inter';
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: #858c8a;
}

.qnaaccordion-one .accordion-button::after {
    background-size: 15px;
    background-image: url(../image/plus-black-15.svg);
    width: 15px;
    height: 15px;
}

.qnaaccordion-one .accordion-button:not(.collapsed)::after {
    background-image: url(../image/minus-black-15.svg);
}

/* Blog Post */
.blog-post1-link {
    display: block;
    width: 100%;
    height: 100%;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 14px 32px 0 rgba(147, 148, 158, 0.2);
    background: var(--whiteColor);
}

.blog-post1-link .banner {
    width: 100%;
    aspect-ratio: 335 / 230;
    margin-bottom: 14px;
}

.blog-post1-link .banner img {
    width: 100%;
    height: 100%;
    border-radius: 8px;
}

.blog-post1-details .title {
    font-family: 'Ubuntu';
    font-weight: 700;
    font-size: 18px;
    line-height: 28px;
    color: #121421;
    margin-bottom: 8px;
}

.blog-post1-details .info {
    font-family: 'Inter';
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: #93949e;
    margin-bottom: 16px;
}

.blog-post1-details .read-more {
    column-gap: 4px;
    font-family: 'Inter';
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: #121421;
}

.blog-post1-details .read-more img {
    transition: .3s;
}

.blog-post1-link:hover .read-more img {
    margin-left: 5px;
}

/* Download */
.home1-download-section {
    background: #e6f8f4;
}

.home1-download-area {
    padding: 60px 0;
    max-width: 547px;
    width: 100%;
    margin: 0 auto;
}

.home1-download-area .title {
    font-family: 'Ubuntu';
    font-weight: 700;
    font-size: 36px;
    line-height: 40px;
    text-align: center;
    color: #0d221d;
    margin-bottom: 16px;
}

.home1-download-area .info {
    font-family: 'Inter';
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    text-align: center;
    color: #0d221d;
}

.home1-download-area .buttons {
    gap: 12px;
}

.home1-download-area .buttons .theme-btn1 {
    min-width: 124px;
}

/* ===================================================================
                    Elegant Homepage 1 CSS End
====================================================================*/



/* ===================================================================
                        Meditation CSS Start
====================================================================*/
.maditation-banner-typography {
    max-width: 1083px;
    margin: 27px auto 50px auto;
}

.maditation-banner-content {
    justify-content: space-between;
}

.maditation-banner-left {
    margin-top: 45px;
    margin-bottom: 20px;
}

.maditation-banner-left .info {
    max-width: 359px;
    font-family: 'Manrope';
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: #49494A;
    margin-bottom: 32px;
}

.explore-btn1 {
    display: flex;
    align-items: center;
    max-width: max-content;
}

.explore-btn1 .text {
    font-family: 'Manrope';
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: var(--whiteColor);
    padding: 7px 23px;
    border-radius: 35px;
    background: #121314;
    border: 1px solid #121314;
    transition: .3s;
}

.explore-btn1 .icon {
    height: 40px;
    width: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #121314;
    border: 1px solid #121314;
    transition: .3s;
}

.explore-btn1:hover .text {
    background: #4e4e4e;
    border-color: #4e4e4e;
}

.explore-btn1:hover .icon {
    background: #4e4e4e;
    border-color: #4e4e4e;
}

.maditation-banner-image {
    max-width: 582px;
    width: 100%;
    margin-left: -80px;
}

.maditation-banner-image img {
    width: 100%;
}

.maditation-banner-right {
    display: flex;
    flex-direction: column;
    justify-content: end;
    margin-left: 13.6px;
}

.maditation-video-profiles {
    margin-bottom: 12px;
}

.maditation-video-profiles li {
    height: 45px;
    width: 45px;
    border-radius: 50%;
}

.maditation-video-profiles li img {
    height: 100%;
    width: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.maditation-video-profiles li:not(:first-child) {
    margin-left: -13px;
}

.maditation-video-profiles-last {
    position: relative;
}

.maditation-video-profiles-last .more-options {
    position: absolute;
    content: "";
    top: 50%;
    left: 50%;
    transform: translateY(-50%) translateX(-50%);
}

.maditation-video-profiles-last .more {
    font-family: 'Manrope';
    font-weight: 800;
    font-size: 14px;
    line-height: 22px;
    color: var(--whiteColor);
}

.maditation-video-profiles-last .item {
    font-family: 'Manrope';
    font-weight: 500;
    font-size: 9px;
    line-height: 11px;
    color: var(--whiteColor);
    margin-top: -6px;
}

.maditation-class-participant {
    column-gap: 24px;
    row-gap: 12px;
}

.maditation-class-participant .total {
    font-family: 'Manrope';
    font-weight: 800;
    font-size: 36px;
    line-height: 40px;
    color: #121314;
}

.maditation-class-participant .info {
    font-family: 'Manrope';
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: #121314;
}

.maditation-beginner-lesson {
    border-radius: 65px;
    padding: 10px 32px;
    width: 100%;
    border: 1px solid #0BF496;
    margin-bottom: 90px;
}

.maditation-beginner-lesson .total {
    font-family: 'Manrope';
    font-weight: 800;
    font-size: 36px;
    line-height: 40px;
    color: #121314;
}

.maditation-beginner-lesson .info {
    font-family: 'Manrope';
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: #121314;
}

/* Harmony */
.harmony-main-section {
    background: #141318;
    padding: 80px 0 114px 0;
    position: relative;
    z-index: 1;
}

.harmony-main-section::after {
    position: absolute;
    content: "";
    top: 0;
    right: 0;
    width: 300px;
    aspect-ratio: 516 / 342;
    background: url(../images/shape/harmoney-section-shadow1.svg) no-repeat scroll center center / cover;
    z-index: -1;
}

.harmony-main-section::before {
    position: absolute;
    content: "";
    bottom: 0;
    left: 0;
    width: 320px;
    aspect-ratio: 553 / 299;
    background: url(../images/shape/harmoney-section-shadow2.svg) no-repeat scroll center center / cover;
    z-index: -1;
}

.harmony-title-area {
    column-gap: 30px;
    row-gap: 16px;
    margin-bottom: 24px;
}

.harmony-title-area .title {
    font-family: 'Mica Valo';
    font-weight: 400;
    font-size: 48px;
    line-height: 60px;
    color: var(--whiteColor);
    max-width: 50%;
    width: 100%;
}

@media all and (max-width:767px) {
    .harmony-title-area .title {
        max-width: 100%;
    }
}

.explore-btn2 {
    display: flex;
    max-width: max-content;
}

.explore-btn2 .text {
    border: 1px solid var(--whiteColor);
    border-radius: 35px;
    padding: 7px 23px;
    font-family: 'Manrope';
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: var(--whiteColor);
    transition: .3s;
}

.explore-btn2 .icon {
    height: 40px;
    width: 40px;
    border-radius: 50%;
    border: 1px solid var(--whiteColor);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: .3s;
}

.explore-btn2:hover .text {
    border-color: #666666;
}

.explore-btn2:hover .icon {
    border-color: #666666;
}

.single-harmony-yoga {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
}

.single-harmony-yoga .banner {
    width: 100%;
    aspect-ratio: 263 / 263;
}

.single-harmony-yoga .banner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 12px;
}

.single-harmony-yoga .overlay {
    position: absolute;
    height: 100%;
    width: 100%;
    border-radius: 12px;
    background: url(../image/harmony-yoga-overlay.svg) no-repeat scroll center center / cover;
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    left: 0;
    top: 100%;
    transition: .3s;
}

.single-harmony-yoga .overlay .name {
    font-family: 'Mica Valo';
    font-weight: 400;
    font-size: 24px;
    line-height: 32px;
    color: var(--blackColor);
    text-align: center;
}

.single-harmony-yoga.active .overlay,
.single-harmony-yoga:hover .overlay {
    top: 0;
}

/* Pose's Journey */
.posesjourney-title-area {
    column-gap: 30px;
    row-gap: 16px;
    margin-bottom: 24px;
}

.posesjourney-title-area .title {
    font-family: 'Mica Valo';
    font-weight: 400;
    font-size: 48px;
    line-height: 60px;
    color: var(--blackColor);
    max-width: 643px;
    width: 100%;
}

.single-posesjourney-yoga {
    border-radius: 16px;
    position: relative;
    overflow: hidden;
}

.single-posesjourney-yoga .banner {
    width: 100%;
    aspect-ratio: 360 / 360;
    border-radius: 16px;
}

.single-posesjourney-yoga .banner img {
    width: 100%;
    height: 100%;
    border-radius: 16px;
    object-fit: cover;
}

.single-posesjourney-yoga .price {
    position: absolute;
    right: 16px;
    top: 16px;
    border-radius: 8px;
    padding: 4px 8px;
    background: #121314;
    font-family: 'Manrope';
    font-weight: 700;
    font-size: 16px;
    line-height: 24px;
    color: var(--whiteColor);
    z-index: 1;
}

.single-posesjourney-yoga .overlay {
    position: absolute;
    width: 100%;
    height: 260px;
    max-height: calc(100% - 50px);
    border-radius: 16px;
    left: 0;
    bottom: 0;
    display: flex;
    align-items: flex-end;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    background-image: var(--bgShape);
    padding: 16px;
    bottom: -300px;
    transition: bottom 0.3s;
}

.single-posesjourney-yoga:hover .overlay {
    bottom: -35px;
    transition: bottom 0.3s;
}

.posejourney-overley {
    width: 100%;
    column-gap: 16px;
}

.posejourney-overley .title-area .title {
    font-family: 'Mica Valo';
    font-weight: 400;
    font-size: 24px;
    line-height: 32px;
    color: var(--blackColor);
    margin-bottom: 4px;
}

.posejourney-overley .title-area .info {
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: #121314;
    font-family: 'Manrope';
}

.posejourney-overley .icon {
    height: 44px;
    min-width: 44px;
    width: 44px;
    border-radius: 50%;
    background: var(--whiteColor);
    display: flex;
    justify-content: center;
    align-items: center;
    transform: translateX(62px);
    transition: .3s;
    visibility: hidden;
    opacity: 0;
}

.single-posesjourney-yoga:hover .posejourney-overley .icon {
    visibility: visible;
    opacity: 1;
    transform: translateX(0px);
}

/* Yoga Benefti */
.yoga-benefit-area {
    background: #fff8f6;
    border-radius: 16px;
    padding: 48px 36px 21px 36px;
    position: relative;
    z-index: 1;
}

.yoga-benefit-area::after {
    position: absolute;
    content: "";
    top: 0;
    right: 0;
    border-top-right-radius: 16px;
    width: 197px;
    aspect-ratio: 197 / 341;
    background: url(../image/yoga-benefit-shadow2.svg) no-repeat scroll center center / cover;
    z-index: -1;
    transform: rotate(180deg);
    filter: opacity(0.3);
    border-radius: 0px 0px 0px 16px;
}

.yoga-benefit-area::before {
    position: absolute;
    content: "";
    left: 0;
    bottom: 0;
    border-bottom-left-radius: 16px;
    width: 170px;
    aspect-ratio: 168 / 365;
    background: url(../image/yoga-benefit-shadow1.svg) no-repeat scroll center center / cover;
    z-index: -1;
    transform: rotate(180deg);
    filter: opacity(0.5);
    border-radius: 0px 16px 0px 0px;
}

.yoga-benefit-area>.title {
    font-family: 'Mica Valo';
    font-weight: 400;
    font-size: 48px;
    line-height: 60px;
    text-align: center;
    color: var(--blackColor);
    margin-bottom: 4px;
}

.yoga-benefits-wrap {
    column-gap: 42px;
    row-gap: 42px;
}

.yoga-benefit-banner {
    max-width: 280px;
    width: 100%;
}

.yoga-benefit-list li {
    display: flex;
    align-items: flex-start;
    column-gap: 8px;
}

.yoga-benefit-list li:not(:last-child) {
    margin-bottom: 32px;
}

.yoga-benefit-image {
    height: 56px;
    min-width: 56px;
    width: 56px;
    border-radius: 8px;
    background: #ffedd1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.yoga-benefit-details>.title {
    font-family: 'Mica Valo';
    font-weight: 400;
    font-size: 18px;
    line-height: 24px;
    color: var(--blackColor);
    margin-bottom: 8px;
}

.yoga-benefit-details>.info {
    font-family: 'Manrope';
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    color: #49494a;
}

.yoga-benefit-left .title {
    text-align: right;
}

.yoga-benefit-left .info {
    text-align: right;
}

/* True Self */
.true-self-details .title {
    font-family: 'Mica Valo';
    font-weight: 400;
    font-size: 48px;
    line-height: 60px;
    color: #000;
    margin-bottom: 32px;
}

.true-self-details .info {
    font-family: 'Manrope';
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: #5e6168;
}

.true-self-users {
    margin-bottom: 12px;
}

.true-self-users li:not(:first-child) {
    margin-left: -8px;
}

.true-self-users li {
    min-width: 32px;
    width: 32px;
    height: 32px;
}

.true-self-users li img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

/* Video */
.true-self-video {
    width: 100%;
    aspect-ratio: 360 / 308;
}

.true-self-video .plyr--video {
    height: 100%;
    width: 100%;
    border-radius: 24px;
}

.true-self-video .plyr__control--overlaid {
    background: var(--whiteColor);
    color: var(--blackColor);
    outline: 14px solid rgba(38, 38, 38, 0.26);
}

.true-self-video .plyr__control svg {
    height: 14px;
    width: 14px;
}

.true-self-video .plyr--video .plyr__control:hover {
    background: var(--whiteColor);
    color: var(--blackColor);
}

.true-self-video .plyr--full-ui input[type=range] {
    color: var(--whiteColor);
}

.true-self-banners .banner {
    width: 100%;
    aspect-ratio: 262 / 100;
}

.true-self-banners .banner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 100px;
}

.true-self-banners .banner:not(:last-child) {
    margin-bottom: 4px;
}

/* Pricing */
.pricing-area-title {
    font-family: 'Mica Valo';
    font-weight: 400;
    font-size: 48px;
    line-height: 60px;
    color: #000;
    margin-bottom: 24px;
    text-align: center;
}

.single-yoga-pricing {
    border-radius: 20px;
    padding: 24px 24px 40px 24px;
    box-shadow: 2px 7px 10px 0 rgba(0, 0, 0, 0.06);
    background: var(--whiteColor);
    overflow: hidden;
}

.single-yoga-pricing .plan {
    font-family: 'Manrope';
    font-weight: 600;
    font-size: 24px;
    line-height: 32px;
    color: #121314;
    margin-bottom: 16px;
}

.single-yoga-pricing .info {
    font-family: 'Manrope';
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    color: #121314;
    margin-bottom: 16px;
}

.single-yoga-pricing .price {
    font-family: 'Manrope';
    color: #121314;
    font-weight: 800;
    font-size: 48px;
    line-height: 60px;
    display: flex;
    align-items: flex-start;
}

.single-yoga-pricing .price .symble {
    font-size: 30px;
    line-height: 48px;
}

.get-start-btn1 {
    display: block;
    width: 100%;
    padding: 7px 23px;
    text-align: center;
    border: 1px solid #121314;
    border-radius: 35px;
    font-family: 'Manrope';
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: #121314;
    transition: .3s;
}

.get-start-btn1:hover {
    background: #121314;
    color: var(--whiteColor);
}

.get-start-btn2 {
    display: block;
    width: 100%;
    padding: 7px 23px;
    text-align: center;
    border: 1px solid #121314;
    border-radius: 35px;
    font-family: 'Manrope';
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    transition: .3s;
    background: #121314;
    color: var(--whiteColor);
}

.get-start-btn2:hover {
    color: #121314;
    background: var(--whiteColor);
}

.yoga-pricing-details-list {
    margin-top: 20px;
}

.yoga-pricing-details-list li:not(:last-child) {
    margin-bottom: 24px;
}

.yoga-pricing-details-list li {
    padding-left: 28px;
    position: relative;
    font-family: 'Manrope';
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    color: #121314;
}

.yoga-pricing-details-list li::after {
    position: absolute;
    content: "";
    left: 0;
    top: 0;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: url(../image/checked-sky-20.svg) no-repeat scroll center center / cover;
}

.single-yoga-pricing.premium {
    box-shadow: none;
    position: relative;
    z-index: 1;
}

.single-yoga-pricing.premium::after {
    position: absolute;
    content: "";
    right: -163px;
    top: -195px;
    border-radius: 100%;
    background: radial-gradient(50.32% 50.69% at 47.23% 46.33%, rgb(57, 219, 254) 0%, rgb(220, 253, 255) 100%);
    filter: blur(10px);
    filter: blur(100px);
    width: 245px;
    height: 615px;
    z-index: -1;
    transform: rotate(-10deg);
}

.single-yoga-pricing.premium::before {
    position: absolute;
    content: "";
    left: -135px;
    bottom: -141px;
    border-radius: 100%;
    background: radial-gradient(56.25% 50.4% at 51.2% 47.2%, rgb(255, 99, 99) 0%, rgb(255, 207, 207) 100%);
    filter: blur(15.300000190734863px);
    filter: blur(150px);
    width: 181px;
    height: 478px;
    z-index: -1;
    transform: rotate(-20deg);
}

.yoga-pricing-details-list.premium li::after {
    background: url(../image/checked-black-20.svg) no-repeat scroll center center / cover;
}

.meditation-blog-title-area {
    column-gap: 24px;
    row-gap: 16px;
    margin-bottom: 24px;
}

.meditation-blog-title-area .title {
    font-family: 'Mica Valo';
    font-weight: 400;
    font-size: 48px;
    line-height: 60px;
    color: #000;
}

/* Blog */
.meditation-blog-link {
    display: block;
    width: 100%;
    height: 100%;
}

.meditation-blog-inner .banner {
    width: 100%;
    aspect-ratio: 360 / 280;
    margin-bottom: 12px;
}

.meditation-blog-inner .banner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
}

.meditation-blog-details .title {
    font-family: 'Mica Valo';
    font-weight: 400;
    font-size: 20px;
    line-height: 28px;
    line-height: 1.4;
    color: #121314;
    margin-bottom: 8px;
}

.meditation-blog-details .info {
    font-family: 'Manrope';
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: #49494a;
    margin-bottom: 16px;
}

.meditation-blog-details .read-more {
    column-gap: 8px;
    font-family: 'Inter';
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: #121314;
}

.meditation-blog-details .read-more img {
    transition: .3s;
}

.meditation-blog-link:hover .read-more img {
    margin-left: 5px;
}

/* ===================================================================
                        Meditation CSS End
====================================================================*/


/* ===================================================================
                        Cooking CSS Start
====================================================================*/
.cooking-banner-section {
    overflow: hidden;
}

.cooking-banner-details {
    margin-top: 100px;
    margin-bottom: 130px;
    position: relative;
    z-index: 1;
}

.cooking-banner-details::after {
    position: absolute;
    content: "";
    left: -266px;
    top: -223px;
    width: 541px;
    aspect-ratio: 541 / 541;
    border-radius: 100%;
    background: radial-gradient(61.56% 61.56% at 56.49% 50%, rgb(0, 144, 127) 0%, rgb(0, 144, 127) 100%);
    filter: blur(200px);
    z-index: -1;
    opacity: 0.09;
}

.cooking-banner-details .light {
    font-family: 'Mulish';
    font-weight: 700;
    font-size: 14px;
    letter-spacing: 0.15em;
    color: var(--skinColor3);
    margin-bottom: 10px;
}

.cooking-banner-details .title {
    font-family: 'Mulish';
    font-weight: 700;
    font-size: 52px;
    line-height: 50px;
    text-transform: capitalize;
    color: #062320;
    max-width: 513px;
    width: 100%;
}

.cooking-banner-details .title .highlight {
    font-family: 'Mulish';
    font-weight: 800;
    font-size: 58px;
    line-height: 78px;
    text-transform: uppercase;
    color: var(--skinColor3);
}

.cooking-banner-details .title .small {
    font-family: 'Mulish';
    font-weight: 700;
    font-size: 41px;
    line-height: 55px;
    text-transform: capitalize;
    color: #062320;
}

.cooking-banner-details .info {

    font-weight: 400;
    font-size: 15px;
    line-height: 24px;
    color: #909090;
}

.rectangle-btn1 {
    box-shadow: 0 10px 18px 0 rgba(0, 144, 127, 0.25);
    background: linear-gradient(166deg, #00907f 0%, #006e61 100%);
    padding: 14px 26px;
    font-family: 'Mulish';
    font-weight: 600;
    font-size: 15px;
    color: #fff;
    transition: .3s;
    position: relative;
    overflow: hidden;
    z-index: 1;
    display: block;
    max-width: max-content;
}

.rectangle-btn1::before {
    content: "";
    position: absolute;
    left: 0px;
    top: 0px;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(35, 101, 96, 1) 0%, rgba(58, 131, 126, 1) 45%, rgba(58, 131, 126, 1) 55%, rgba(35, 101, 96, 1) 100%);
    border-radius: inherit;
    opacity: 0;
    z-index: -1;
    transition: .3s;
}

.rectangle-btn1:hover:before {
    opacity: 1;
}

.cooking-banner-sponsors {
    column-gap: 54.5px;
    row-gap: 30px;
    margin-top: 50px;
}

.cooking-banner-image {
    max-width: 517px;
    width: 100%;
    margin-top: 68px;
    margin-bottom: 60px;
    position: relative;
}

.cooking-banner-image::after {
    position: absolute;
    content: "";
    top: -5px;
    right: -99px;
    width: 369px;
    aspect-ratio: 369 / 369;
    border-radius: 100%;
    background: radial-gradient(61.56% 61.56% at 56.49% 50%, rgb(0, 144, 127) 0%, rgb(0, 144, 127) 100%);
    filter: blur(50px);
    opacity: 0.1;
    z-index: -1;
}

.cooking-banner-image::before {
    position: absolute;
    content: "";
    bottom: 9.5px;
    left: -100px;
    width: 461px;
    aspect-ratio: 461 / 461;
    border-radius: 100%;
    background: radial-gradient(61.56% 61.56% at 56.49% 50%, rgb(0, 144, 127) 0%, rgb(0, 144, 127) 100%);
    filter: blur(50px);
    opacity: 0.1;
    z-index: -1;
}

/* Service */
.cooking-services-area {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    row-gap: 50px;
}

.cooking-single-service {
    text-align: center;
    position: relative;
    padding: 0 24px;
}

.cooking-single-service img {
    text-align: center;
    margin: 0 auto 30px auto;
}

.cooking-single-service .title {
    font-family: 'Mulish';
    font-weight: 700;
    font-size: 18px;
    line-height: 25px;
    color: #062320;
    max-width: 264px;
    margin: 0 auto 15px auto;
}

.cooking-single-service .info {

    font-weight: 400;
    font-size: 15px;
    line-height: 24px;
    text-align: center;
    color: #909090;
    max-width: 264px;
    margin: 0 auto;
}

.cooking-single-service:not(:last-child)::after {
    position: absolute;
    content: "";
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    height: 118px;
    width: 1px;
    background: #909090;
    opacity: 0.3;
}

/* Section Title */
.cooking-section-title {
    max-width: 565px;
    margin: 0 auto 50px auto;
}

.cooking-section-title>.title {
    font-family: 'Mulish';
    font-weight: 700;
    font-size: 32px;
    line-height: 42px;
    color: #062320;
    text-align: center;
}

.cooking-section-title>.info {

    font-weight: 400;
    font-size: 15px;
    line-height: 24px;
    text-align: center;
    color: #909090;
}

/* Course Card */
.cooking-course-link {
    display: block;
    width: 100%;
    height: 100%;
}

.cooking-course-card {
    box-shadow: 0 14px 32px 0 rgb(147 148 158 / 16%);
    background: var(--whiteColor);
    padding: 16px;
    height: 100%;
    transition: box-shadow 0.3s;
}
.cooking-course-card:hover{
    box-shadow: 0 14px 32px 0 rgba(96, 97, 109, 0.247);
    transition: box-shadow 0.3s;
}

.cooking-course-card .banner {
    width: 100%;
    aspect-ratio: 232 / 157;
    margin-bottom: 16px;
}

.cooking-course-card .banner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cooking-course-card-body>.title {
    font-family: 'Mulish';
    font-weight: 700;
    font-size: 18px;
    line-height: 26px;
    color: #171719;
    margin-bottom: 10px;
}

.cooking-course-card-body .time-rating {
    gap: 10px;
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(144, 144, 144, 0.3);
}

.cooking-course-card-body .time-wrap {
    gap: 6px;
}

.cooking-course-card-body .time-wrap .time {

    font-weight: 400;
    font-size: 13px;
    line-height: 26px;
    color: #909090;
}

.cooking-course-card-body .rating-wrap {
    gap: 5px;
}

.cooking-course-card-body .rating-wrap .rating {
    font-family: 'Mulish';
    font-weight: 700;
    font-size: 15px;
    line-height: 26px;
    color: #062320;
}

.cooking-course-card-body .author-price {
    gap: 10px;
}

.cooking-course-card-body .author-wrap {
    gap: 11px;
}

.cooking-course-card-body .author-wrap img {
    min-width: 30px;
    width: 30px;
    height: 30px;
    object-fit: cover;
    border-radius: 50%;
}

.cooking-course-card-body .author-wrap .name {
    font-family: 'Mulish';
    font-weight: 700;
    font-size: 13px;
    line-height: 26px;
    color: #171719;
}

.cooking-course-card-body .author-price .price {
    font-family: 'Mulish';
    font-weight: 700;
    font-size: 20px;
    line-height: 26px;
    color: var(--skinColor3);
}

/* Course List View */
.cooking-course-list-link {
    display: block;
    width: 100%;
}

.cooking-course-list-card {
    width: 100%;
    box-shadow: 0 14px 32px 0 rgba(0, 144, 127, 0.2);
    background: var(--whiteColor);
}

.cooking-course-list-banner-title {
    padding: 16px;
    column-gap: 20px;
    max-width: 100%;
    width: 100%;
}

.cooking-course-list-banner-title .banner {
    min-width: 195px;
    width: 195px;
    aspect-ratio: 195 / 127.63;
}

.cooking-course-list-banner-title .banner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cooking-course-list-banner-title .title-wrap .title {
    font-family: 'Mulish';
    font-weight: 700;
    font-size: 18px;
    line-height: 30px;
    color: #171719;
    margin-bottom: 20px;
}

.cooking-course-list-banner-title .title-wrap {
    max-width: 317px;
}

.cooking-course-list-banner-title .author-wrap {
    column-gap: 12px;
}

.cooking-course-list-banner-title .author-wrap img {
    min-width: 34.43px;
    width: 34.43px;
    height: 34.43px;
    border-radius: 50%;
    object-fit: cover;
}

.cooking-course-list-banner-title .author-wrap .name {
    font-family: 'Mulish';
    font-weight: 700;
    font-size: 14px;
    line-height: 30px;
    color: #171719;
}

.cooking-course-list-other {
    padding: 16px;
    max-width: 100%;
    width: 100%;
    justify-content: space-around;
    gap: 16px;
}

.cooking-course-list-other .date-time {
    position: relative;
    width: 100%;
    display: flex;
    justify-content: center;
}

.cooking-course-list-other .date-time::after {
    position: absolute;
    content: "";
    right: -9px;
    top: 50%;
    transform: translateY(-50%);
    height: 57px;
    width: 1px;
    background: rgba(144, 144, 144, 0.3);
}

.cooking-course-list-other .date-time .info {

    font-weight: 500;
    font-size: 16px;
    line-height: 26px;
    color: #909090;
    margin-bottom: 13px;
}

.cooking-course-list-other .date-time .value {
    font-family: 'Mulish';
    font-weight: 700;
    font-size: 16px;
    line-height: 26px;
    color: #062320;
}

.cooking-course-list-other .date-time-price {
    width: 100%;
    display: flex;
    justify-content: center;
}

.cooking-course-list-other .date-time-price .info {

    font-weight: 500;
    font-size: 16px;
    line-height: 26px;
    color: #909090;
    margin-bottom: 13px;
}

.cooking-course-list-other .date-time-price .value {
    font-family: 'Mulish';
    font-weight: 700;
    font-size: 16px;
    line-height: 26px;
    color: var(--skinColor3);
}

/* Counter */
.cooking-counter-area {
    column-gap: 20px;
    row-gap: 20px;
}

.cooking-counter-single {
    max-width: 100%;
    width: 100%;
    text-align: center;
    position: relative;
}

.cooking-counter-single:not(:last-child):after {
    position: absolute;
    content: "";
    right: -11px;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 77px;
    background: rgba(144, 144, 144, 0.3);
}

.cooking-counter-single .total {
    font-family: 'Mulish';
    font-weight: 600;
    font-size: 82px;
    line-height: 107px;
    color: var(--skinColor3);
    margin-bottom: 5px;
}

.cooking-counter-single .info {

    font-weight: 500;
    font-size: 18px;
    line-height: 25px;
    color: #151d27;
}

/* Kitchen */
.desirable-kitchen-area {
    column-gap: 30px;
    row-gap: 30px;
}

.desirable-kitchen-details {
    max-width: 514px;
    width: 100%;
}

.desirable-kitchen-details>.title {
    font-family: 'Mulish';
    font-weight: 700;
    font-size: 32px;
    line-height: 42px;
    color: #062320;
}

.desirable-kitchen-details>.info {

    font-weight: 400;
    font-size: 15px;
    line-height: 25px;
    color: #909090;
}

.desirable-kitchen-prefer {
    column-gap: 45px;
    row-gap: 30px;
    margin-bottom: 40px;
}

.desirable-kitchen-prefer li {
    display: flex;
    align-items: center;
    column-gap: 13px;
    min-width: 187px;
}

.desirable-kitchen-prefer li .prefer {
    font-family: 'Mulish';
    font-weight: 700;
    font-size: 16px;
    line-height: 21px;
    color: #151d27;
}

.desirable-kitchen-banner {
    max-width: 596px;
    width: 100%;
}

/* Become Instructor */
.become-instructor-area {
    column-gap: 43px;
}

.become-instructor-video-area {
    max-width: 626px;
    width: 100%;
    padding-right: 48px;
    position: relative;
}

.become-instructor-video-area .play-icon {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    height: 100px;
    width: 100px;
    border-radius: 50%;
    border: 6px solid var(--whiteColor);
    background: linear-gradient(155deg, #00907f 0%, #006e61 100%);
    display: flex;
    align-items: center;
    justify-content: center;
}

.become-instructor-video-area .play-icon>img {
    margin-left: 5px;
}

/* Video Modal  */
.instructor-video-modal .modal-header {
    padding-top: 10px;
    padding-bottom: 10px;
    background-color: #fff;
    background-image: none;
    border-bottom: var(--bs-modal-header-border-width) solid var(--bs-modal-header-border-color) !important;
}

.instructor-video-modal .modal-title {
    font-family: 'Mulish';
    font-weight: 700;
    font-size: 18px;
    line-height: 26px;
    color: #171719;
}

.instructor-video-modal .modal-header .btn-close {
    background-size: 14px;
}

.instructor-video-modal .modal-header .btn-close:focus {
    box-shadow: none;
}

/* Video */
.instructor-modal-video {
    width: 100%;
}

.instructor-modal-video .plyr--video {
    height: 100%;
    width: 100%;
    border-radius: 3px;
}

.instructor-modal-video .plyr__control--overlaid {
    background: var(--whiteColor);
    color: var(--blackColor);
    outline: 10px solid rgba(38, 38, 38, 0.26);
}

.instructor-modal-video .plyr__control svg {
    height: 14px;
    width: 14px;
}

.instructor-modal-video .plyr--video .plyr__control:hover {
    background: var(--whiteColor);
    color: var(--blackColor);
}

.instructor-modal-video .plyr--full-ui input[type=range] {
    color: var(--whiteColor);
}

.become-instructor-details {
    max-width: 465px;
    width: 100%;
}

.become-instructor-details>.title {
    font-family: 'Mulish';
    font-weight: 700;
    font-size: 32px;
    line-height: 42px;
    color: #062320;
}

.become-instructor-details>.info {

    font-weight: 400;
    font-size: 15px;
    line-height: 25px;
    color: #909090;
}

/* Popular Instructor */
.cooking-popular-instructor {
    max-width: 334px;
    margin: auto;
}

.cooking-popular-instructor .profile-img {
    margin-bottom: 12px;
    width: 100%;
    height: 230px;
}
.cooking-popular-instructor .profile-img img{
    height: 100%;
    width: 100%;
    object-fit: cover;
}

.cooking-popular-instructor .details .name {
    font-family: 'Raleway';
    font-weight: 600;
    font-size: 24px;
    line-height: 48px;
    color: #062320;
    text-align: center;
}

.cooking-popular-instructor .details .role {

    font-weight: 400;
    font-size: 18px;
    line-height: 38px;
    color: rgba(144, 144, 144, 0.8);
    margin-bottom: 8px;
    text-align: center;
}

.popular-instructor-socila {
    column-gap: 10px;
    row-gap: 8px;
}

.popular-instructor-socila li a {
    padding: 0 7px;
}

.popular-instructor-socila li a path {
    transition: .3s;
}

.popular-instructor-socila li a:hover path {
    fill: var(--skinColor1);
}

/* Frequently Asked Questions */
.frequently-qna-title-area {
    column-gap: 40px;
    row-gap: 30px;
}

.frequently-qna-title-area>.title {
    font-family: 'Mulish';
    font-weight: 700;
    font-size: 42px;
    line-height: 57px;
    color: #062320;
    max-width: 369px;
    width: 100%;
}

.frequently-qna-title-area>.info {

    font-weight: 400;
    font-size: 15px;
    line-height: 30px;
    color: #909090;
    max-width: 600px;
    width: 100%;
}

/* Accordion */
.qnaaccordion-two .accordion-button {
    padding: 0;
    transition: .3s;
    font-family: 'Mulish';
    font-weight: 700;
    font-size: 18px;
    line-height: 22px;
    color: #062320;
}

.qnaaccordion-two .accordion-item:not(:first-child) .accordion-button {
    padding-top: 30px;
}

.qnaaccordion-two .accordion-item:not(:last-child) .accordion-button {
    padding-bottom: 30px;
}

.qnaaccordion-two .accordion-button:focus {
    box-shadow: none;
}

.qnaaccordion-two .accordion-button:not(.collapsed) {
    box-shadow: none;
    background-color: transparent;
    color: var(--blackColor);
    padding-bottom: 30px;
}

.qnaaccordion-two .accordion-button::after {
    background-image: url(../image/angle-right-black-20.svg);
    background-size: 20px;
    width: 20px;
    height: 20px;
}

.qnaaccordion-two .accordion-button:not(.collapsed)::after {
    background-image: url(../image/angle-up-green-20.svg);
}

.qnaaccordion-two .accordion-item {
    border: none;
}

.qnaaccordion-two .accordion-item:not(:last-child) {
    border-bottom: 1px solid rgba(144, 144, 144, 0.3);
}

.qnaaccordion-two .accordion-body {
    padding: 0;
    padding-bottom: 30px;
}

.qnaaccordion-two .accordion-item:last-child .accordion-body {
    padding-bottom: 0px;
}

.qnaaccordion-two .answer {

    font-weight: 400;
    font-size: 15px;
    line-height: 30px;
    color: #909090;
}

.two-accordion-wrap .row {
    --bs-gutter-x: 61px;
    row-gap: 61px;
}

/* News */
.list-news1-link {
    display: block;
    width: 100%;
    height: 100%;
}

.list-link1-card {
    column-gap: 20px;
    row-gap: 20px;
}

.list-link1-card .banner {
    max-width: 244px;
    width: 100%;
}

.list-link1-card .banner img {
    width: 100%;
}

.list-news1-card-body {
    max-width: 100%;
    width: 100%;
}

.list-news1-card-body .date-wrap {
    column-gap: 6px;
    margin-bottom: 4px;
}

.list-news1-card-body .date-wrap .date {

    font-weight: 400;
    font-size: 13px;
    line-height: 26px;
    color: #909090;
}

.list-news1-card-body .date-wrap img {
    margin-bottom: 2px;
}

.list-news1-card-body>.title {
    font-family: 'Mulish';
    font-weight: 700;
    font-size: 18px;
    line-height: 30px;
    color: #171719;
    margin-bottom: 12px;
}

.list-news1-card-body>.info {

    font-weight: 400;
    font-size: 15px;
    line-height: 24px;
    color: #909090;
    margin-bottom: 20px;
}

.list-news1-card-body .arrow img {
    transition: .3s;
}

.list-news1-link:hover .arrow>img {
    margin-left: 3px;
}

.cooking-news-section {
    overflow: hidden;
}

.cooking-news-main-area {
    position: relative;
    z-index: 1;
}

.cooking-news-main-area::after {
    position: absolute;
    content: "";
    top: -45px;
    right: -395px;
    width: 541px;
    aspect-ratio: 541 / 541;
    border-radius: 100%;
    background: radial-gradient(61.56% 61.56% at 56.49% 50%, rgb(0, 144, 127) 0%, rgb(0, 144, 127) 100%);
    filter: blur(200px);
    z-index: -1;
    opacity: 0.09;
}

/* ===================================================================
                        Cooking CSS End
====================================================================*/


/* ===================================================================
                        Development CSS End
====================================================================*/
.dev-banner-section {
    background-position: center bottom;
    background-repeat: no-repeat;
    background-size: contain;
    position: relative;
    overflow: hidden;
}

.dev-banner-section::after {
    content: "";
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 75%;
    aspect-ratio: 1053 / 388;
    bottom: -119px;
    background: url(../images/shape/dev-banner-circle.png) no-repeat scroll center center / cover;
    z-index: -1;
}

.dev-banner-section::after {
    content: "";
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 60%;
    aspect-ratio: 848 / 598;
    bottom: -110px;
    background: url(../images/shape/dev-banner-shadow.webp) no-repeat scroll center center / cover;
    z-index: -1;
}

.development-banner-area {
    margin-top: 80px;
}

.development-banner-area>.title {
    font-family: 'Montserrat';
    font-weight: 700;
    font-size: 50px;
    line-height: 66px;
    text-align: center;
    color: #201f22;
}

.development-banner-area>.title .highlight {
    color: var(--skinColor4);
}

.development-banner-area .video-play-btn {
    margin-top: 195px;
    margin-bottom: 189px;
    margin-left: 42px;
}

@media all and (min-width: 1499px) {
    .development-banner-area .video-play-btn {
        margin-bottom: 200px;
    }

}

.video-play-btn {
    display: flex;
    align-items: center;
    column-gap: 16px;
    font-family: 'Montserrat';
    font-weight: 600;
    font-size: 20px;
    line-height: 28px;
    color: #201f22;
    transition: .3s;
    max-width: max-content;
}

.video-play-btn .icon {
    min-width: 70px;
    width: 70px;
    height: 70px;
    border-radius: 50%;
    border: 1px solid #b5b2ad;
    background: #f5f3ec;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: .3s;
}

.video-play-btn .icon img {
    margin-left: 5px;
}

.video-play-btn:hover {
    color: var(--skinColor4);
}

.video-play-btn:hover .icon {
    border-color: var(--skinColor4);
}

.development-hero-section1 {
    background: #F4F4F4;
    border-top: 1px solid #b5b2ad;
}

.development-hero-area1 {
    padding: 33px 0 27px 0;
    gap: 28px;
    flex-wrap: wrap;
}

.hero-rated-profile-area {
    padding-right: 25.5px;
    max-width: 231.5px;
    width: 100%;
    position: relative;
}

.hero-rated-profile-area::after {
    position: absolute;
    content: "";
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: calc(100% - 18px);
    background: rgba(32, 31, 34, 0.27);
}

.hero-rated-profile-area .profiles {
    margin-bottom: 12px;
}

.hero-rated-profile-area .profiles li {
    min-width: 40px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.hero-rated-profile-area .profiles li:not(:last-child) img {
    height: 100%;
    width: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.hero-rated-profile-area .profiles li:last-child {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #201f22;
}

.hero-rated-profile-area .profiles li:not(:first-child) {
    margin-left: -13px;
}

.hero-rated-profile-area .profiles li:not(:last-child) {
    position: relative;
}

.hero-rated-profile-area .profiles li:nth-of-type(1) {
    z-index: 2;
}

.hero-rated-profile-area .profiles li:nth-of-type(2) {
    z-index: 1;
}

.hero-rated-profile-area>.info {
    font-family: 'Montserrat';
    font-weight: 600;
    font-size: 14px;
    line-height: 20px;
    color: #201f22;
}

.development-hero-area1 .hero-info {

    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: #201f22;
    max-width: 512px;
}

.btn-black1 {
    border-radius: 8px;
    padding: 14px 30px;
    background: #201f22;
    font-family: 'Montserrat';
    font-weight: 600;
    font-size: 14px;
    line-height: 20px;
    color: var(--whiteColor);
    max-width: max-content;
    display: block;
    transition: .3s;
}

.btn-black1:hover {
    background: #424242;
}

/* Software Development */
.software-development-banner {
    max-width: 504px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.software-development-banner::after {
    position: absolute;
    content: "";
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: url(../images/shape/soft-dev-shadow.webp) no-repeat scroll center center / cover;
    z-index: -1;
}

.software-development-details>.title {
    font-family: 'Montserrat';
    font-weight: 700;
    font-size: 32px;
    line-height: 36px;
    color: #201f22;
    margin-bottom: 16px;
}

.software-development-details>.title .highlight {
    color: var(--skinColor4);
    font-weight: 600;
}

.software-development-details>.info {

    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: #78777c;
}

.dashed-list-items li {
    font-family: 'Montserrat';
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    color: #201f22;
}

.dashed-list-items li span {
    color: var(--skinColor4);
}

.dashed-list-items li:not(:last-child) {
    margin-bottom: 8px;
}

.btn-black-arrow1 {
    border-radius: 8px;
    padding: 14px 24px 14px 30px;
    background: #201f22;
    font-family: 'Montserrat';
    font-weight: 600;
    font-size: 14px;
    line-height: 20px;
    color: var(--whiteColor);
    display: flex;
    align-items: center;
    column-gap: 6px;
    max-width: max-content;
    transition: .3s;
}

.btn-black-arrow1:hover {
    background: #424242;
}

/* Section Title */
.dev-section-title {
    max-width: 621px;
    width: 100%;
    margin: 0 auto 30px auto;
}

.dev-section-title>.title {
    font-family: 'Montserrat';
    font-weight: 700;
    font-size: 32px;
    line-height: 36px;
    text-align: center;
    color: #201f22;
}

.dev-section-title>.title .highlight {
    color: var(--skinColor4);
}

.dev-section-title>.info {


    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    text-align: center;
    color: #78777c;
}

.learning-coding-card {
    border: 1px solid #b5b2ad;
    border-radius: 12px;
    padding: 40px 24px;
    width: 100%;
    height: 100%;
}

.learning-coding-card>.title {
    font-family: 'Montserrat';
    font-weight: 600;
    font-size: 24px;
    line-height: 32px;
    color: #201f22;
    text-align: center;
    margin-bottom: 16px;
}

.learning-coding-card>.info {

    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    text-align: center;
    color: #78777c;
}

.learning-coding-card .banner {
    width: 125px;
    height: 125px;
    margin: 40px auto 0px auto;
}

.learning-coding-card .banner img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* Course */
.dev-course-card-link {
    display: block;
    height: 100%;
    width: 100%;
}

.dev-course-card {
    border: 1px solid #b5b2ad;
    border-radius: 12px;
    padding: 12px;
    height: 100%;
    width: 100%;
}

.dev-course-card .banner {
    width: 100%;
    aspect-ratio: 246 / 186;
    margin-bottom: 12px;
}

.dev-course-card .banner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
}

.dev-course-card-body>.title {
    font-family: 'Montserrat';
    font-weight: 600;
    font-size: 18px;
    line-height: 28px;
    color: #201f22;
    margin-bottom: 4px;
}

.dev-course-card-body .reviews {
    column-gap: 6px;
    margin-bottom: 8px;
}

.dev-course-card-body .reviews .ratings {
    column-gap: 4px;
}

.dev-course-card-body .reviews .total {


    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #78777c;
}

.dev-course-card-body .price {
    font-family: 'Montserrat';
    font-weight: 600;
    font-size: 20px;
    line-height: 28px;
    color: #201f22;
    margin-bottom: 8px;
}

.dev-course-card-body .leason-student {
    column-gap: 12px;
    row-gap: 8px;
    flex-wrap: wrap;
}

.dev-course-card-body .leason-student .leasons-students {
    column-gap: 6px;
}

.dev-course-card-body .leason-student .leasons-students .total {

    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #78777c;
}

.dev-course-card-body .leason-student .leasons-students:not(:last-child) {
    padding-right: 12px;
    position: relative;
}

.dev-course-card-body .leason-student .leasons-students:not(:last-child)::after {
    position: absolute;
    content: "";
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 14px;
    background: rgba(32, 31, 34, 0.27);
}

.dev-course-btn-area {
    margin-top: 10px;
}

/* Programming E-book */
.programming-ebook-section {
    background: rgba(242, 225, 217, 0.8);
}

.programming-ebook-area {
    padding: 30px 0;
    column-gap: 50px;
    row-gap: 40px;
}

.programming-ebook-banner {
    max-width: 498px;
    width: 100%;
}

.programming-ebook-banner .img {
    width: 100%;
}

.programming-ebook-details {
    max-width: 582px;
    width: 100%;
}

.programming-ebook-details .title {
    font-family: 'Montserrat';
    font-weight: 700;
    font-size: 32px;
    line-height: 36px;
    color: #201f22;
    margin-bottom: 16px;
}

.programming-ebook-details .title .highlight {
    color: var(--skinColor4);
    font-weight: 600;
}

.programming-ebook-details .info {


    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: #78777c;
}

/* QNA Accordion */
.qna-three-accordion .accordion-item {
    border: none;
}

.qna-three-accordion .accordion-item:not(:last-child) {
    border-bottom: 1px solid rgba(133, 140, 138, 0.3);
}

.qna-three-accordion .accordion-item,
.qna-three-accordion .accordion-button {
    border-radius: 0;
}

.qna-three-accordion .accordion-button {
    padding: 0;
    transition: .3s;
    font-family: 'Ubuntu';
    font-weight: 700;
    font-size: 18px;
    line-height: 28px;
    color: #201f22;
}

.qna-three-accordion .accordion-item:not(:last-child) .accordion-button {
    padding-bottom: 28px;
}

.qna-three-accordion .accordion-item:not(:first-child) .accordion-button {
    padding-top: 28px;
}

.qna-three-accordion .accordion-button:focus {
    box-shadow: none;
}

.qna-three-accordion .accordion-button:not(.collapsed) {
    background-color: inherit;
    color: #201f22;
    box-shadow: none;
    padding-bottom: 12px !important;
}

.qna-three-accordion .accordion-body {
    padding: 0;
    padding-bottom: 28px;
    padding-right: 30px;
}

.qna-three-accordion .accordion-body .answer {


    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: #78777c;
}

.qna-three-accordion .accordion-button::after {
    background-size: 24px;
    background-image: url(../image/arrow-circle-black-24.svg);
    width: 24px;
    height: 24px;
}

.qna-three-accordion .accordion-button:not(.collapsed)::after {
    transform: rotate(-90deg);
}

/* Testimonial */
.dev-student-testimonial {
    height: 100%;
    width: 100%;
    border: 1px solid #b5b2ad;
    border-radius: 12px;
    padding: 30px 24px;
}

.dev-student-testimonial .ratings {
    column-gap: 5.32px;
    margin-bottom: 12px;
}

.dev-student-testimonial .feedback {

    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: #78777c;
    margin-bottom: 18px;
}

.dev-student-testimonial .feedback .bold {
    font-weight: 500;
}

.dev-student-testimonial .profile-wrap {
    column-gap: 18px;
}

.dev-student-testimonial .profile-wrap .profile {
    min-width: 60px;
    width: 60px;
    height: 60px;
}

.dev-student-testimonial .profile-wrap .profile img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.dev-student-testimonial .name-role .name {
    font-family: 'Montserrat';
    font-weight: 700;
    font-size: 18px;
    line-height: 28px;
    color: #201f22;
}

.dev-student-testimonial .name-role .role {

    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: #78777c;
}

.dev-student-swiper .swiper-slide {
    height: auto;
}

.swiper-button-wrap {
    column-gap: 18px;
    margin-top: 30px;
}

.swiper-button-wrap .swiper-button-next,
.swiper-button-wrap .swiper-button-prev {
    position: inherit;
    margin: 0;
    height: 46px;
    width: 46px;
}

.swiper-button-wrap .swiper-button-next:after,
.swiper-button-wrap .swiper-button-prev:after {
    content: "";
    height: 46px;
    width: 46px;
    background-size: 46px;
    background-position: center;
    background-repeat: no-repeat;
}

.swiper-button-wrap .swiper-button-next:after {
    background-image: url(../image/angle-circle-right-46.svg);
}

.swiper-button-wrap .swiper-button-prev:after {
    background-image: url(../image/angle-circle-left-46.svg);
}

.swiper-button-wrap .swiper-button-next.swiper-button-disabled,
.swiper-button-wrap .swiper-button-prev.swiper-button-disabled {
    opacity: 0.45;
}

/* News Blog */
.dev-news-link {
    display: block;
    height: 100%;
    width: 100%;
}

.dev-news-card {
    height: 100%;
    width: 100%;
    border: 1px solid #b5b2ad;
    border-radius: 12px;
    padding: 16px;
}

.dev-news-card .banner {
    width: 100%;
    aspect-ratio: 335 / 230;
    margin-bottom: 14px;
}

.dev-news-card .banner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
}

.dev-news-card-body>.title {
    font-family: 'Montserrat';
    font-weight: 700;
    font-size: 20px;
    line-height: 28px;
    color: #201f22;
}

.dev-news-card-body .date-comments {
    column-gap: 12px;
    row-gap: 5px;
}

.dev-news-card-body .date-comments .date-wrap,
.dev-news-card-body .date-comments .comment-wrap {
    column-gap: 6px;
}

.dev-news-card-body .date-comments .date-wrap .value,
.dev-news-card-body .date-comments .comment-wrap .value {

    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #78777c;
}

.dev-news-card-body .date-comments .date-wrap {
    padding-right: 12px;
    position: relative;
}

.dev-news-card-body .date-comments .date-wrap::after {
    position: absolute;
    content: "";
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    height: 14px;
    width: 1px;
    background: rgba(32, 31, 34, 0.27);
}

.dev-news-card-body>.info {

    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: #78777c;
}

/* ===================================================================
                        Development CSS End
====================================================================*/



/* ===================================================================
                        Language CSS Start
====================================================================*/
.text-bordered-1 {
    border: 1px solid var(--skinColor5);
    border-radius: 100px;
    padding: 7px 15px;

    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0.06em;
    text-transform: capitalize;
    color: var(--skinColor5);
    max-width: max-content;
}

.lms-subscribe-form-1 .form-control-1 {
    max-width: 322px;
    width: 100%;
}

.form-control-1 {
    border: 1px solid rgba(134, 134, 141, 0.34);
    border-radius: 8px;
    font-family: 'Rubik';
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #86868d;
    transition: .3s;
    padding: 13px 15px;
}

.form-control-1:focus,
.form-control-1:hover {
    border-color: var(--skinColor5);
    color: #86868d;
}

.form-control-1::placeholder {
    color: #86868d;
}

.btn-primary-1 {
    border-radius: 8px;
    padding: 14px 30px;
    background: var(--skinColor5);
    font-family: 'Rubik';
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    color: var(--whiteColor);
    border: none;
}

.btn-primary-1:active,
.btn-primary-1:hover {
    background: #076785 !important;
    color: var(--whiteColor) !important;
}

.lms-banner-1 {
    max-width: 388px;
    margin-left: -24px;
}

.lms-banner-1 img {
    width: 100%;
}

.lms-banner-area-1 {
    gap: 30px;
}

.lms-banner-wrap-1 {
    padding: 70px 0 100px 0;
}

.lms-banner-items1 {
    max-width: 223px;
}

/* Service */
.lms-service-card-1 {
    max-width: 282px;
    margin: 0 auto;
}

.service-card-icon1 {
    margin: 0 auto 30px auto;
}

/* Course */
/* card design */
/* card 1 */
.lms-1-card {
    box-shadow: 0 14px 32px 0 rgba(147, 148, 158, 0.2);
    background: var(--whiteColor);
    border-radius: 12px;
    height: 100%;
    width: 100%;
    overflow: hidden;
    transition: 0.3s;
}
i:not(.fas, .fa, .fab) {
    line-height: 1.5em !important;
    vertical-align: -0.12em !important;
    display: inline-flex;
}

i[class^="fi-rr-"]:before,
i[class*=" fi-rr-"]:before,
span[class^="fi-rr-"]:before,
span[class*="fi-rr-"]:before {
    line-height: 1.5em !important;
}

.lms-1-card-body {
    padding: 16px;
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* card hover */
.lms-card-hover1 {
    transition: .3s;
}

.lms-card-hover1:hover {
    box-shadow: 0 14px 32px 0 rgba(83, 99, 210, 0.26);
}

.lms-card-hover2 {
    transition: .3s;
    outline: 1px solid transparent;
}

.lms-card-hover2:hover {
    outline-color: #1d242d;
}

.card-banner-hover1 {
    transition: .3s;
}

.card-banner-hover1:hover {
    box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
}

.list-view-banner1 {
    max-width: 233px;
    width: 100%;
    aspect-ratio: 233 / 180;
}

.list-view-banner1>img {
    height: 100%;
    width: 100%;
    object-fit: cover;
    border-radius: 8px;
}

.card-rating-reviews1 {
    gap: 6px;
}

.card-flug-sm {
    min-width: 22px;
    width: 22px;
    height: 22px;
}

.card-flug-sm>img {
    height: 100%;
    width: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.card-programs-1 {
    width: 100%;
    padding-bottom: 20px;
    border-bottom: 1px solid #d6d6d8;
}

.card-author-sm {
    min-width: 30px;
    width: 30px;
    height: 30px;
}

.card-author-sm>img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.btn-outline-primary-1 {
    border: 1px solid var(--skinColor5);
    border-radius: 8px;
    padding: 11px 17px;

    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    color: var(--skinColor5);
}

.btn-outline-primary-1:active,
.btn-outline-primary-1:hover {
    background: var(--skinColor5) !important;
    color: var(--whiteColor) !important;
}

.btn-primary-2 {
    border-radius: 8px;
    padding: 12px 18px;
    background: var(--skinColor5);

    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    color: var(--whiteColor);
    border: none;
}

.btn-primary-2:active,
.btn-primary-2:hover {
    background: #076785 !important;
    color: var(--whiteColor) !important;
}

.about-text-items {
    padding-left: 23px;
    position: relative;
}

.about-text-items::after {
    position: absolute;
    content: "";
    left: 0;
    top: 0;
    width: 3px;
    height: calc(100% - 7px);
    border-radius: 24px;
    background: linear-gradient(rgb(38 72 113) 0%, rgb(72 137 215 / 0%) 100%);
}

.about-area-banner1>img {
    width: 100%;
}

/* Form and Why choose Area */
.form-control-2 {
    border: 1px solid rgba(134, 134, 141, 0.34);
    border-radius: 8px;
    font-family: 'Rubik';
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #86868d;
    transition: .3s;
    padding: 15px 25px;
}

.form-control-2:focus,
.form-control-2:hover {
    border-color: var(--skinColor5);
    color: #86868d;
}

.form-control-2::placeholder {
    color: #86868d;
}

.form-label-2 {

    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: #030531;
    margin-bottom: 12px;
}

.btn-primary-3 {
    border-radius: 8px;
    padding: 16px 26px;
    background: var(--skinColor5);

    font-weight: 500;
    font-size: 16px;
    color: var(--whiteColor);
    border: none;
}

.btn-primary-3:active,
.btn-primary-3:hover {
    background: #076785 !important;
    color: var(--whiteColor) !important;
}

.bgcolor-card-1 {
    border-radius: 12px;
    padding: 16px;
    max-width: 255px;
    width: 100%;
    background: var(--bgcolor, linear-gradient(164deg, #e8f7fc 0%, #f1f9fc 100%));
}

.signup-form-wrap {
    position: static;
    z-index: 1;
}

.signup-form-wrap::after {
    position: absolute;
    content: "";
    left: 0;
    margin-top: -330px;
    width: 671px;
    width: calc(50% - 200px);
    height: 312px;
    background: linear-gradient(89deg, #264871 0%, #2e5788 33.99%, #4889d7 100%);
    z-index: -1;
}

/* Team */
.grid-view-banner1 {
    border-radius: 8px;
}
.grid-view-banner1>a{
    width: 100%;
}
.grid-view-banner1>a>img, .grid-view-banner1>img {
    width: 100%;
    height: 207px;
    border-radius: 8px;
    object-fit:cover;
}

.grid-view-banner2>img {
    width: 100%;
    border-radius: 16px 16px 0 0;
}

.social-link-1 {
    border: 0.50px solid rgba(38, 72, 113, 0.2);
    border-radius: 4px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.social-link-1:hover {
    border-color: var(--skinColor5);
}

.link-icon-btn1 {
    display: flex;
    align-items: center;

    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: #121421;
    gap: 4px;
    max-width: max-content;
}

.link-icon-btn1 span::before {
    display: block;
    font-size: 18px;
    transition: .3s;
}

.link-icon-btn1:hover span::before {
    margin-left: 3px;
}

/* Language Slider */
.testimonial-profile-wrap1 {
    max-width: 290px;
    width: 100%;
    margin: auto;
}

.testimonial-profile-area1 {
    height: 56px;
}

.testimonial-profile-img1 {
    width: 56px !important;
    aspect-ratio: 1/1;
    border-radius: 50%;
    overflow: hidden;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    display: flex !important;
    align-items: center;
    justify-content: center;
    margin: 0 3px;
}

.testimonial-profile-img1 img {
    width: 100%;
    aspect-ratio: 1/1;
    object-fit: cover;
    object-position: center;
    transition: .5s;
    -webkit-transition: .5s;
    -moz-transition: .5s;
    -ms-transition: .5s;
    -o-transition: .5s;
}

.testimonial-profile1 {
    display: flex !important;
    align-items: center;
    justify-content: center;
}

.testimonial-profile-area1 .slick-slide img {
    width: 70%;
    aspect-ratio: 1/1;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
}

.testimonial-profile-area1 .slick-current img {
    width: 100% !important;
    aspect-ratio: 1/1;
}

/* Details */
.testimonial-details-wrap1 .slick-prev {
    left: 0px;
}

.testimonial-details-wrap1 .slick-next {
    right: 0px;
}

.testimonial-details-wrap1 .slick-next,
.testimonial-details-wrap1 .slick-prev {
    height: 52px;
    width: 52px;
    border-radius: 50%;
    background: rgba(134, 134, 141, 0.14);
    transition: .3s;
}

.testimonial-details-wrap1 {
    padding: 0 100px;
}

.testimonial-ratings-1 {
    column-gap: 6px;
}

.testimonial-details-wrap1 .slick-next:before {
    content: "\f130";
}

.testimonial-details-wrap1 .slick-prev:before {
    content: "\f12f";
}

.testimonial-details-wrap1 .slick-next:before,
.testimonial-details-wrap1 .slick-prev:before {
    transition: .3s;
    font-family: uicons-regular-rounded !important;
    font-style: normal;
    font-weight: normal !important;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: #030531;
}

.testimonial-details-wrap1 .slick-next:hover:before,
.testimonial-details-wrap1 .slick-prev:hover:before {
    color: var(--whiteColor);
}

.testimonial-details-wrap1 .slick-next:hover,
.testimonial-details-wrap1 .slick-prev:hover {
    color: var(--whiteColor);
    background: var(--skinColor5);
}

/* ===================================================================
                        Language CSS End
====================================================================*/



/* ===================================================================
                        Kindergarten CSS End
====================================================================*/
.kg-banner-area {
    padding-top: 60px;
}

.text-red-highlight1 {
    font-family: 'Lexend Deca';
    font-weight: 600;
    font-size: 14px;
    line-height: 18px;
    letter-spacing: 0.2em;
    color: #ff375e;
}

.title-purple {
    color: #5363d2;
}

.kg-banner-title {
    position: relative;
}

.kg-banner-title::before {
    position: absolute;
    content: "";
    left: -31px;
    top: -4px;
    width: 31px;
    height: 31px;
    background: url(../image/kg-banner-shape1.svg) no-repeat center center / cover;
}

.kg-banner-title-last {
    position: relative;
    padding-right: 70px;
    display: inline-block;
}

.kg-banner-title-last::after {
    position: absolute;
    content: "";
    right: 0px;
    bottom: -2px;
    width: 64px;
    height: 53px;
    background: url(../image/kg-banner-shape2.svg) no-repeat center center / cover;
}

.speech-purple-bordered {
    padding-left: 14px;
    position: relative;
}

.speech-purple-bordered::after {
    position: absolute;
    content: "";
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 2px;
    height: calc(100% - 8px);
    background: var(--skinColor2);
}

.play-btn-1 {
    display: flex;
    align-items: center;
    gap: 10px;
    font-family: 'Outfit';
    font-weight: 500;
    font-size: 15px;
    line-height: normal;
    text-decoration: underline;
    text-decoration-skip-ink: none;
    color: #0f101a;
    transition: .3s;
}

.play-btn-1 .icon {
    height: 42px;
    width: 42px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(164deg, #ff6b88 0%, #ff375e 100%);
    transition: .3s;
}

.play-btn-1 .icon img {
    margin-left: 3px;
}

.play-btn-1:hover .icon {
    box-shadow: 0 7px 12px 0 rgba(255, 55, 94, 0.25);
}

.trusted-companies-title {
    padding-right: 30px;
    position: relative;
    max-width: 212px;
    width: 100%;
}

.trusted-companies-title::after {
    position: absolute;
    content: "";
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: calc(100% - 14px);
    background: #0f101a;
}

.lms-banner-2 img {
    width: 100%;
}

.lms-banner-2 {
    position: relative;
    z-index: 1;
}

.lms-banner-2::after {
    position: absolute;
    content: "";
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    background-image: url(../images/shape/kg-banner-shadow1.svg);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    z-index: -1;
}


.counter-area-wrap1 {
    border-radius: 30px;
    background: rgba(83, 99, 210, 0.1);
    padding: 60px 40px;
}

.image-box-md {
    width: 88px;
    height: 88px;
    border-radius: 15px;
    background: var(--whiteColor);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px;
}

.kg-counter-title {
    font-family: 'Lexend Deca';
    font-weight: 600;
    font-size: 38px;
    line-height: 53px;
    color: var(--skinColor2);
}

.section-title-1 .subtitle-2 {
    max-width: 494px;
    width: 100%;
    margin: 0 auto;
}

.card-icon-text1>.info {

    font-weight: 400;
    font-size: 13px;
    line-height: 26px;
    color: #8f919b;
}

.card-icon-text1>span::before {
    display: block;
    color: var(--skinColor2);
    margin-top: -1px;
    font-size: 14px;
}

.card-icon-text2>span::before {
    display: block;
    color: #1d242d;
    margin-top: -1px;
    font-size: 14px;
}

.card-rating1>.rating {
    font-family: 'Mulish';
    font-weight: 700;
    font-size: 15px;
    line-height: 26px;
    color: #062320;
}

.card-rating1>img {
    margin-top: -2px;
}

.card-leason-rating1 {
    padding-bottom: 12px;
    margin-bottom: 12px;
    border-bottom: 1px solid rgba(143, 145, 155, 0.3);
}

.kg-card-profile-price .title-1 {
    font-family: 'Mulish';
    color: #0f101a;
}

.kg-card-profile-price .price {
    font-family: 'Mulish';
    color: #ff375e;
}


/* Category */
.bg-icon-card1 {
    min-width: 93px;
    border-radius: 8px;
    width: 93px;
    height: 93px;
    background: var(--bgcolor, #e9f6ff);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
}

/* Accordion */
.qnaaccordion-three .accordion-button {
    padding: 0;
    transition: .3s;
    font-family: 'Lexend Deca';
    font-weight: 500;
    font-size: 18px;
    line-height: 22px;
    color: #0f101a;
}

.qnaaccordion-three .accordion-item:not(:first-child) .accordion-button {
    padding-top: 30px;
}

.qnaaccordion-three .accordion-item:not(:last-child) .accordion-button {
    padding-bottom: 30px;
}

.qnaaccordion-three .accordion-button:focus {
    box-shadow: none;
}

.qnaaccordion-three .accordion-button:not(.collapsed) {
    box-shadow: none;
    background-color: transparent;
    color: var(--skinColor2);
    padding-bottom: 30px;
}

.qnaaccordion-three .accordion-button::after {
    background-image: url(../image/angle-right-black-20.svg);
    background-size: 20px;
    width: 20px;
    height: 20px;
}

.qnaaccordion-three .accordion-button:not(.collapsed)::after {
    background-image: url(../image/angle-up-blue-20.svg);
}

.qnaaccordion-three .accordion-item {
    border: none;
}

.qnaaccordion-three .accordion-item:not(:last-child) {
    border-bottom: 1px solid rgba(143, 145, 155, 0.3);
}

.qnaaccordion-three .accordion-body {
    padding: 0;
    padding-bottom: 30px;
}

.qnaaccordion-three .accordion-item:last-child .accordion-body {
    padding-bottom: 0px;
}

.accor-three-answer {

    font-weight: 400;
    font-size: 15px;
    line-height: 30px;
    color: #8f919b;
}


.link-btn-hover1 {
    transition: .3s;
}

.link-btn-hover1:hover {
    color: var(--skinColor2);
}

.lms-1-card:hover .link-btn-hover1 {
    color: var(--skinColor2);
}

.link-icon-btn2 {
    display: flex;
    align-items: center;
    font-family: 'Lexend Deca';
    font-weight: 400;
    font-size: 15px;
    line-height: 26px;
    color: #0f101a;
    gap: 4px;
    max-width: max-content;
}

.link-icon-btn2 span::before {
    display: block;
    font-size: 20px;
    transition: .3s;
}

.link-icon-btn2:hover span::before {
    margin-left: 3px;
}

/* Testimonial */
.lms-testimonial-1 {
    padding: 0px 0px 50px 28px;
}

.lms-testimonial-1 .swiper-slide {
    height: auto;
}

.lms-testimonial-1 .swiper-button-prev,
.lms-testimonial-1 .swiper-button-next {
    background: rgba(134, 134, 141, 0.15);
    backdrop-filter: blur(8px);
    height: 42px;
    width: 42px;
    border-radius: 50%;
    color: var(--blackColor);
    transition: .3s;
}

.swiper-button-next {
    right: 3px;
}

.swiper-button-prev {
    left: 3px;
}

.lms-testimonial-1 .swiper-button-prev:hover,
.lms-testimonial-1 .swiper-button-next:hover {
    background: var(--skinColor2);
    color: var(--whiteColor);
}

.lms-testimonial-1 .swiper-button-prev:after,
.lms-testimonial-1 .swiper-button-next:after {
    font-size: 14px;
    font-weight: bold;
    margin-left: -2px;
}

.single-testimonial1-inner {
    box-shadow: 0 14px 32px 0 rgba(83, 99, 210, 0.16);
    background: var(--bg-color, #fffccf);
    border-radius: 16px;
    padding: 30px;
    position: relative;
    margin-top: 52px;
}

.testimonial1-user-role {
    font-family: 'Lexend Deca';
    font-weight: 400;
    font-size: 14px;
    color: var(--skinColor2);
}

.testimonial1-profile-img {
    width: 85px;
    height: 85px;
    border-radius: 50%;
    outline: 2px solid #fffbf8;
    position: absolute;
    top: -50px;
    right: 40px;
}

.testimonial1-profile-img img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.swiper-slide.swiper-slide-active .testimonial1-profile-img {
    outline-color: #FF375E;
}

.btn-purple-1 {
    background: linear-gradient(158deg, #6f7fed 0%, #5363d2 100%);
    border-radius: 10px;
    padding: 14px 23.5px;
    font-family: 'Lexend Deca';
    font-weight: 500;
    font-size: 15px;
    line-height: normal;
    color: var(--whiteColor);
    border: none;
    transition: .3s;
    overflow: hidden;
    position: relative;
    z-index: 1;
}

.btn-purple-1::before {
    position: absolute;
    content: "";
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    background: linear-gradient(158deg, #2d44dd 0%, #6f7fed 100%);
    visibility: hidden;
    opacity: 0;
    pointer-events: none;
    z-index: -1;
    transition: .3s;
}

.btn-purple-1:active,
.btn-purple-1:hover {
    background: linear-gradient(158deg, #6f7fed 0%, #5363d2 100%) !important;
    color: var(--whiteColor) !important;
}

.btn-purple-1:hover::before {
    visibility: visible;
    opacity: 1;
    pointer-events: auto;
}

.btn-purple-1 span::before {
    display: block;
    margin-bottom: -1px;
}

.btn-purple-sm {
    padding: 11.5px 20px;
    font-family: 'Outfit';
    font-weight: 500;
    font-size: 15px;
    box-shadow: 0 7px 12px 0 rgba(83, 99, 210, 0.26);
}

.community-service-banner {
    height: 36px;
    width: auto;
}

.community-service-banner>img {
    height: 100%;
    width: auto;
    object-fit: contain;
}

.community-service-name {
    font-family: 'Mulish';
    font-weight: 600;
    font-size: 16px;
    line-height: 21px;
    color: #0f101a;
}

.community-banner1>img {
    width: 100%;
}

.community-banner1 {
    position: relative;
    z-index: 1;
}

.community-banner1::after {
    position: absolute;
    content: "";
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    background-image: url(../images/shape/community-banner-shadow1.svg);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    z-index: -1;
}

/* ===================================================================
                        Kindergarten CSS End
====================================================================*/



/* ===================================================================
                        Marketplace CSS Start
====================================================================*/
.lms-banner-area-3 {
    padding: 83px 40px;
    border-radius: 16px;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    position: relative;
    z-index: 1;
}

.lms-banner-area-3::after {
    position: absolute;
    content: "";
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    background: #1d242d;
    opacity: 0.6;
    z-index: -1;
    border-radius: inherit;
}

.lms-banner-slide {
    max-width: 1000px;
    width: 100%;
    margin: 0 auto;
    padding-bottom: 57px;
}

.text-white-highlight1 {
    font-family: 'Lexend Deca';
    font-weight: 600;
    font-size: 16px;
    line-height: 21px;
    letter-spacing: 0.2em;
    color: var(--whiteColor);
    text-align: center;
}

.lms-banner-slide .btn-white1 {
    margin: 0 auto;
}

.btn-white1 {
    display: flex;
    align-items: center;
    justify-content: center;
    column-gap: 9.24px;
    padding: 16px 20px;
    border-radius: 10px;
    background: var(--whiteColor);
    max-width: max-content;
    font-family: 'Lato';
    font-weight: 600;
    font-size: 15px;
    color: #1d242d;
    transition: .3s;
    border: none;
}

.btn-white1 span::before {
    display: block;
    margin-bottom: -1px;
    color: #838b95;
}

.btn-white1:active,
.btn-white1:hover {
    box-shadow: 0 14px 32px 0 rgba(0, 0, 0, 0.4);
    background: var(--whiteColor) !important;
    color: #1d242d !important;
}

.btn-white1:hover span::before {
    color: #838b95;
}

.banner-swiper-1 .swiper-horizontal>.swiper-pagination-bullets,
.banner-swiper-1 .swiper-pagination-bullets.swiper-pagination-horizontal,
.banner-swiper-1 .swiper-pagination-custom,
.banner-swiper-1 .swiper-pagination-fraction {
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 10.35px;
}

.banner-swiper-1 .swiper-pagination-bullet {
    height: 8.62px;
    width: 8.62px;
    background: #b6bdc6;
    opacity: 1;
    transition: .3s;
}

.banner-swiper-1 .swiper-pagination-bullet-active {
    height: 10.35px;
    width: 10.35px;
    background: var(--whiteColor);
}

.banner-swiper-1 .swiper-horizontal>.swiper-pagination-bullets .swiper-pagination-bullet,
.banner-swiper-1 .swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet {
    margin: 0 4.75px;
}

/* Categories */
.icon-box-md {
    height: 84.64px;
    width: 84.64px;
    border-radius: 16px;
    background: #f2f4f7;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Card */
.card-discount1 {
    width: 65.17px;
    height: 65.17px;
    background: url(../image/card-discount-shape.svg) no-repeat scroll center center / cover;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    font-family: 'Lato';
    font-weight: 800;
    font-size: 15px;
    line-height: 18px;
    color: var(--whiteColor);
    position: absolute;
    right: 12.5px;
    top: 12.5px;
}

.card-icon-text2>span::before {
    display: block;
    color: #1D242D;
    margin-top: -1px;
    font-size: 14px;
}

.card-rating2>img {
    margin-top: -2px;
}

.card-rating2>.rating {
    font-family: 'Mulish';
    font-weight: 700;
    font-size: 15px;
    line-height: 26px;
    color: #1d242d;
}

.mk-old-price {
    font-family: 'Lato';
    font-weight: 700;
    font-size: 14px;
    line-height: 26px;
    text-decoration: line-through;
    color: #838b95;
}

.mk-card-price .mk-old-price {
    margin-bottom: -3px;
}

.btn-dark-1 {
    display: block;
    max-width: max-content;
    border: none;
    background: #1d242d;
    border-radius: 10px;
    padding: 9px 16.3px;
    font-family: 'Lato';
    font-weight: 600;
    font-size: 13px;
    line-height: normal;
    color: var(--whiteColor);
    transition: .3s;
}

.btn-dark-1:active,
.btn-dark-1:hover {
    background: #39495d !important;
    color: var(--whiteColor) !important;
}

/* Counter */
.counter-section-2 {
    background: url(../images/img/counter-banner-2.webp) no-repeat scroll center center / cover;
    position: relative;
    z-index: 1;
}

.counter-section-2::after {
    position: absolute;
    content: "";
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    z-index: -1;
    background: #1d242d;
    opacity: 0.9;
}

.counter-area-wrap2 {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    row-gap: 30px;
    padding: 55.75px 0;
}

.counter-single-item2 {
    position: relative;
}

.counter-single-item2:not(:last-child)::after {
    position: absolute;
    content: "";
    right: 0px;
    width: 1px;
    height: 77px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(224, 231, 240, 0.3);
}

/* Video */
.video-banner-area1 {
    width: 100%;
    padding-right: 48px;
    position: relative;
}

.video-banner-area1>img {
    width: 100%;
    border-radius: 16px;
}

.video-banner-area1 .play-btn-2 {
    position: absolute;
    content: "";
    right: 0;
    top: 50%;
    transform: translateY(-50%);
}

.play-btn-2 {
    border: 6px solid var(--whiteColor);
    background: #1d242d;
    height: 104px;
    width: 104px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.play-btn-2 img {
    margin-left: 4px;
}

/* QNA Accordion */
.qnaaccordion-four .accordion-item {
    border: none;
}

.qnaaccordion-four .accordion-item:not(:last-child):has(.accordion-button.collapsed) {
    border-bottom: 1px solid rgba(143, 145, 155, 0.3);
    transition: .3s;
}

.qnaaccordion-four .accordion-item:not(:last-child) {
    border-bottom: 1px solid #1d242d;
}

.qnaaccordion-four .accordion-item,
.qnaaccordion-four .accordion-button {
    border-radius: 0;
}

.qnaaccordion-four .accordion-button {
    padding: 0;
    padding-right: 10px;
    transition: .3s;
    font-family: 'Lato';
    font-weight: 600;
    font-size: 18px;
    line-height: 22px;
    color: #1d242d;
}

.qnaaccordion-four .accordion-item:not(:last-child) .accordion-button {
    padding-bottom: 30px;
}

.qnaaccordion-four .accordion-item:not(:first-child) .accordion-button {
    padding-top: 30px;
}

.qnaaccordion-four .accordion-button:focus {
    box-shadow: none;
}

.qnaaccordion-four .accordion-button:not(.collapsed) {
    background-color: inherit;
    color: #1d242d;
    box-shadow: none;
    padding-bottom: 15px !important;
}

.qnaaccordion-four .accordion-body {
    padding: 0;
    padding-bottom: 24px;
    padding-right: 30px;
}

.qnaaccordion-four .accordion-button::after {
    background-size: 15px;
    background-image: url(../image/plus-gray-15.svg);
    width: 15px;
    height: 15px;
}

.qnaaccordion-four .accordion-button:not(.collapsed)::after {
    background-image: url(../image/minus-black-15-2.svg);
}

/* Testimonial */
.lms-testimonial-2 {
    padding: 20px 0 50px 0;
}

.lms-testimonial-2 .swiper-slide {
    height: auto;
}

.lms-single-testimonial2 {
    height: 100%;
    padding: 16px 16px 12px 16px;
}

.testimonial-profile-wrap2 {
    gap: 22px;
}

.testimonial-profile-2 {
    min-width: 82px;
    width: 82px;
    height: 82px;
}

.testimonial-profile-2>img {
    width: 100%;
    height: 100%;
    border-radius: 20px;
    object-fit: cover;
}

.testimonial-quate-1 {
    width: 43px;
    margin-top: 14px;
}

.testimonial-quate-1>img {
    width: 100%;
}

/* Subscribe Area */
.subscribe-area-wrap1 {
    border-radius: 16px;
    background: #1d242d;
}

.subscribe-area-banner1 {
    width: 100%;
    height: 100%;
}

.subscribe-area-banner1>img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 16px 0 0 16px;
}

.subscribe-area-1 {
    padding: 56px 79px;
}

.subscribe-form-inner {
    gap: 10px;
}

.btn-white1-sm {
    padding: 10.25px 15px;
    font-size: 13px;
}

.sub1-form-control {
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.06);
    padding: 9.25px 16px 9.25px 38px;
    font-family: 'Outfit';
    font-weight: 400;
    font-size: 13px;
    color: #838b95;
    transition: .3s;
    background-repeat: no-repeat;
    background-size: 14px 13px;
    background-position: 16px center;
    background-image: url(../image/message-gray-12.svg);
    max-width: 284px;
    width: 100%;
}

.sub1-form-control::placeholder {
    color: #838b95;
}

.sub1-form-control:focus {
    color: #838b95;
    background-color: rgba(255, 255, 255, 0.06);
}

.sub1-form-control:focus,
.sub1-form-control:hover {
    border-color: var(--whiteColor);
}

/* blog */
.mk-blog-banner {
    width: 100%;
    border-radius: 16px;
    overflow: hidden;
    position: relative;
}

.mk-blog-banner::after {
    position: absolute;
    content: "";
    left: 0;
    bottom: 0;
    width: 100%;
    height: calc(100% - 48px);
    border-radius: inherit;
    background: linear-gradient(360deg, rgb(29 36 45) 0%, rgb(29 36 45 / 0%) 60%);
}

.mk-blog-banner>img {
    width: 100%;
}

.mk-blog-body {
    margin: -50px 20px 0px 20px;
    position: relative;
    width: calc(100% - 40px);
}

.card-icon-text3 {
    column-gap: 5px;
}

.card-icon-text3 span::before {
    display: block;
    font-size: 12px;
    margin-top: -1px;
    color: #838b95;
}

.mk-blog-icontext {
    padding-top: 12px;
    border-top: 1px solid rgba(143, 145, 155, 0.3);
}

/* ===================================================================
                        Marketplace CSS End
====================================================================*/


/* ===================================================================
                        University CSS Start
====================================================================*/
/* New Start */
.uv-banner-content {
    margin-bottom: 44px;

}
.uv-banner-content .title-5 span.highlight-title{
    position: relative;
    display: inline-block;
}
.uv-banner-content .title-5 span.highlight-title::after{
    content: "";
    background-image: url(../image/highlight_right.png);
    background-size: auto;
    height: 50px;
    background-repeat: no-repeat;
    width: 50px;
    position: absolute;
    top: -8px;
    right: -42px;
    z-index: 99;
}
.uv-banner-content .title-5 span.highlight-title::before{
    content: "";
    background-image: url(../image/highlight_left.png);
    background-size: auto;
    height: 50px;
    background-repeat: no-repeat;
    width: 50px;
    position: absolute;
    top: -15px;
    left: -38px;
    z-index: 99;
}

.scale-slider-main .swiper-button-next,
.scale-slider-main .swiper-rtl .swiper-button-prev {
    right: 0px;
}

.scale-slider-main .swiper-button-prev,
.scale-slider-main .swiper-rtl .swiper-button-next {
    left: 0px;
}

.scale-slider-main .swiper-button-next,
.scale-slider-main .swiper-button-prev {
    position: absolute;
    width: 44px;
    height: 44px;
    margin-top: -22px;
}

.scale-slider-main .swiper-button-prev::after,
.scale-slider-main .swiper-rtl .swiper-button-next::after {
    content: '';
    height: 100%;
    width: 100%;
    background: url(../image/arrow-left-white-44.svg) no-repeat scroll center center / cover;
    transition: .3s;
}

.scale-slider-main .swiper-button-prev:hover::after,
.scale-slider-main .swiper-rtl .swiper-button-next:hover::after {
    background: url(../image/arrow-left-red-44.svg) no-repeat scroll center center / cover;
    transition: .3s;
}

.scale-slider-main .swiper-button-next::after,
.scale-slider-main .swiper-rtl .swiper-button-prev::after {
    content: '';
    height: 100%;
    width: 100%;
    background: url(../image/arrow-right-white-44.svg) no-repeat scroll center center / cover;
    transition: .3s;
}

.scale-slider-main .swiper-button-next:hover::after,
.scale-slider-main .swiper-rtl .swiper-button-prev:hover::after {
    background: url(../image/arrow-right-red-44.svg) no-repeat scroll center center / cover;
    transition: .3s;
}

.scale-slider-main .swiper-slide {
    height: auto;
}

.scale-single-slide {
    border-radius: 20px;
    padding: 20px 16px;
    background: transparent;
    position: relative;
    z-index: 1;
    height: 100%;
}

.scale-single-slide::after {
    position: absolute;
    content: "";
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    border-radius: inherit;
    outline: 1px solid #FF375E;
    outline-offset: -2px;
    z-index: 5;
}

.scale-slide-image {
    border-radius: 20px;
    height: 100%;
    width: 100%;
}

.scale-slide-image>img {
    border-radius: 20px;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.lms-banner-section4 {
    padding-top: 60px;
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.lms-banner-section4::after {
    position: absolute;
    content: "";
    left: 0;
    top: 0;
    width: 100%;
    height: calc(100% - 269px);
    background: #121421;
    z-index: -2;
}

.lms-video .plyr {
    z-index: 6;
}

/* Video */
.lms-video {
    width: 100%;
}

.lms-video .plyr--video {
    width: 100%;
    border-radius: 16px;
}

.lms-video .plyr__control--overlaid {
    background: #ff2458;
    color: var(--whiteColor);
    outline: 1px solid var(--whiteColor);
    outline-offset: 5px;
}

.lms-video .plyr__control svg {
    height: 14px;
    width: 14px;
}

.lms-video .plyr--video .plyr__control:hover {
    background: #ff2458;
    color: var(--whiteColor);
}

.lms-video .plyr--full-ui input[type=range] {
    color: #ff2458;
}

.lms2-video {
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 6;
}

.lms2-video iframe {
    width: 100%;
    height: 100%;
    border-radius: 16px;
}

.uv-banner-shape1 {
    position: absolute;
    left: 0;
    height: 830px;
    width: 436px;
    top: -91px;
    background: url(../image/uv-banner-shape1.svg) no-repeat scroll center center / Cover;
    z-index: -1;
    opacity: 0.4;
}

.uv-banner-shape2 {
    position: absolute;
    right: 0;
    height: 830px;
    width: 436px;
    bottom: 0;
    background: url(../image/uv-banner-shape2.svg) no-repeat scroll center center / Cover;
    z-index: -1;
    opacity: 0.4;
}

/* New End */
/* .lms-banner-section4 {
    padding-top: 60px;
    background: #121421;
} */
.max-w-620px {
    max-width: 620px;
    width: 100%;
    margin: 0 auto;
}
.max-w-765px {
    max-width: 765px;
    width: 100%;
    margin: 0 auto;
}
.max-w-850px {
    max-width: 765px;
    width: 100%;
    margin: 0 auto;
}

/* Service */
.service-card-banner-2 {
    width: 100%;
}

.service-card-banner-2>img {
    width: 100%;
    border-radius: 20px;
}

/* Community */
.community-banner-2 {
    padding-top: 20px;
    padding-left: 20px;
    width: 100%;
    position: relative;
    z-index: 1;
}

.community-banner-2>img {
    width: 100%;
    border-radius: 20px;
}

.community-banner-2::after {
    position: absolute;
    content: "";
    left: 0;
    top: 0;
    height: 63.63px;
    width: 107.81px;
    background: url(../images/shape/community2-shape1.svg) no-repeat scroll center center / cover;
    z-index: -1;
}

.community-service-banner2 {
    height: 41.47px;
    min-width: 41.47px;
    width: 41.47px;
}

.community-service-banner2>img {
    height: 100%;
    width: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.btn-danger-1 {
    border: none;
    transition: .3s;
    border-radius: 2px 10px;
    background: #ff2458;
    padding: 13.5px 26px;
    font-family: 'DM Sans';
    font-weight: 500;
    font-size: 15px;
    line-height: normal;
    color: var(--whiteColor);
}

.btn-danger-1:active,
.btn-danger-1:hover {
    background: #ff2458bd !important;
    color: var(--whiteColor) !important;
}

/* Section Title */
.section-title-2 {
    max-width: 580px;
    width: 100%;
    margin: 0 auto;
}

.imagebg-btn-card {
    position: relative;
    overflow: hidden;
    width: 100%;
}

.imagebg-btn-card>img {
    border-radius: 27px;
    width: 100%;
}

.btn-whitelight {
    border-radius: 14px;
    box-shadow: 0 14px 32px 0 rgba(147, 148, 158, 0.2);
    background: rgba(255, 255, 255, 0.9);
    padding: 18.5px 26px;
    font-family: 'DM Sans';
    font-weight: 700;
    font-size: 22px;
    line-height: 31px;
    color: #121421;
    border: none;
    transition: .3s;
}

.btn-whitelight:active,
.btn-whitelight:hover {
    background: rgba(255, 255, 255, 1) !important;
    color: #121421 !important;
}

.card-position-btn1 {
    position: absolute;
    bottom: -70px;
    left: 30px;
    width: calc(100% - 60px);
    visibility: hidden;
    opacity: 0;
    pointer-events: none;
}

.imagebg-btn-card:hover .btn-whitelight {
    visibility: visible;
    opacity: 1;
    pointer-events: auto;
    bottom: 30px;
}

.list-view-banner2 {
    max-width: 233px;
    width: 100%;
    aspect-ratio: 233 / 210;
}

.list-view-banner2>img {
    height: 100%;
    width: 100%;
    object-fit: cover;
    border-radius: 10px;
}

.card-leason-rating2 {
    padding-bottom: 12px;
    margin-bottom: 14px;
    border-bottom: 1px solid rgba(143, 145, 155, 0.3);
}

.card-icon-text4>span::before {
    display: block;
    color: var(--skinColor6);
    margin-top: -1px;
    font-size: 14px;
}

.card-rating3>img {
    margin-top: -2px;
}

/* Event */
.lms-event1-banner {
    max-width: 313px;
    width: 100%;
}

.lms-event1-banner>img {
    height: 170px;
    width: 100%;
    border-radius: 20px;
    object-fit: cover;
}

.lms-icon-text1>span::before {
    display: block;
    color: var(--skinColor6);
    margin-top: -1px;
    font-size: 14px;
}

.lms-author-sm {
    min-width: 30px;
    width: 30px;
    height: 30px;
}

.event-details-banner-wrap {
    column-gap: 47px;
    row-gap: 24px;
}

.lms-event-single1:not(:last-child) {
    margin-bottom: 30px;
}

.lms-event-single1:not(:last-child) .event-details-banner-wrap {
    padding-bottom: 30px;
    border-bottom: 1px solid rgba(143, 145, 155, 0.3);
}

.lms-event-number {
    max-width: 147px;
    width: 100%;
    text-align: center;
    position: relative;
}

.lms-event-single1:not(:last-child) .lms-event-number::after {
    position: absolute;
    content: "";
    left: 50%;
    top: 83px;
    width: 1px;
    height: calc(100% - 85px);
    background: var(--skinColor6);
}

/* Accordion */
.qnaaccordion-five .accordion-button {
    padding: 0;
    transition: .3s;
    border-color: rgba(143, 145, 155, 0.3);
}

.qnaaccordion-five .accordion-item:not(:first-child) .accordion-button {
    padding-top: 30px;
}

.qnaaccordion-five .accordion-item:not(:last-child) .accordion-button {
    padding-bottom: 30px;
    border-bottom: 1px solid rgba(143, 145, 155, 0.3);
}

.qnaaccordion-five .accordion-button:focus {
    box-shadow: none;
}

.qnaaccordion-five .accordion-button:not(.collapsed) {
    box-shadow: none;
    background-color: transparent;
    color: var(--skinColor6);
    padding-bottom: 20px !important;
    border-bottom: 1px solid rgba(143, 145, 155, 0.3);
}

.qnaaccordion-five .accordion-button::after {
    background-image: none;
    content: "\f12d";
    font-family: uicons-regular-rounded !important;
    font-style: normal;
    font-weight: bold !important;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 12px;
    height: auto;
    width: auto;
}

.qnaaccordion-five .accordion-button:not(.collapsed)::after {
    transform: rotate(90deg);
}

.qnaaccordion-five .accordion-item {
    border: none;
}

.qnaaccordion-five .accordion-body {
    padding: 0;
    padding-bottom: 10px;
    padding-top: 20px;
}

.qnaaccordion-five .accordion-item:last-child .accordion-body {
    padding-bottom: 0px;
}

.list-group-card {
    border-radius: 16px;
    border: 1px solid rgba(143, 145, 155, 0.3);
    overflow: hidden;
}

.list-group-card-item {
    padding: 16px 28px;
    border-bottom: 1px solid rgba(143, 145, 155, 0.3);
}

.list-group-card-item .title-5 {
    color: #93949e;
}


.list-group-card li .title-5 {
    min-width: 33.33%;
}

.list-group-card-header {
    background: var(--skinColor6);
    padding: 15px 28px;
}

.list-group-card-footer {
    background: rgba(191, 191, 191, 0.3);
    padding: 15px 28px
}

.link-btn-hover2 {
    transition: .3s;
}

.link-btn-hover2:hover {
    color: var(--skinColor6);
}

.lms-1-card:hover .link-btn-hover2 {
    color: var(--skinColor6);
}

.capitalize {
    text-transform: capitalize;
}

.cook_slider .item h1 {
    font-size: 25px;
    font-weight: 600;
    color: #010101;
    margin-bottom: 5px;
}


.eThink {}

.eThink .lms-event-single1:not(:last-child) .lms-event-number::after {
    background-color: var(--skinColor3);
}

.eThink .lms-icon-text1>span::before {
    color: var(--skinColor3);
}

.lms-author-sm img {
    border-radius: 50%;
    object-fit: cover;
}

.slider_btn {
    display: flex;
    justify-content: center;
    margin: auto;
    margin-bottom: 60px;
    margin-top: -60px;
    position: relative;
    z-index: 9;
}

.posejourney-overley .info {
    text-transform: capitalize;
}

.social-link-1 i {
    color: #264871;
}

.h-190px {
    height: 190px;
    object-fit: cover;
}

.h-230px {
    height: 230px !important;
    object-fit: cover;
}

.radious-0 {
    border-radius: 0 !important;
}

.popular-instructor-socila li a i {
    color: #00907F;
}

/* ===================================================================
                        University CSS End
====================================================================*/
/* 8 home page code ended */




.Userprofile .dropmenu-end a {
    padding: 6px 10px;
    margin-bottom: 0px;
}

.course-motion-top {
    justify-content: flex-start;
}

.hero-details {
    margin-top: 20px;
}

.page-content-pb-100 {
    padding-bottom: 40px !important;
}

@media only screen and (min-width: 992px) {
    .page-static-sidebar {
        position: sticky;
        top: 5px;
        margin-top: -365px;
    }

    .hero-details,
    .details-page-content {
        margin-top: -100px;
    }

    .page-content-pb-100 {
        padding-bottom: 100px !important;
    }
}

.course-details h2.g-title {
    margin: 30px 0px 20px 0px !important;
}

.course-details .g-text {
    margin-bottom: 30px;
}

.playing-breadcum {
    height: 500px;
}


.course-motion-top li {
    min-width: 166px;
}

.player-feature .hero-details {
    top: 0px;
}

.hero-details img {
    height: auto;
}

.fDetails-tab {
    max-width: unset;
}

.profile-banner-area {
    background: linear-gradient(90deg, #f57d30 26%, #f68034 93%);
    width: 100%;
    height: 310px;
    position: relative;
    z-index: -1;
}

.profile-banner-area:after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, rgb(255 255 255 / 94%) 0%, rgb(255 255 255 / 35%) 70%);
    z-index: 0;
}

.eNtery-item {
    position: relative;
}

.breadcum-area {
    background: linear-gradient(90deg, #8ca3ff 26%, #d58dff 93%);
    position: relative;
    z-index: 0;
}

.breadcum-area:after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, rgb(255 255 255 / 94%) 0%, rgb(255 255 255 / 35%) 70%);
    z-index: -1;
}

.breadcum-area.bg-white:after,
.breadcum-area.bg-default:after {
    background: linear-gradient(180deg, rgb(255 255 255 / 94%) 0%, rgb(255 255 255 / 68%) 70%) !important;
}

.profile-banner-area-container {
    margin-top: -280px;
}

.profile-info {
    position: relative;
}

.profile-info img.photo {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    margin: auto;
    text-align: center;
}

.profile-info .name,
.profile-info .email {
    text-align: center;
    font-size: 15px;
}

.profile-info .name {
    margin: 16px 0px;
    font-size: 24px;
    font-weight: 600;
    color: var(--color-2);

}

.profile-info .upload-new {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    text-align: center;
    position: absolute;
    top: 0;
    left: 33px;
}

.profile-info .upload-new:hover a {
    display: flex;

}

.profile-info .upload-new>a {
    width: 100%;
    height: 100%;
    display: none;
    align-items: center;
    text-align: center;
    justify-content: center;
    background-color: #0000007a;
    color: #fff;
    border-radius: 50%;
}

@media (max-width: 576px) {
    .toggle-bar {
        width: 36px;
    }

    .us-btn:after {
        display: none;
    }

    .Userprofile {
        width: 48px;
    }
}

.sub-header-left .nice-select {
    padding: 0px;
}

.eNtry-breadcum .breadcrumb-item.active {
    color: #000;
}

.tab-list li.active {
    border-radius: 6px;
}

.tooltip {
    font-size: 13px;
}

.active-image.no-status::after,
.active-image.no-status::before {
    display: none;
}

/*Start common css*/
.ellipsis-line-1 {
    display: -webkit-box !important;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: normal !important;
}

.ellipsis-line-2 {
    display: -webkit-box !important;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: normal !important;
}

.ellipsis-line-3 {
    display: -webkit-box !important;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: normal !important;
}

.ellipsis-line-4 {
    display: -webkit-box !important;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
}

.ellipsis-line-5 {
    display: -webkit-box !important;
    -webkit-line-clamp: 5;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
}

.ellipsis-line-12 {
    display: -webkit-box !important;
    -webkit-line-clamp: 12;
    -webkit-box-orient: vertical !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: normal !important;
}

.ellipsis-line-20 {
    display: -webkit-box !important;
    -webkit-line-clamp: 20;
    -webkit-box-orient: vertical !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: normal !important;
}

.alert-purple {
    color: #b94aff;
    background-color: #c364ff1a;
    border-color: #c364ff45;
}

.alert-purple a {
    color: #b94aff;
}

.image-100 {
    min-width: 100px !important;
    width: 100px !important;
    height: 100px !important;
    border-radius: 50% !important;
    object-fit: cover !important;
}

.image-80 {
    width: 80px !important;
    min-width: 80px !important;
    height: 80px !important;
    border-radius: 50% !important;
    object-fit: cover !important;
}

.image-50 {
    width: 50px !important;
    min-width: 50px !important;
    height: 50px !important;
    border-radius: 50% !important;
    object-fit: cover !important;
}

.image-45 {
    width: 45px !important;
    min-width: 45px !important;
    height: 45px !important;
    border-radius: 50% !important;
    object-fit: cover !important;
}

.image-40 {
    width: 40px !important;
    min-width: 40px !important;
    height: 40px !important;
    border-radius: 50% !important;
    object-fit: cover !important;
}

.image-35 {
    width: 35px !important;
    min-width: 35px !important;
    height: 35px !important;
    border-radius: 50% !important;
    object-fit: cover !important;
}

.image-30 {
    width: 30px !important;
    min-width: 30px !important;
    height: 30px !important;
    border-radius: 50% !important;
    object-fit: cover !important;
}

.footer-content img,
.logo-image img {
    width: auto !important;
}

.blog-post1-link .banner img {
    object-fit: cover;
}

/* floating searchbar for mobile app */
@media only screen and (min-width: 992px) {
    .floating-searchbar {
        display: none;
    }
}

.floating-searchbar {
    position: relative;
}

.floating-searchbar>form {
    width: 250px;
    position: absolute;
    right: 0;
    top: 60px;
    z-index: 1001;
    display: flex;
    visibility: hidden;
    opacity: 0;
    transition: opacity .3s, margin .3s;
}

.primary-end .nice-control .current {
    display: inline-block !important;
    padding: 0px 6px !important;
}


.floating-searchbar button {
    background-color: transparent;
    height: 100%;
}

.floating-searchbar form>button {
    background-color: #fff;
    height: 42px;
    width: 42px;
    margin: 2px;
    margin-left: -45px;
    border-radius: 10px;
}

.floating-searchbar>form.show {
    visibility: visible;
    opacity: 1;
    margin-top: -4px;
    transition: opacity .3s, margin .3s;
}

/* floating searchbar for mobile app ENDED*/

.modal-spinner-border {
    width: 100px;
    height: 100px;
    line-height: 100px;
    padding: 0px;
    text-align: center;
    margin-left: auto;
    margin-right: auto;
}

.color-linkedin {
    color: #0077b5;
}

.color-whatsapp {
    color: #128c7e;
}

.color-twitter {
    color: #1DA1F2;
}

.color-facebook {
    color: #316FF6;
}

.bg-color-e9f6ff {
    background-color: #e9f6ff;
}

.bg-color-ffeff8-f6f0f4 {
    background: linear-gradient(160deg, #ffeff8 10.78%, #f6f0f4 91.29%);
    ;
}

.bg-color-e8f7fc-f1f9fc {
    background: linear-gradient(164deg, #e8f7fc 0%, #f1f9fc 100%);
}

.Userprofile img,
.profile-info img.photo {
    object-fit: cover;
}

.primary-menu .have-mega-menu .menu-parent-a {
    position: relative;
}

.primary-menu .have-mega-menu .menu-parent-a::after {
    vertical-align: middle;
    border: 6px solid #6c7486;
    border-right: 6px solid transparent;
    border-bottom: 0;
    border-left: 6px solid transparent;
    border-radius: 3px;
    background: #fff;
    position: absolute;
    right: -18px;
    top: 9px;
    left: auto;
    width: 10px;
}

.primary-menu .have-mega-menu .menu-parent-a.active::after,
.primary-menu .have-mega-menu .menu-parent-a:hover::after {
    vertical-align: middle;
    border: 6px solid #2f57ef;
    border-right: 6px solid transparent;
    border-bottom: 0;
    border-left: 6px solid transparent;
    border-radius: 3px;
    background: #fff;
    position: absolute;
    right: -18px;
    top: 9px;
    left: auto;
    width: 10px;
}

.description-style {
    color: #3b3b3b;
}

.description-style h1,
.description-style h2,
.description-style h3,
.description-style h4,
.description-style h5,
.description-style h6 {
    margin-bottom: 14px;
}

.description-style h1,
.description-style h2,
.description-style h3,
.description-style h4,
.description-style h5,
.description-style h6,
.description-style strong {
    color: #232323;
}

.description-style ul,
.description-style ol {
    list-style: initial;
    margin: initial;
    padding: 0px 0px 0px 17.5px;
}
.description-style-sm p{
    font-size: 14px;
}

.card.Ecard.eBar-card {
    height: 400px;
}

.lms-testimonial-1 {
    padding: 40px 22px;
}

.single-testimonial1-inner {
    height: 320px;
}

.ps-box {
    position: relative;
    z-index: 2;
}

.breadcum-area {
    z-index: 0 !important;
}

.header-area .offcanvas-body {
    height: 100%;
    overflow-y: auto;
}

.user-slider.owl-carousel .owl-nav:not(:nth-last-child(2)) {
    display: none;
}

.offcanvas-body .list-unstyled .btn-toggle:not(.collapsed) {
    color: var(--color-1) !important;
}

.offcanvas-body .list-unstyled button:hover,
.offcanvas-body .list-unstyled button.active,
.offcanvas-body .list-unstyled a:hover,
.offcanvas-body .list-unstyled a.active {
    color: var(--color-1) !important;
}

.bg-pink {
    background-color: #d336e7 !important;
}

.main-search-item {
    width: 450px !important;
}

.btn-join-now {
    color: #2f57f0 !important;
    font-weight: 600;
}

.nav.nav-bordered .nav-link {
    border-bottom: 2px solid #2f56f000;
    color: #131315;
}

.nav.nav-bordered .nav-link:hover,
.nav.nav-bordered .nav-link.active {
    border-bottom: 2px solid #2f57f0;
    color: #2f57f0;
}

/* overlap content start*/
.overlay-content.show-more {
    max-height: 125px;
    height: min-content;
    overflow-y: hidden;
    position: relative;
}

.overlay-content-max-h-800.show-more {
    max-height: 800px;
}

.overlay-content-max-h-700.show-more {
    max-height: 700px;
}

.overlay-content-max-h-600.show-more {
    max-height: 600px;
}

.overlay-content-max-h-500.show-more {
    max-height: 500px;
}

.overlay-content-max-h-400.show-more {
    max-height: 400px;
}

.overlay-content-max-h-300.show-more {
    max-height: 300px;
}

.overlay-content-max-h-200.show-more {
    max-height: 200px;
}

.overlay-content-max-h-150.show-more {
    max-height: 150px;
}

.overlay-content-max-h-100.show-more {
    max-height: 100px;
}

.overlay-content.show-less {
    position: relative;
    padding-bottom: 40px;
}

.overlay-content.show-more p:last-child {
    position: absolute;
    width: -webkit-fill-available;
    z-index: 1111;
    bottom: 0px;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 50%);
    padding: 30px 0px 6px 0px;
}

.overlay-content.show-less p:last-child {
    padding: 5px 0px;
    width: -webkit-fill-available;
    position: absolute;
    z-index: 1111;
    bottom: -8px;
}

/* overlap content ended*/
.sub-header li.primary-end {
    height: 22px !important;
    margin-top: 4px !important;
}

.testimonial-border .user-info {
    margin-top: 8px;
}

.fw-300 {
    font-weight: 300 !important;
}

.fw-400 {
    font-weight: 400 !important;
}

.fw-500 {
    font-weight: 500 !important;
}

.fw-600 {
    font-weight: 600 !important;
}

.fw-700 {
    font-weight: 700 !important;
}

.fw-800 {
    font-weight: 800 !important;
}

.fw-900 {
    font-weight: 900 !important;
}

.course-card1-inner {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.course-card1-details {
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: space-between;
}

.course-card1-link,
.blog-post1-link,
.dev-course-card,
.dev-news-card,
.lms-1-card{
    transition: box-shadow 0.3s !important;
}
.course-card1-link:hover,
.blog-post1-link:hover,
.dev-course-card:hover,
.dev-news-card:hover,
.lms-1-card:hover {
    box-shadow: 0 14px 32px 0 rgb(147 148 158 / 41%) !important;
    transition: box-shadow 0.3s !important;
}

.elegant-testimonial-slide {
    box-shadow: 0px 4px 10px rgba(147, 148, 158, 0.2);
}

.elegant-testimonial-slide:hover {
    box-shadow: 0px 4px 10px rgba(147, 148, 158, 0.339);
}

.meditation-testimonial-1 {
    padding-bottom: 28px;
}

.swiper-button-prev,
.swiper-button-next {
    border-radius: 50%;
    height: 50px;
    width: 50px;
    border: 1px solid #000;
    color: #000;
    transition: 0.3s;
}

.swiper-button-prev:hover,
.swiper-button-next:hover {
    background-color: #000;
    color: #fff;

    transition: 0.3s;
}

.swiper-button-next:after,
.swiper-button-prev:after {
    font-size: 25px;
}

.swiper-button-prev:after,
.swiper-rtl .swiper-button-next:after {
    content: "\f16b";
    font-family: 'uicons-regular-rounded';
}

.swiper-button-next:after,
.swiper-rtl .swiper-button-prev:after {
    content: "\f16c";
    font-family: 'uicons-regular-rounded';
}

.swiper-horizontal .row .swiper-scrollbar {
    height: 5px;
    width: 240px;
}

@media all and (max-width:400px) {
    .swiper-horizontal .row .swiper-scrollbar {
        height: 5px;
        width: 180px;
    }
}

.swiper-scrollbar-drag {
    background: #000;
}

.swiper-scrollbar {
    background: #fff;
    box-shadow: 0px 0px 5px 2px #413d3d0a;
}

.swiper-button-prev {
    left: 20px;
}

.swiper-button-next {
    right: 20px;
}

@media all and (max-width:576px) {
    .swiper-slide {
        padding-left: 15px;
        padding-right: 15px;
    }
}
.meditation-testimonial-1 .swiper-slide{
    height: unset;
}
.kindergarden-home .swiper-button-prev{
    left: 0px;
}
.kindergarden-home .swiper-button-next{
    right: 0px;
}





#schedule-list .swiper-button-prev, #schedule-list .swiper-button-next{
    border: none !important;
}
.swiper-button-prev:after, .swiper-rtl .swiper-button-next:after{
    content: "\f12c" !important;
}
.swiper-button-next:after, .swiper-rtl .swiper-button-prev:after{
    content: "\f12d" !important;
}

.breadcum-area {
    background: linear-gradient(90deg, #f68034 26%, #f67f33 93%);
}
.breadcum-area .tab-list .gradient {
    color: #f68034 !important;
    background-image: linear-gradient(to right, #f68034 0%, #f68034 51%, #f68034 100%);
}

.breadcum-area .tab-list .gradient svg * {
    fill: #f68034 !important;
}
