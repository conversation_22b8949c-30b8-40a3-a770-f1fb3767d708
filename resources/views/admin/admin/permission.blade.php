@extends('layouts.admin')
@push('title', '<PERSON><PERSON> quyền')
@push('meta')@endpush
@push('css')
<style>
    .permission-group {
        margin-bottom: 20px;
        border: 1px solid #eee;
        border-radius: 8px;
    }
    .permission-group-header {
        padding: 10px 15px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #eee;
        cursor: pointer;
        border-radius: 8px 8px 0 0;
    }
    .permission-group-header h5 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
    }
    .permission-group-body {
        padding: 0;
    }
    .permission-item {
        padding: 8px 15px;
        border-bottom: 1px solid #f1f1f1;
    }
    .permission-item:last-child {
        border-bottom: none;
    }
    .select-all-checkbox {
        margin-left: 10px;
    }
    .checkmark {
        display: inline-block;
        width: 18px;
        height: 18px;
        background-color: #fff;
        border: 1px solid #ddd;
        border-radius: 3px;
        position: relative;
    }
    .checkmark.checked {
        background-color: #4e73df;
        border-color: #4e73df;
    }
    .checkmark.checked:after {
        content: '';
        position: absolute;
        left: 6px;
        top: 2px;
        width: 5px;
        height: 10px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
    }
    .toggle-icon {
        float: right;
    }
</style>
@endpush
@section('content')
    <div class="ol-card radius-8px">
        <div class="ol-card-body my-3 py-12px px-20px">
            <div class="d-flex align-items-center justify-content-between gap-3 flex-wrap flex-md-nowrap">
                <h4 class="title fs-16px">
                    <i class="fi-rr-settings-sliders me-2"></i>
                    Phân quyền quản trị viên
                </h4>

                <a href="{{ route('admin.admins.index') }}" class="btn ol-btn-outline-secondary d-flex align-items-center cg-10px">
                    <span class="fi-rr-arrow-alt-left"></span>
                    <span>Quay lại</span>
                </a>
            </div>
        </div>
    </div>
    @php
        // Define permissions with their corresponding route names
        $permissionGroups = [
            'Bảng điều khiển' => [
                'admin.dashboard' => 'Bảng điều khiển',
            ],
            'Danh mục' => [
                'admin.categories' => 'Danh mục',
                'admin.category.create' => 'Tạo danh mục',
                'admin.category.edit' => 'Chỉnh sửa danh mục',
                'admin.category.delete' => 'Xóa danh mục',
            ],
            'Khóa học' => [
                'admin.courses' => 'Khóa học',
                'admin.course.create' => 'Tạo khóa học',
                'admin.course.edit' => 'Chỉnh sửa khóa học',
                'admin.course.update' => 'Cập nhật khóa học',
                'admin.course.duplicate' => 'Sao chép khóa học',
                'admin.course.delete' => 'Xóa khóa học',
                'admin.course.status' => 'Trạng thái khóa học',
                'admin.course.draft' => 'Bản nháp khóa học',
                'admin.course.approval' => 'Phê duyệt khóa học',
            ],
            'Chương trình học' => [
                'admin.section.store' => 'Tạo chương',
                'admin.section.update' => 'Cập nhật chương',
                'admin.section.delete' => 'Xóa chương',
                'admin.lesson.store' => 'Tạo bài học',
                'admin.lesson.edit' => 'Chỉnh sửa bài học',
                'admin.lesson.delete' => 'Xóa bài học',
                'admin.section.sort' => 'Sắp xếp chương',
                'admin.lesson.sort' => 'Sắp xếp bài học',
            ],
            'Bài kiểm tra & Câu hỏi' => [
                'admin.course.quiz.store' => 'Tạo bài kiểm tra',
                'admin.course.quiz.update' => 'Cập nhật bài kiểm tra',
                'admin.course.quiz.delete' => 'Xóa bài kiểm tra',
                'admin.quiz.participant.result' => 'Kết quả bài kiểm tra',
                'admin.course.question.store' => 'Tạo câu hỏi',
                'admin.course.question.update' => 'Cập nhật câu hỏi',
                'admin.course.question.delete' => 'Xóa câu hỏi',
                'admin.quiz.result.preview' => 'Xem trước kết quả bài kiểm tra',
                'admin.course.question.sort' => 'Sắp xếp câu hỏi',
                'admin.load.question.type' => 'Tải loại câu hỏi',
            ],
            'Lớp học trực tuyến' => [
                'admin.live.class.store' => 'Tạo lớp học trực tuyến',
                'admin.live.class.update' => 'Cập nhật lớp học trực tuyến',
                'admin.live.class.delete' => 'Xóa lớp học trực tuyến',
                'admin.live.class.start' => 'Bắt đầu lớp học trực tuyến',
                'admin.live.class.settings' => 'Cài đặt lớp học trực tuyến',
            ],
            'Bootcamp' => [
                'admin.bootcamps' => 'Bootcamp',
                'admin.bootcamp.create' => 'Tạo Bootcamp',
                'admin.bootcamp.edit' => 'Chỉnh sửa Bootcamp',
                'admin.bootcamp.delete' => 'Xóa Bootcamp',
                'admin.bootcamp.status' => 'Trạng thái Bootcamp',
                'admin.bootcamp.duplicate' => 'Sao chép Bootcamp',
                'admin.bootcamp.purchase.history' => 'Lịch sử mua Bootcamp',
                'admin.bootcamp.module.store' => 'Tạo module Bootcamp',
                'admin.bootcamp.module.update' => 'Cập nhật module Bootcamp',
                'admin.bootcamp.module.delete' => 'Xóa module Bootcamp',
                'admin.bootcamp.categories' => 'Danh mục Bootcamp',
                'admin.bootcamp.module.sort' => 'Sắp xếp module Bootcamp',
                'admin.bootcamp.purchase.invoice' => 'Hóa đơn mua Bootcamp',
                'admin.bootcamp.live.class.join' => 'Tham gia lớp học trực tuyến Bootcamp',
                'admin.bootcamp.class.end' => 'Kết thúc lớp học Bootcamp',
                'admin.update.on.end.class' => 'Cập nhật khi kết thúc lớp học',
                'admin.bootcamp.resource.download' => 'Tải tài nguyên Bootcamp',
            ],
            'Đào tạo nhóm' => [
                'admin.team.packages' => 'Đào tạo nhóm',
                'admin.team.packages.create' => 'Tạo gói nhóm',
                'admin.team.packages.edit' => 'Chỉnh sửa gói nhóm',
                'admin.team.packages.delete' => 'Xóa gói nhóm',
                'admin.team.packages.duplicate' => 'Sao chép gói nhóm',
                'admin.team.toggle.status' => 'Chuyển đổi trạng thái gói nhóm',
                'admin.team.packages.purchase.history' => 'Lịch sử mua gói nhóm',
                'admin.get.courses.by.privacy' => 'Lấy khóa học theo quyền riêng tư',
                'admin.get.course.price' => 'Lấy giá khóa học',
                'admin.team.packages.purchase.invoice' => 'Hóa đơn mua gói nhóm',
            ],
            'Đặt lịch gia sư' => [
                'admin.tutor_subjects' => 'Môn học gia sư',
                'admin.tutor_subject_create' => 'Tạo môn học gia sư',
                'admin.tutor_subject_edit' => 'Chỉnh sửa môn học gia sư',
                'admin.tutor_subject_delete' => 'Xóa môn học gia sư',
                'admin.tutor_subject_status' => 'Trạng thái môn học gia sư',
                'admin.tutor_categories' => 'Danh mục gia sư',
                'admin.tutor_category_create' => 'Tạo danh mục gia sư',
                'admin.tutor_category_edit' => 'Chỉnh sửa danh mục gia sư',
                'admin.tutor_category_delete' => 'Xóa danh mục gia sư',
                'admin.tutor_category_status' => 'Trạng thái danh mục gia sư',
            ],
            'Học viên' => [
                'admin.student.index' => 'Học viên',
                'admin.student.create' => 'Tạo học viên',
                'admin.student.edit' => 'Chỉnh sửa học viên',
                'admin.student.delete' => 'Xóa học viên',
                'admin.student.status' => 'Trạng thái học viên',
                'admin.student.enrollments' => 'Xem đăng ký học viên',
                'admin.student.enroll' => 'Đăng ký',
                'admin.enroll.history' => 'Lịch sử đăng ký',
                'admin.enroll.history.delete' => 'Xóa lịch sử đăng ký',
                'admin.student.get' => 'Lấy danh sách học viên',
                'admin.student.devices' => 'Quản lý thiết bị học viên',
                'admin.student.device.delete' => 'Xóa thiết bị học viên',
                'admin.student.reset.violations' => 'Đặt lại vi phạm đăng nhập',
                'admin.student.learning.progress' => 'Quản lý lộ trình học của học viên',
            ],
            'Giảng viên' => [
                'admin.instructor.index' => 'Giảng viên',
                'admin.instructor.create' => 'Tạo giảng viên',
                'admin.instructor.edit' => 'Chỉnh sửa giảng viên',
                'admin.instructor.delete' => 'Xóa giảng viên',
                'admin.instructor.course' => 'Khóa học giảng viên',
                'admin.instructor.payout' => 'Thanh toán giảng viên',
                'admin.instructor.payment' => 'Thanh toán giảng viên',
                'admin.instructor.setting' => 'Cài đặt giảng viên',
                'admin.instructor.application' => 'Đơn đăng ký giảng viên',
                'admin.instructor.application.approve' => 'Phê duyệt đơn đăng ký giảng viên',
                'admin.instructor.application.delete' => 'Xóa đơn đăng ký giảng viên',
                'admin.instructor.revenue' => 'Doanh thu giảng viên',
                'admin.instructor.payout.filter' => 'Lọc thanh toán giảng viên',
                'admin.instructor.payout.invoice' => 'Hóa đơn thanh toán giảng viên',
                'admin.instructor.application.download' => 'Tải đơn đăng ký giảng viên',
                'admin.instructor_revenue.delete' => 'Xóa doanh thu giảng viên',
            ],
            'Quản trị viên' => [
                'admin.admins.index' => 'Quản trị viên',
                'admin.admins.create' => 'Tạo quản trị viên',
                'admin.admins.edit' => 'Chỉnh sửa quản trị viên',
                'admin.admins.delete' => 'Xóa quản trị viên',
                'admin.admins.permission' => 'Phân quyền quản trị viên',
                'admin.manage.profile' => 'Quản lý hồ sơ',
            ],
            'Thanh toán & Giao dịch' => [
                'admin.offline.payments' => 'Thanh toán offline',
                'admin.offline.payment.doc' => 'Tài liệu thanh toán offline',
                'admin.offline.payment.accept' => 'Chấp nhận thanh toán offline',
                'admin.offline.payment.decline' => 'Từ chối thanh toán offline',
                'admin.offline.payment.delete' => 'Xóa thanh toán offline',
                'admin.revenue' => 'Doanh thu quản trị',
                'admin.revenue.delete' => 'Xóa doanh thu',
                'admin.purchase.history' => 'Lịch sử mua hàng',
                'admin.invoice' => 'Hóa đơn',
                'admin.purchase.history.invoice' => 'Hóa đơn lịch sử mua hàng',
            ],
            'Marketing' => [
                'admin.newsletter' => 'Bản tin',
                'admin.subscribed_user' => 'Người đăng ký bản tin',
                'admin.newsletters.form' => 'Form bản tin',
                'admin.newsletter.delete' => 'Xóa bản tin',
                'admin.subscribed_user.delete' => 'Xóa người đăng ký',
                'admin.get.user' => 'Lấy người dùng cho bản tin',
                'admin.coupons' => 'Mã giảm giá',
                'admin.coupon.create' => 'Tạo mã giảm giá',
                'admin.coupon.edit' => 'Chỉnh sửa mã giảm giá',
                'admin.coupon.delete' => 'Xóa mã giảm giá',
                'admin.coupon.status' => 'Trạng thái mã giảm giá',
                'admin.marketing.popup' => 'Popup marketing',
                'admin.marketing.popup.store' => 'Tạo popup marketing',
                'admin.marketing.popup.update' => 'Cập nhật popup marketing',
                'admin.marketing.popup.delete' => 'Xóa popup marketing',
            ],
            'Tiếp thị liên kết' => [
                'admin.affiliate' => 'Tiếp thị liên kết',
                'admin.affiliate.withdraws' => 'Rút tiền tiếp thị liên kết',
                'admin.affiliate.settings' => 'Cài đặt tiếp thị liên kết',
                'admin.affiliate.status' => 'Cập nhật trạng thái tiếp thị liên kết',
                'admin.affiliate.withdraw.status' => 'Cập nhật trạng thái rút tiền',
                'admin.affiliate.setting.store' => 'Lưu cài đặt tiếp thị liên kết',
            ],
            'Liên lạc' => [
                'admin.message' => 'Tin nhắn',
                'admin.contacts' => 'Liên hệ người dùng',
                'admin.contact.delete' => 'Xóa liên hệ',
            ],
            'Blog' => [
                'admin.blogs' => 'Blog',
                'admin.blog.create' => 'Tạo blog',
                'admin.blog.edit' => 'Chỉnh sửa blog',
                'admin.blog.delete' => 'Xóa blog',
                'admin.blog.status' => 'Trạng thái blog',
                'admin.blog.pending' => 'Danh sách blog chờ duyệt',
                'admin.blog.category' => 'Danh mục blog',
                'admin.blog.settings' => 'Cài đặt blog',
                'admin.blog.category.create' => 'Tạo danh mục blog',
                'admin.blog.category.delete' => 'Xóa danh mục blog',
            ],
            'Đánh giá' => [
                'admin.review.create' => 'Đánh giá người dùng',
                'admin.review.edit' => 'Chỉnh sửa đánh giá',
                'admin.review.delete' => 'Xóa đánh giá',
            ],
            'Quản lý trang' => [
                'admin.pages' => 'Trang',
                'pages.index' => 'Danh sách trang',
                'pages.create' => 'Tạo trang',
                'pages.store' => 'Lưu trang',
                'pages.edit' => 'Chỉnh sửa trang',
                'pages.update' => 'Cập nhật trang',
                'pages.delete' => 'Xóa trang',
                'pages.status' => 'Trạng thái trang',
                'pages.duplicate' => 'Sao chép trang',
                'admin.page.create' => 'Tạo trang',
                'admin.page.edit' => 'Chỉnh sửa trang',
                'admin.page.delete' => 'Xóa trang',
                'admin.page.status' => 'Trạng thái trang',
                'admin.page.layout.edit' => 'Chỉnh sửa bố cục trang',
                'admin.page.all.builder.developer.file' => 'Lấy file phát triển builder',
                'admin.page.layout.update' => 'Cập nhật bố cục trang',
                'admin.page.layout.image.update' => 'Cập nhật hình ảnh bố cục trang',
                'admin.page.preview' => 'Xem trước trang',
            ],
            'Ghi nhận thông tin người dùng' => [
                'admin.user.inquiries.index' => 'Danh sách thông tin người dùng',
                'admin.user.inquiries.show' => 'Xem chi tiết thông tin',
                'admin.user.inquiries.delete' => 'Xóa thông tin người dùng',
            ],
            'Cài đặt' => [
                'admin.system.settings' => 'Cài đặt hệ thống',
                'admin.website.settings' => 'Cài đặt website',
                'admin.payment.settings' => 'Cài đặt thanh toán',
                'admin.manage.language' => 'Cài đặt ngôn ngữ',
                'admin.certificate.settings' => 'Chứng chỉ',
                'admin.certificate.builder' => 'Tạo chứng chỉ',
                'admin.open.ai.settings' => 'Cài đặt Open AI',
                'admin.seo.settings' => 'Cài đặt SEO',
                'admin.drip.settings' => 'Cài đặt nội dung drip',
                'admin.notification.settings' => 'Cài đặt thông báo',
                'admin.player.settings' => 'Cài đặt trình phát',
                'admin.embed.settings' => 'Nhúng form đăng ký',
                'admin.api.configurations' => 'Cấu hình API',
                'admin.language.phrase.edit' => 'Chỉnh sửa cụm từ ngôn ngữ',
                'admin.language.phrase.import' => 'Nhập cụm từ ngôn ngữ',
                'admin.language.delete' => 'Xóa ngôn ngữ',
                'admin.api.configuration.update' => 'Cập nhật cấu hình API',
                'admin.open.ai.generate' => 'Tạo nội dung Open AI',
            ],
            'Khác' => [
                'admin.about' => 'Giới thiệu',
                'admin.bunny.upload' => 'Tải video Bunny',
                'admin.addons' => 'Addon',
                'admin.bunny.statistics' => 'Thống kê Bunny',
                'admin.bunny.list' => 'Danh sách video Bunny',
                'admin.save_valid_purchase_code' => 'Lưu mã mua hàng hợp lệ',
                'admin.select.language' => 'Chọn ngôn ngữ',
                'admin.addons.toggle' => 'Chuyển đổi trạng thái Addon',
                'admin.addons.seed' => 'Khởi tạo Addon',
            ],
        ];
        $permission_row = DB::table('permissions')
            ->where('admin_id', $admin->id)
            ->first();
        $permissions = json_decode($permission_row->permissions ?? '{}', true);
    @endphp

    <div class="row">
        <div class="col-xl-8">
            <div class="ol-card p-4">
                <div class="ol-card-body">
                    <div class="col-12 mb-4">
                        <p class="column-title mb-1">Phân quyền cho: {{ $admin->name }}</p>
                        <small class="text-muted">
                            <strong>Lưu ý</strong> :
                            Bạn có thể bật/tắt công tắc để cho phép hoặc vô hiệu hóa quyền truy cập tính năng</small>
                    </div>

                    <div class="accordion" id="permissionAccordion">
                        @foreach($permissionGroups as $groupName => $groupPermissions)
                            <div class="permission-group mb-3">
                                <div class="permission-group-header d-flex justify-content-between align-items-center collapsed"
                                     data-bs-toggle="collapse"
                                     data-bs-target="#group{{ str_replace(' ', '', $groupName) }}"
                                     aria-expanded="false"
                                     aria-controls="group{{ str_replace(' ', '', $groupName) }}">
                                    <h5>{{ $groupName }}</h5>
                                    <div>
                                        <span class="select-all-checkbox">
                                            <input type="checkbox"
                                                   class="group-select-all"
                                                   id="selectAll{{ str_replace(' ', '', $groupName) }}"
                                                   data-group="{{ str_replace(' ', '', $groupName) }}"
                                                   @if(is_array($permissions) && count(array_intersect(array_keys($groupPermissions), $permissions)) == count($groupPermissions)) checked @endif
                                                   >
                                            <label for="selectAll{{ str_replace(' ', '', $groupName) }}">Chọn tất cả</label>
                                        </span>
                                        <i class="toggle-icon fas fa-chevron-down"></i>
                                    </div>
                                </div>
                                <div id="group{{ str_replace(' ', '', $groupName) }}" class="collapse" data-parent="#permissionAccordion">
                                    <div class="permission-group-body">
                                        @foreach($groupPermissions as $route => $title)
                                            <div class="permission-item d-flex justify-content-between align-items-center">
                                                <span>{{ $title }}</span>
                                                <div class="form-check form-switch">
                                                    <input type="checkbox"
                                                           class="form-check-input permission-checkbox"
                                                           id="{{ $admin->id . '-' . $route }}"
                                                           data-group="{{ str_replace(' ', '', $groupName) }}"
                                                           data-switch="bool"
                                                           onchange="setPermission('{{ $admin->id }}', '{{ $route }}')"
                                                           @if (is_array($permissions) && in_array($route, $permissions)) checked @endif>
                                                    <label for="{{ $admin->id . '-' . $route }}" data-on-label="On" data-off-label="Off"></label>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div> <!-- end card body-->
            </div> <!-- end card -->
        </div><!-- end col-->
    </div>
@endsection
@push('js')
    <script>
        "use strict";

        function setPermission(user_id, permission) {
            $.ajax({
                type: "post",
                url: "{{ route('admin.admins.permission.store') }}/" + user_id,
                data: {
                    user_id: user_id,
                    permission: permission,
                },
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response == 1) {
                        success("Phân quyền đã được cập nhật");
                        updateGroupCheckboxes();
                    }
                }
            });
        }

        $(document).ready(function() {
            // Toggle sections when clicking on headers
            $('.permission-group-header').on('click', function() {
                $(this).toggleClass('collapsed');
                $(this).find('.toggle-icon').toggleClass('fa-chevron-down fa-chevron-up');
                $(this).next('.collapse').collapse('toggle');
            });

            // Select all permissions in a group
            $('.group-select-all').on('change', function() {
                const groupId = $(this).data('group');
                const isChecked = $(this).prop('checked');
                const checkboxes = $(`.permission-checkbox[data-group="${groupId}"]`);

                checkboxes.each(function() {
                    const currentValue = $(this).prop('checked');
                    if (currentValue !== isChecked) {
                        $(this).prop('checked', isChecked);
                        const id = $(this).attr('id').split('-');
                        const user_id = id[0];
                        const permission = id.slice(1).join('-');
                        setPermission(user_id, permission);
                    }
                });
            });

            // Update all group checkboxes
            updateGroupCheckboxes();
        });

        function updateGroupCheckboxes() {
            $('.group-select-all').each(function() {
                const groupId = $(this).data('group');
                const checkboxes = $(`.permission-checkbox[data-group="${groupId}"]`);
                const checkedCheckboxes = $(`.permission-checkbox[data-group="${groupId}"]:checked`);
                $(this).prop('checked', checkboxes.length === checkedCheckboxes.length);
            });
        }
    </script>
@endpush
