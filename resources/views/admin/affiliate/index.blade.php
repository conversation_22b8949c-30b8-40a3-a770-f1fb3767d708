@extends('layouts.admin')
@push('title', 'Thống kê doanh thu Affiliate')
@section('content')

    <div class="ol-card radius-8px">
        <div class="ol-card-body py-12px px-20px my-3">
            <div class="d-flex align-items-center justify-content-between flex-md-nowrap flex-wrap gap-3">
                <h4 class="title fs-16px">
                    <i class="fi-rr-chart-line-up me-2"></i>
                    Thống kê doanh thu Affiliate
                </h4>
            </div>
        </div>
    </div>

    <!-- Thống kê tổng quan -->
    <div class="row mb-4">
        @php
            $totalApproved = $affiliates->sum('approved_revenue');
            $totalPending = $affiliates->sum('pending_revenue');
            $totalRevenue = $affiliates->sum('total_revenue');
            $totalOrders = $affiliates->sum('total_orders');
        @endphp

        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
            <div class="ol-card card-hover">
                <div class="ol-card-body p-3">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="">
                            <p class="title fs-14px mb-1">Tổng doanh thu đã duyệt</p>
                            <h4 class="title fs-20px text-success">{{ currency($totalApproved) }}</h4>
                        </div>
                        <div class="ol-card-icon-2">
                            <i class="fi-rr-check-circle text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
            <div class="ol-card card-hover">
                <div class="ol-card-body p-3">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="">
                            <p class="title fs-14px mb-1">Tổng doanh thu chờ duyệt</p>
                            <h4 class="title fs-20px text-warning">{{ currency($totalPending) }}</h4>
                        </div>
                        <div class="ol-card-icon-2">
                            <i class="fi-rr-clock text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
            <div class="ol-card card-hover">
                <div class="ol-card-body p-3">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="">
                            <p class="title fs-14px mb-1">Tổng doanh thu</p>
                            <h4 class="title fs-20px text-primary">{{ currency($totalRevenue) }}</h4>
                        </div>
                        <div class="ol-card-icon-2">
                            <i class="fi-rr-chart-line-up text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
            <div class="ol-card card-hover">
                <div class="ol-card-body p-3">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="">
                            <p class="title fs-14px mb-1">Tổng số đơn hàng</p>
                            <h4 class="title fs-20px text-info">{{ number_format($totalOrders) }}</h4>
                        </div>
                        <div class="ol-card-icon-2">
                            <i class="fi-rr-shopping-cart text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- Start Admin area -->
    <div class="row">
        <div class="col-12">
            <div class="ol-card">
                <div class="ol-card-body mb-5 p-3">
                    <div class="row mb-4 mt-3">
                        <div class="col-md-6 d-flex align-items-center gap-3">
                            <div class="custom-dropdown ms-2">
                                <button class="dropdown-header btn ol-btn-light">
                                    Xuất dữ liệu
                                    <i class="fi-rr-file-export ms-2"></i>
                                </button>
                                <ul class="dropdown-list">
                                    <li>
                                        <a class="dropdown-item export-btn" href="#"
                                            onclick="downloadPDF('.print-table', 'affiliate-revenue-stats')"><i
                                                class="fi-rr-file-pdf"></i> PDF</a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item export-btn" href="#" onclick="window.print();"><i
                                                class="fi-rr-print"></i> In</a>
                                    </li>
                                </ul>
                            </div>

                        </div>

                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            @if ($affiliates->count() > 0)
                                <div
                                    class="admin-tInfo-pagi d-flex justify-content-md-between justify-content-center align-items-center gr-15 flex-wrap">
                                    <p class="admin-tInfo">
                                        Hiển thị {{ count($affiliates) }} trong tổng số {{ $affiliates->total() }} affiliate
                                    </p>
                                </div>
                                <div class="table-responsive course_list overflow-auto overflow-auto" id="course_list">
                                    <table class="eTable eTable-2 print-table table">
                                        <thead>
                                            <tr>
                                                <th scope="col">#</th>
                                                <th scope="col">Thông tin Affiliate</th>
                                                <th scope="col">Doanh thu đã duyệt</th>
                                                <th scope="col">Doanh thu chờ duyệt</th>
                                                <th scope="col">Tổng doanh thu</th>
                                                <th scope="col">Số đơn hàng</th>
                                                <th scope="col" class="print-d-none">Thao tác</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($affiliates as $key => $affiliate)
                                                <tr>
                                                    <th scope="row">
                                                        <p class="row-number">{{ ++$key }}</p>
                                                    </th>
                                                    <td>
                                                        <div class="dAdmin_profile d-flex align-items-center min-w-200px">
                                                            <div class="dAdmin_profile_name">
                                                                <h4 class="title fs-14px mb-1">
                                                                    {{ @$affiliate->affiliater->name ?? 'N/A' }}
                                                                </h4>
                                                                <p class="sub-title2 text-12px text-muted mb-0">
                                                                    {{ @$affiliate->affiliater->email ?? 'N/A' }}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="text-success fw-bold fs-14px">
                                                            {{ currency($affiliate->approved_revenue) }}
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="text-warning fw-bold fs-14px">
                                                            {{ currency($affiliate->pending_revenue) }}
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="text-primary fw-bold fs-14px">
                                                            {{ currency($affiliate->total_revenue) }}
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="text-center">
                                                            <span class="badge bg-info">{{ $affiliate->total_orders }} đơn</span>
                                                        </div>
                                                    </td>
                                                    <td class="print-d-none">
                                                        <div class="dropdown ol-icon-dropdown ol-icon-dropdown-transparent">
                                                            <button class="btn ol-btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                                <span class="fi-rr-menu-dots-vertical"></span>
                                                            </button>
                                                            <ul class="dropdown-menu">
                                                                <li>
                                                                    <a class="dropdown-item" href="{{ route('admin.offline.payments', ['affiliate_id' => $affiliate->affiliate_id]) }}">
                                                                        <i class="fi-rr-eye text-info me-1"></i>
                                                                        Xem chi tiết
                                                                    </a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                                <div
                                    class="admin-tInfo-pagi d-flex justify-content-md-between justify-content-center align-items-center gr-15 flex-wrap">
                                    <p class="admin-tInfo">
                                        Hiển thị {{ count($affiliates) }} trong tổng số {{ $affiliates->total() }} affiliate
                                    </p>
                                    {{ $affiliates->links() }}
                                </div>
                            @else
                                @include('admin.no_data')
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- End Admin area -->
@endsection

@push('js')
    <script type="text/javascript">
        "use strict";

        $(document).ready(function() {
            // Có thể thêm các function JavaScript khác nếu cần
            console.log('Affiliate revenue statistics page loaded');
        });
    </script>
@endpush
