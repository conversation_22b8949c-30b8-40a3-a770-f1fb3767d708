@extends('layouts.admin')

@push('title', '<PERSON><PERSON><PERSON> <PERSON>ê<PERSON> Bunny Stream')

@push('meta')
    <meta name="csrf-token" content="{{ csrf_token() }}">
@endpush

@section('content')

    <style>
        /* Modern color scheme */
        :root {
            --primary-color: #4f46e5;
            --primary-light: #818cf8;
            --primary-dark: #3730a3;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #06b6d4;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
        }

        /* Page header styling */
        .page-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 1rem 1rem;
        }

        .page-header h1 {
            font-size: 2rem;
            font-weight: 600;
            margin: 0;
        }

        .page-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
        }

        /* Statistics cards */
        .stats-card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-200);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
        }

        .stats-card-header {
            background: var(--gray-50);
            padding: 1.5rem;
            border-bottom: 1px solid var(--gray-200);
        }

        .stats-card-header h5 {
            margin: 0;
            font-weight: 600;
            color: var(--gray-800);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .stats-card-body {
            padding: 1.5rem;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid var(--gray-100);
        }

        .stat-item:last-child {
            border-bottom: none;
        }

        .stat-label {
            color: var(--gray-600);
            font-weight: 500;
        }

        .stat-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        /* Upload zone styling */
        .upload-section {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-200);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .upload-drop-zone {
            border: 3px dashed var(--gray-300);
            border-radius: 1rem;
            padding: 3rem 2rem;
            text-align: center;
            margin: 1.5rem;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
            position: relative;
            overflow: hidden;
        }

        .upload-drop-zone::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(79, 70, 229, 0.05) 50%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .upload-drop-zone:hover::before {
            opacity: 1;
        }

        .upload-drop-zone.highlight {
            border-color: var(--primary-color);
            background: linear-gradient(135deg, rgba(79, 70, 229, 0.1) 0%, rgba(129, 140, 248, 0.05) 100%);
            transform: scale(1.02);
        }

        .upload-drop-zone i {
            font-size: 4rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
            display: block;
        }

        .upload-drop-zone h4 {
            margin-bottom: 0.5rem;
            color: var(--gray-800);
            font-weight: 600;
            font-size: 1.5rem;
        }

        .upload-drop-zone p {
            color: var(--gray-600);
            margin-bottom: 0.5rem;
        }

        .upload-drop-zone .text-muted {
            color: var(--gray-500);
            font-size: 0.875rem;
        }

        /* File Queue Styling */
        .file-queue {
            margin-top: 1.5rem;
            background: white;
            border-radius: 1rem;
            border: 1px solid var(--gray-200);
            overflow: hidden;
        }

        .file-queue-header {
            background: var(--gray-50);
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--gray-200);
        }

        .file-queue-header h5 {
            margin: 0;
            font-weight: 600;
            color: var(--gray-800);
        }

        .file-queue-body {
            max-height: 400px;
            overflow-y: auto;
        }

        .file-item {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--gray-100);
            position: relative;
            transition: background-color 0.2s ease;
        }

        .file-item:hover {
            background-color: var(--gray-50);
        }

        .file-item:last-child {
            border-bottom: none;
        }

        .file-item .file-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            flex-shrink: 0;
        }

        .file-item .file-icon i {
            color: white;
            font-size: 1.25rem;
        }

        .file-item .file-info {
            flex-grow: 1;
            min-width: 0;
        }

        .file-item .file-name {
            font-weight: 600;
            color: var(--gray-800);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: 0.25rem;
        }

        .file-item .file-size {
            color: var(--gray-500);
            font-size: 0.875rem;
        }

        .file-item .file-status {
            margin: 0 1rem;
            flex-shrink: 0;
        }

        .file-item .file-remove {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--gray-100);
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            flex-shrink: 0;
        }

        .file-item .file-remove:hover {
            background: var(--danger-color);
            color: white;
        }

        .file-progress {
            height: 4px;
            width: 100%;
            background-color: var(--gray-200);
            border-radius: 2px;
            overflow: hidden;
            position: absolute;
            bottom: 0;
            left: 0;
        }

        .file-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
            width: 0%;
            transition: width 0.3s ease;
        }

        /* Queue summary styling */
        .queue-summary {
            background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
            border-radius: 1rem;
            padding: 1.5rem;
            margin: 1.5rem;
            border: 1px solid var(--gray-200);
        }

        .queue-progress-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .queue-progress-text {
            font-weight: 600;
            color: var(--gray-700);
        }

        .queue-stats-text {
            color: var(--gray-600);
            font-size: 0.875rem;
        }

        .progress {
            height: 8px;
            border-radius: 4px;
            background-color: var(--gray-200);
            overflow: hidden;
            margin-bottom: 1rem;
        }

        .progress-bar {
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .queue-status {
            text-align: center;
            color: var(--gray-600);
            font-size: 0.875rem;
            font-style: italic;
        }

        /* Button styling */
        .btn-modern {
            border-radius: 0.75rem;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            border: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-modern:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn-primary-modern {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
        }

        .btn-secondary-modern {
            background: var(--gray-100);
            color: var(--gray-700);
        }

        .btn-secondary-modern:hover {
            background: var(--gray-200);
            color: var(--gray-800);
        }

        .btn-danger-modern {
            background: linear-gradient(135deg, var(--danger-color), #f87171);
            color: white;
        }

        /* DataTable styling */
        .video-table-container {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-200);
            overflow: hidden;
            margin-top: 2rem;
        }

        .table-header {
            background: var(--gray-50);
            padding: 1.5rem;
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-header h5 {
            margin: 0;
            font-weight: 600;
            color: var(--gray-800);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        #videoTable {
            width: 100% !important;
            border-collapse: separate;
            border-spacing: 0;
            margin: 0;
        }

        #videoTable thead th {
            background-color: var(--gray-50);
            border-bottom: 1px solid var(--gray-200);
            padding: 1.25rem 1.5rem;
            font-weight: 600;
            color: var(--gray-700);
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        #videoTable tbody td {
            padding: 1.25rem 1.5rem;
            vertical-align: middle;
            border-bottom: 1px solid var(--gray-100);
        }

        /* Specific column spacing */
        #videoTable tbody td:nth-child(4) {
            padding: 1.25rem 2rem; /* More padding for status column */
        }

        #videoTable tbody td:nth-child(5) {
            padding: 1.25rem 1.5rem;
            white-space: nowrap;
        }

        #videoTable tbody tr:hover {
            background-color: var(--gray-50);
        }

        #videoTable tbody tr:last-child td {
            border-bottom: none;
        }

        /* Fix DataTable search box and pagination */
        .dataTables_wrapper .dataTables_filter {
            margin-bottom: 15px;
            text-align: right;
        }

        .dataTables_wrapper .dataTables_filter input {
            border: 1px solid #ced4da;
            border-radius: 6px;
            padding: 6px 12px;
            margin-left: 8px;
        }

        .dataTables_wrapper .dataTables_length {
            margin-bottom: 15px;
        }

        .dataTables_wrapper .dataTables_length select {
            border: 1px solid #ced4da;
            border-radius: 6px;
            padding: 6px 12px;
        }

        .dataTables_wrapper .dataTables_paginate {
            margin-top: 15px;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button {
            padding: 5px 10px;
            margin: 0 2px;
            border-radius: 4px;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.current {
            background: #007bff;
            color: white !important;
            border: 1px solid #007bff;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
            background: #e9ecef;
            color: #212529 !important;
            border: 1px solid #dee2e6;
        }

        /* Action buttons styling */
        .action-btn {
            margin-right: 0.75rem;
            margin-bottom: 0.25rem;
            padding: 0.625rem 1.25rem;
            font-size: 0.875rem;
            border-radius: 0.5rem;
            font-weight: 500;
            border: none;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            min-width: auto;
        }

        .action-btn:last-child {
            margin-right: 0;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .action-btn.btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        }

        .action-btn.btn-info {
            background: linear-gradient(135deg, var(--info-color), #67e8f9);
        }

        .action-btn.btn-danger {
            background: linear-gradient(135deg, var(--danger-color), #f87171);
        }

        /* Status badges */
        .badge {
            font-weight: 600;
            padding: 0.5rem 1rem;
            border-radius: 0.75rem;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .badge.bg-secondary {
            background: linear-gradient(135deg, var(--gray-400), var(--gray-500)) !important;
            color: white !important;
        }

        .badge.bg-warning {
            background: linear-gradient(135deg, var(--warning-color), #fbbf24) !important;
            color: white !important;
        }

        .badge.bg-success {
            background: linear-gradient(135deg, var(--success-color), #34d399) !important;
            color: white !important;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
            border-color: rgba(16, 185, 129, 0.2);
            animation: pulse-success 2s infinite;
        }

        .badge.bg-danger {
            background: linear-gradient(135deg, var(--danger-color), #f87171) !important;
            color: white !important;
        }

        .badge.bg-info {
            background: linear-gradient(135deg, var(--info-color), #67e8f9) !important;
            color: white !important;
        }

        @keyframes pulse-success {
            0%, 100% {
                box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
                transform: scale(1.05);
            }
        }

        /* Video modal styling */
        .video-modal {
            max-width: 800px;
        }

        .video-container {
            position: relative;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
            height: 0;
            overflow: hidden;
        }

        .video-iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: 0;
        }

        /* Alert styling */
        #alertContainer .alert {
            margin-bottom: 20px;
            border-radius: 6px;
        }

        /* Copy tooltip styling */
        .copy-tooltip {
            position: relative;
            display: inline-block;
        }

        .copy-tooltip .tooltiptext {
            visibility: hidden;
            width: 120px;
            background-color: #333;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 5px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -60px;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .copy-tooltip .tooltiptext::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #333 transparent transparent transparent;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .page-header {
                padding: 1.5rem 0;
                text-align: center;
            }

            .page-header h1 {
                font-size: 1.5rem;
            }

            .stats-card-body .row {
                flex-direction: column;
            }

            .stat-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .upload-drop-zone {
                padding: 2rem 1rem;
                margin: 1rem;
            }

            .upload-drop-zone h4 {
                font-size: 1.25rem;
            }

            .file-item {
                flex-wrap: wrap;
                padding: 1rem;
            }

            .file-item .file-info {
                order: 1;
                width: 100%;
                margin-bottom: 0.5rem;
            }

            .file-item .file-status {
                order: 2;
                margin: 0;
            }

            .file-item .file-remove {
                order: 3;
                margin-left: auto;
            }

            .action-btn {
                margin-bottom: 0.5rem;
                margin-right: 0.5rem;
                font-size: 0.75rem;
                padding: 0.5rem 1rem;
                width: auto;
                display: inline-flex;
            }

            .action-btn:last-child {
                margin-right: 0;
            }

            .dataTables_wrapper .dataTables_filter {
                text-align: left;
                margin-top: 10px;
            }

            .dataTables_wrapper .dataTables_filter input {
                width: 100%;
                margin-left: 0;
            }

            .table-header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            #videoTable {
                font-size: 0.875rem;
            }

            #videoTable thead th,
            #videoTable tbody td {
                padding: 0.75rem 0.5rem;
            }
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            :root {
                --gray-50: #1f2937;
                --gray-100: #374151;
                --gray-200: #4b5563;
                --gray-300: #6b7280;
            }
        }

        /* Animation classes */
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
    </style>
    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <h1><i class="fas fa-video me-2"></i>Quản lý Video Bunny Stream</h1>
                    <p>Tải lên và quản lý video với dịch vụ Bunny Stream chuyên nghiệp</p>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        @if(isset($configError))
        <!-- Configuration Error Alert -->
        <div class="row justify-content-center mb-4">
            <div class="col-md-10">
                <div class="alert alert-warning border-0 shadow-sm" role="alert">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h5 class="alert-heading mb-2">Cấu hình dịch vụ video chưa sẵn sàng</h5>
                            <p class="mb-2">{{ $configError }}</p>
                            <hr class="my-3">
                            <p class="mb-0 small text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Để sử dụng tính năng upload video, vui lòng đảm bảo các thông tin cấu hình Bunny Stream đã được thiết lập đúng trong hệ thống.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @else
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="stats-card">
                    <div class="stats-card-header">
                        <h5><i class="fas fa-chart-bar me-2"></i>Thống kê lưu trữ & băng thông</h5>
                        <button id="refreshStats" class="btn btn-modern btn-secondary-modern btn-sm">
                            <i class="fas fa-sync-alt"></i> Làm mới
                        </button>
                    </div>
                    <div class="stats-card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="stat-item">
                                    <span class="stat-label">
                                        <i class="fas fa-hdd me-2 text-primary"></i>Dung lượng sử dụng
                                    </span>
                                    <span id="storageUsage" class="stat-value">0 MB</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">
                                        <i class="fas fa-exchange-alt me-2 text-info"></i>Băng thông sử dụng
                                    </span>
                                    <span id="trafficUsage" class="stat-value">0 MB</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="stat-item">
                                    <span class="stat-label">
                                        <i class="fas fa-play-circle me-2 text-success"></i>Tổng video
                                    </span>
                                    <span id="videoCount" class="stat-value">0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">
                                        <i class="fas fa-dollar-sign me-2 text-warning"></i>Chi phí ước tính/tháng
                                    </span>
                                    <span id="totalCost" class="stat-value">$0.00</span>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="stat-item">
                                    <span class="stat-label">
                                        <i class="fas fa-folder me-2 text-secondary"></i>Tên thư viện
                                    </span>
                                    <span id="libraryName" class="stat-value">-</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="stat-item">
                                    <span class="stat-label">
                                        <i class="fas fa-calendar me-2 text-secondary"></i>Ngày tạo
                                    </span>
                                    <span id="libraryCreated" class="stat-value">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Section -->
        <div class="row">
            <div class="col-12">
                <div class="upload-section">
                    <div id="alertContainer"></div>

                    <!-- Drag & Drop Upload Zone -->
                    <div id="dropZone" class="upload-drop-zone">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <h4>Kéo thả file video vào đây</h4>
                        <p>hoặc nhấp để chọn file từ máy tính</p>
                        <p class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Hỗ trợ: MP4, MOV, AVI, FLV • Kích thước tối đa: {{ $maxUploadSize }}
                        </p>
                        <input type="file" id="fileInput" class="d-none" multiple
                               accept="video/mp4,video/mov,video/avi,video/flv">
                    </div>

                    <!-- File Queue -->
                    <div id="fileQueue" class="file-queue d-none">
                        <div class="file-queue-header">
                            <h5><i class="fas fa-list me-2"></i>Hàng đợi tải lên</h5>
                        </div>
                        <div class="file-queue-body">
                            <div id="queueItems"></div>
                        </div>

                        <!-- Upload Status Summary -->
                        <div id="queueSummary" class="queue-summary d-none">
                            <div class="queue-progress-info">
                                <span id="queueProgress" class="queue-progress-text">
                                    Tiến độ: <span id="queueProgressValue">0%</span>
                                </span>
                                <span id="queueStats" class="queue-stats-text">0/0 file hoàn thành</span>
                            </div>
                            <div class="progress">
                                <div id="queueProgressBar" class="progress-bar" role="progressbar"
                                     style="width: 0%"></div>
                            </div>
                            <p id="queueStatus" class="queue-status">Sẵn sàng tải lên</p>
                        </div>
                    </div>

                    <!-- Upload Controls -->
                    <div class="d-flex justify-content-between align-items-center" style="padding: 1.5rem;">
                        <button id="clearQueueBtn" class="btn btn-modern btn-secondary-modern d-none">
                            <i class="fas fa-times"></i> Xóa hàng đợi
                        </button>
                        <button id="uploadAllBtn" class="btn btn-modern btn-primary-modern d-none">
                            <i class="fas fa-upload"></i> Tải lên tất cả video
                        </button>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <!-- Video List Table -->
        @if(!isset($configError))
        <div class="row">
            <div class="col-12">
                <div class="video-table-container">
                    <div class="table-header">
                        <h5><i class="fas fa-video me-2"></i>Danh sách video</h5>
                        <button id="refreshVideoList" class="btn btn-modern btn-secondary-modern btn-sm">
                            <i class="fas fa-sync-alt"></i> Làm mới
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table id="videoTable" class="table">
                            <thead>
                            <tr>
                                <th><i class="fas fa-file-video me-2"></i>Tiêu đề</th>
                                <th><i class="fas fa-calendar me-2"></i>Ngày tạo</th>
                                <th><i class="fas fa-eye me-2"></i>Lượt xem</th>
                                <th><i class="fas fa-info-circle me-2"></i>Trạng thái</th>
                                <th><i class="fas fa-cogs me-2"></i>Thao tác</th>
                            </tr>
                            </thead>
                            <tbody>
                            <!-- Data will be loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        @endif
    </div>

    <!-- Video Player Modal -->
    <div class="modal fade" id="videoPlayerModal" tabindex="-1" aria-labelledby="videoPlayerModalLabel"
         aria-hidden="true">
        <div class="modal-dialog video-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="videoPlayerModalLabel">Xem video</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="video-container">
                        <iframe id="videoPlayerIframe" class="video-iframe" allowfullscreen></iframe>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel"
         aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteConfirmModalLabel">Xác nhận xóa</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    Bạn có chắc chắn muốn xóa video "<span id="deleteVideoTitle"></span>"?
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary"
                            data-bs-dismiss="modal">Hủy</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Xóa</button>
                </div>
            </div>
        </div>
    </div>
    @if(!isset($configError))
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

    <script>
        // File queue management
        let fileQueue = [];
        const maxUploadSizeBytes = {{ $maxUploadSizeBytes }};
        const maxUploadSizeFormatted = '{{ $maxUploadSize }}';
        let currentlyUploading = false;
        const csrfToken = '{{ csrf_token() }}';

        // Add variables to track queue status
        let queueStatsData = {
            total: 0,
            completed: 0,
            processing: 0,
            failed: 0,
            totalProgress: 0
        };

        // Add variables to track processing videos
        let processingFiles = [];

        // Add variables to track table refresh
        let refreshInterval = null;
        let allVideosReady = false;

        // Initialize drag and drop functionality
        const dropZone = document.getElementById('dropZone');
        const fileInput = document.getElementById('fileInput');
        const queueContainer = document.getElementById('fileQueue');
        const queueItems = document.getElementById('queueItems');
        const clearQueueBtn = document.getElementById('clearQueueBtn');
        const uploadAllBtn = document.getElementById('uploadAllBtn');
        const queueSummary = document.getElementById('queueSummary');
        const queueProgressBar = document.getElementById('queueProgressBar');
        const queueProgressValue = document.getElementById('queueProgressValue');
        const queueStatsElement = document.getElementById('queueStats');
        const queueStatus = document.getElementById('queueStatus');

        // Add event listeners for drag and drop
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, unhighlight, false);
        });

        function highlight() {
            dropZone.classList.add('highlight');
        }

        function unhighlight() {
            dropZone.classList.remove('highlight');
        }

        // Handle file drop
        dropZone.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            handleFiles(files);
        }

        // Handle file selection via click
        dropZone.addEventListener('click', () => {
            fileInput.click();
        });

        fileInput.addEventListener('change', () => {
            handleFiles(fileInput.files);
        });

        function handleFiles(files) {
            if (files.length === 0) return;

            // Filter for video files
            const videoFiles = Array.from(files).filter(file =>
                file.type.startsWith('video/') ||
                /\.(mp4|mov|avi|flv)$/i.test(file.name)
            );

            if (videoFiles.length === 0) {
                showAlert('Vui lòng chọn file video hợp lệ (MP4, MOV, AVI, FLV)', 'warning');
                return;
            }

            // Check file sizes
            const oversizedFiles = videoFiles.filter(file => file.size > maxUploadSizeBytes);
            if (oversizedFiles.length > 0) {
                const fileNames = oversizedFiles.map(f => f.name).join(', ');
                showAlert(`Các file sau vượt quá kích thước tối đa ${maxUploadSizeFormatted}: ${fileNames}`, 'warning');

                // Only add files that are within size limit
                const validFiles = videoFiles.filter(file => file.size <= maxUploadSizeBytes);
                addFilesToQueue(validFiles);
            } else {
                addFilesToQueue(videoFiles);
            }

            // Make sure Upload All Videos button appears when new files are added
            const hasPendingFiles = fileQueue.some(f => f.status === 'pending');
            if (hasPendingFiles) {
                uploadAllBtn.classList.remove('d-none');
                uploadAllBtn.disabled = currentlyUploading;
            }
        }

        function addFilesToQueue(files) {
            // Add files to queue
            files.forEach((file, index) => {
                // Check if file is already in queue
                const isDuplicate = fileQueue.some(queuedFile =>
                    queuedFile.name === file.name &&
                    queuedFile.size === file.size &&
                    queuedFile.status !== 'error'
                );

                if (!isDuplicate) {
                    const fileObj = {
                        file: file,
                        id: 'file_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                        name: file.name,
                        size: file.size,
                        status: 'pending', // pending, uploading, processing, complete, error
                        progress: 0,
                        guid: null
                    };

                    fileQueue.push(fileObj);

                    // Add with animation delay
                    setTimeout(() => {
                        renderFileItem(fileObj);
                    }, index * 100);
                }
            });

            updateQueueUI();
            updateQueueSummary();
        }

        function renderFileItem(fileObj) {
            const fileSize = formatFileSize(fileObj.size);
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.id = fileObj.id;
            fileItem.innerHTML = `
                <div class="file-icon">
                    <i class="fas fa-file-video"></i>
                </div>
                <div class="file-info">
                    <div class="file-name">${fileObj.name}</div>
                    <div class="file-size">${fileSize}</div>
                </div>
                <div class="file-status">
                    <span class="badge bg-secondary">Chờ xử lý</span>
                </div>
                <button class="file-remove" onclick="removeFile('${fileObj.id}')">
                    <i class="fas fa-times"></i>
                </button>
                <div class="file-progress">
                    <div class="file-progress-bar" style="width: 0%"></div>
                </div>
            `;

            // Add with fade-in animation
            fileItem.classList.add('fade-in');
            queueItems.appendChild(fileItem);
        }

        function updateFileStatus(fileId, status, progress = null, message = null) {
            const fileObj = fileQueue.find(f => f.id === fileId);
            if (!fileObj) return;

            const oldStatus = fileObj.status;
            fileObj.status = status;

            if (progress !== null) {
                fileObj.progress = progress;
            }

            const fileItem = document.getElementById(fileId);
            if (!fileItem) return;

            const statusElement = fileItem.querySelector('.file-status');
            const progressBar = fileItem.querySelector('.file-progress-bar');

            // Update progress bar if provided
            if (progress !== null) {
                progressBar.style.width = `${progress}%`;
            }

            // Update status badge
            let badgeClass = 'bg-secondary';
            let statusText = 'Chờ xử lý';

            switch (status) {
                case 'uploading':
                    badgeClass = 'bg-primary';
                    statusText = 'Đang tải lên';
                    break;
                case 'processing':
                    badgeClass = 'bg-warning text-dark';
                    statusText = message || 'Đang xử lý';
                    break;
                case 'complete':
                    badgeClass = 'bg-success';
                    statusText = 'Hoàn thành';
                    break;
                case 'error':
                    badgeClass = 'bg-danger';
                    statusText = 'Lỗi';
                    break;
            }

            statusElement.innerHTML = `<span class="badge ${badgeClass}">${statusText}</span>`;

            // Update queue statistics when status changes
            if (oldStatus !== status) {
                updateQueueSummary();
            }
        }

        function removeFile(fileId) {
            // Find file index in queue
            const fileIndex = fileQueue.findIndex(f => f.id === fileId);
            if (fileIndex === -1) return;

            // Check if file is currently uploading
            if (fileQueue[fileIndex].status === 'uploading') {
                showAlert('Không thể xóa file đang được tải lên', 'warning');
                return;
            }

            // Remove from queue array
            fileQueue.splice(fileIndex, 1);

            // Remove from DOM
            const fileItem = document.getElementById(fileId);
            if (fileItem) {
                fileItem.remove();
            }

            updateQueueUI();
            updateQueueSummary();
        }

        function updateQueueUI() {
            if (fileQueue.length > 0) {
                queueContainer.classList.remove('d-none');
                queueContainer.classList.add('fade-in');
                queueSummary.classList.remove('d-none');
                clearQueueBtn.classList.remove('d-none');

                // Only show Upload All Videos button when there are pending files
                const hasPendingFiles = fileQueue.some(f => f.status === 'pending');

                if (hasPendingFiles) {
                    uploadAllBtn.classList.remove('d-none');
                    uploadAllBtn.disabled = currentlyUploading;

                    // Add pulse effect when ready to upload
                    if (!currentlyUploading) {
                        uploadAllBtn.classList.add('pulse');
                    } else {
                        uploadAllBtn.classList.remove('pulse');
                    }
                } else {
                    uploadAllBtn.classList.add('d-none');
                    uploadAllBtn.classList.remove('pulse');
                }
            } else {
                queueContainer.classList.add('d-none');
                queueSummary.classList.add('d-none');
                clearQueueBtn.classList.add('d-none');
                uploadAllBtn.classList.add('d-none');
                uploadAllBtn.classList.remove('pulse');
            }
        }

        function updateQueueSummary() {
            // Calculate statistics
            const total = fileQueue.length;
            const completed = fileQueue.filter(f => f.status === 'complete').length;
            const processing = fileQueue.filter(f => ['uploading', 'processing'].includes(f.status)).length;
            const failed = fileQueue.filter(f => f.status === 'error').length;
            const pending = fileQueue.filter(f => f.status === 'pending').length;

            // Calculate total progress
            let totalProgress = 0;
            if (total > 0) {
                const progressSum = fileQueue.reduce((sum, file) => {
                    // Ensure file.progress is a number
                    const progress = typeof file.progress === 'number' ? file.progress : 0;
                    return sum + progress;
                }, 0);
                totalProgress = Math.round(progressSum / total);
            }

            // Update UI
            queueProgressBar.style.width = `${totalProgress}%`;
            queueProgressValue.textContent = `${totalProgress}%`;
            queueStatsElement.textContent = `${completed}/${total} file hoàn thành`;

            // Update status
            if (currentlyUploading) {
                if (processing > 0) {
                    const uploadingCount = fileQueue.filter(f => f.status === 'uploading').length;
                    const processingCount = fileQueue.filter(f => f.status === 'processing').length;

                    if (uploadingCount > 0) {
                        queueStatus.textContent = `Đang tải lên ${uploadingCount} file...`;
                    } else if (processingCount > 0) {
                        queueStatus.textContent = `Đang xử lý ${processingCount} file...`;
                    } else {
                        queueStatus.textContent = `Đang kiểm tra trạng thái video...`;
                    }
                } else {
                    queueStatus.textContent = `Đang kiểm tra trạng thái video...`;
                }
            } else if (completed === total && total > 0) {
                queueStatus.textContent = `Tất cả file đã tải lên thành công!`;
            } else if (failed > 0) {
                queueStatus.textContent = `${failed} file tải lên thất bại`;
            } else if (pending > 0) {
                queueStatus.textContent = `Sẵn sàng tải lên ${pending} file`;
            } else {
                queueStatus.textContent = `Sẵn sàng tải lên`;
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';

            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));

            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Clear queue button
        clearQueueBtn.addEventListener('click', () => {
            // Check if any file is uploading
            if (fileQueue.some(f => f.status === 'uploading')) {
                showAlert('Không thể xóa hàng đợi khi đang có file được tải lên', 'warning');
                return;
            }

            fileQueue = [];
            queueItems.innerHTML = '';
            updateQueueUI();
            updateQueueSummary();

            // Reset refresh state
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
            }
            allVideosReady = false;
        });

        // Upload all button
        uploadAllBtn.addEventListener('click', function () {
            if (currentlyUploading) {
                return;
            }

            // If no files are pending, reset the state of all files
            const hasPendingFiles = fileQueue.some(f => f.status === 'pending');
            if (!hasPendingFiles) {
                resetUploadState();
            }

            processQueue();
        });

        function processQueue() {
            if (currentlyUploading) return;

            const pendingFiles = fileQueue.filter(f => f.status === 'pending');
            if (pendingFiles.length === 0) {
                return;
            }

            currentlyUploading = true;
            uploadAllBtn.disabled = true;

            // Display upload start notification
            showAlert(`Bắt đầu tải lên ${pendingFiles.length} file...`, 'info');

            // Update queue status
            updateQueueSummary();

            // Start uploading the first file
            uploadFile(pendingFiles[0]);
        }

        function uploadFile(fileObj) {
            updateFileStatus(fileObj.id, 'uploading', 0);

            // Step 1: Create video on Bunny.net to get GUID
            const title = fileObj.name.replace(/\.[^/.]+$/, ""); // Remove extension

            fetch('{{ route('admin.bunny.upload.post') }}', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': csrfToken,
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    title: title,
                    step: 'create'
                })
            })
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        throw new Error(data.error);
                    }

                    fileObj.guid = data.guid;

                    // Step 2: Upload video with received GUID
                    uploadChunkedFile(fileObj);
                })
                .catch(error => {
                    updateFileStatus(fileObj.id, 'error');
                    showAlert('Có lỗi xảy ra khi tạo video. Vui lòng kiểm tra cấu hình và thử lại.', 'danger');

                    // Continue with next file
                    continueQueue();
                });
        }

        function uploadChunkedFile(fileObj) {
            const CHUNK_SIZE = 5 * 1024 * 1024; // 5MB chunks
            const file = fileObj.file;
            const totalChunks = Math.ceil(file.size / CHUNK_SIZE);
            let currentChunk = 0;

            function uploadNextChunk() {
                const start = currentChunk * CHUNK_SIZE;
                const end = Math.min(file.size, start + CHUNK_SIZE);
                const chunk = file.slice(start, end);

                const formData = new FormData();
                formData.append('video', chunk, file.name);
                formData.append('guid', fileObj.guid);
                formData.append('_token', csrfToken);
                formData.append('step', 'upload');
                formData.append('chunk', currentChunk);
                formData.append('totalChunks', totalChunks);

                fetch('{{ route('admin.bunny.upload.chunk') }}', {
                    method: 'POST',
                    body: formData
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.error) {
                            throw new Error(data.error);
                        }

                        // Update progress
                        currentChunk++;
                        const percentComplete = Math.round((currentChunk / totalChunks) * 100);
                        updateFileStatus(fileObj.id, 'uploading', percentComplete);
                        updateQueueSummary();

                        if (currentChunk < totalChunks) {
                            // Upload next chunk
                            uploadNextChunk();
                        } else {
                            // Completed uploading all chunks
                            updateFileStatus(fileObj.id, 'processing', 100, 'Đang hoàn tất');

                            // Call API to finalize upload
                            finalizeUpload(fileObj);
                        }
                    })
                    .catch(error => {
                        updateFileStatus(fileObj.id, 'error');
                        showAlert(`Lỗi khi tải lên phần ${currentChunk + 1} của file ${fileObj.name}. Vui lòng thử lại.`, 'danger');

                        // Continue with next file
                        continueQueue();
                    });
            }

            // Start uploading first chunk
            uploadNextChunk();
        }

        function finalizeUpload(fileObj) {
            fetch('{{ route('admin.bunny.upload.finalize') }}', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': csrfToken,
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({guid: fileObj.guid})
            })
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        throw new Error(data.error);
                    }

                    // Update status
                    updateFileStatus(fileObj.id, 'processing', 100, 'Đang mã hóa');

                    // Start checking encoding status
                    checkVideoStatus(fileObj);
                })
                .catch(error => {
                    updateFileStatus(fileObj.id, 'error');
                    showAlert(`Lỗi khi hoàn tất tải lên file ${fileObj.name}. Vui lòng thử lại.`, 'danger');

                    // Continue with next file
                    continueQueue();
                });
        }

        function checkVideoStatus(fileObj) {
            fetch('{{ route('admin.bunny.check-status') }}', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': csrfToken,
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({guid: fileObj.guid})
            })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'ready') {
                        // Update status to "Complete" when video is ready
                        updateFileStatus(fileObj.id, 'complete', 100);

                        // Remove file from processing list
                        const processingIndex = processingFiles.indexOf(fileObj.guid);
                        if (processingIndex !== -1) {
                            processingFiles.splice(processingIndex, 1);
                        }

                        showAlert(`Video "${fileObj.name}" đã được xử lý thành công!`, 'success');

                        // Refresh video list
                        if (videoTable) {
                            videoTable.ajax.reload(null, false);
                        }

                        // Continue with next file
                        continueQueue();

                    } else if (data.status === 'processing') {
                        // Update encoding progress information
                        const progressMessage = data.progress ? `Đang mã hóa (${data.progress}%)` : 'Đang mã hóa';
                        updateFileStatus(fileObj.id, 'processing', 100, progressMessage);

                        // Check again after 5 seconds
                        setTimeout(() => {
                            checkVideoStatus(fileObj);
                        }, 5000);
                    } else {
                        // If not ready or processing, consider it completed to continue the queue
                        console.log('Video status unknown, continuing queue:', data);
                        updateFileStatus(fileObj.id, 'complete', 100);
                        continueQueue();
                    }
                })
                .catch(error => {
                    console.error('Lỗi kiểm tra trạng thái:', error);

                    // Mark as error and continue with next file
                    updateFileStatus(fileObj.id, 'error');
                    continueQueue();
                });
        }

        function continueQueue() {
            // Update queue status
            updateQueueSummary();

            // Find next file to upload
            const nextFile = fileQueue.find(f => f.status === 'pending');

            if (nextFile) {
                // Upload next file
                uploadFile(nextFile);
            } else {
                // All files completed
                currentlyUploading = false;

                // Update UI - don't show Upload All Videos button again
                // uploadAllBtn will remain disabled until new files are added
                updateQueueUI();
                updateQueueSummary();

                // Check if all files are completed
                const allCompleted = fileQueue.every(f => f.status === 'complete');
                const completedCount = fileQueue.filter(f => f.status === 'complete').length;
                const failedCount = fileQueue.filter(f => f.status === 'error').length;

                // Reload video table immediately
                if (videoTable) {
                    videoTable.ajax.reload();
                }

                if (allCompleted) {
                    showAlert('Tất cả video đã được tải lên và xử lý thành công!', 'success');
                    queueStatus.textContent = `Tất cả file đã tải lên thành công!`;

                    // Start checking video status every 60 seconds
                    startTableRefresh();
                } else if (failedCount > 0) {
                    showAlert(`Tải lên hoàn tất: ${completedCount} thành công, ${failedCount} thất bại`, 'info');
                    queueStatus.textContent = `Tải lên hoàn tất với một số lỗi`;

                    // Start checking video status every 60 seconds
                    startTableRefresh();
                } else {
                    showAlert(`Tất cả ${completedCount} video đã tải lên thành công!`, 'success');
                    queueStatus.textContent = `Tất cả file đã tải lên thành công!`;

                    // Start checking video status every 60 seconds
                    startTableRefresh();
                }
            }
        }

        // Thêm hàm showAlert nếu chưa có
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            alertContainer.innerHTML = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            `;
        }

        // Khởi tạo DataTable
        let videoTable;
        $(document).ready(function () {
            // Tắt thông báo lỗi mặc định của DataTables
            $.fn.dataTable.ext.errMode = 'none';

            videoTable = $('#videoTable').DataTable({
                processing: true,
                serverSide: false,
                pageLength: 20,
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/vi.json',
                    emptyTable: "Chưa có video nào",
                    loadingRecords: "Đang tải dữ liệu...",
                    processing: "Đang xử lý...",
                    zeroRecords: "Không tìm thấy video nào"
                },
                ajax: {
                    url: '{{ route('admin.bunny.list') }}',
                    dataSrc: function(json) {
                        // Kiểm tra nếu có lỗi trong response
                        if (json.error) {
                            showAlert('Không thể tải danh sách video. Vui lòng kiểm tra cấu hình dịch vụ.', 'warning');
                            return [];
                        }
                        return json.items || [];
                    },
                    error: function(xhr, error, code) {
                        // Xử lý lỗi AJAX một cách chuyên nghiệp
                        console.log('DataTable AJAX Error:', error);
                        showAlert('Không thể kết nối đến dịch vụ video. Vui lòng kiểm tra cấu hình hoặc thử lại sau.', 'warning');
                    }
                },
                columns: [
                    {
                        data: 'title',
                        render: function (data, type, row) {
                            if (data) {
                                // If title has more than a certain number of characters, truncate and add "..."
                                const maxCharacters = 50; // You can adjust this number as needed

                                if (data.length > maxCharacters) {
                                    return data.substring(0, maxCharacters) + '...';
                                } else {
                                    return data;
                                }
                            } else {
                                return '<em>Không có tiêu đề</em>';
                            }
                        }
                    },
                    {
                        data: 'dateUploaded',
                        render: function (data) {
                            // Chuyển đổi chuỗi ngày tháng thành đối tượng Date
                            const date = new Date(data);

                            // Lấy ngày, tháng, năm
                            const day = date.getDate().toString().padStart(2, '0');
                            const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Tháng bắt đầu từ 0
                            const year = date.getFullYear();

                            // Trả về chuỗi định dạng dd/mm/yyyy
                            return `${day}/${month}/${year}`;
                        }
                    },
                    {data: 'views'},
                    {
                        data: 'status',
                        render: function (data) {
                            switch (data) {
                                case 0:
                                    return '<span class="badge bg-secondary">Chờ xử lý</span>';
                                case 3:
                                    return '<span class="badge bg-warning text-dark">Đang xử lý</span>';
                                case 4:
                                    return '<span class="badge bg-success">Sẵn sàng</span>';
                                case 5:
                                    return '<span class="badge bg-danger">Lỗi</span>';
                                default:
                                    return '<span class="badge bg-info">Đang xử lý</span>';
                            }
                        }
                    },
                    {
                        data: null,
                        orderable: false,
                        render: function (data, type, row) {
                            let buttons = '';

                            // View button only shows when video is ready
                            if (row.status === 4) {
                                buttons += `<button class="btn btn-sm btn-primary action-btn view-video me-1" data-guid="${row.guid}" data-title="${row.title || 'Video'}">
                                <i class="fas fa-play"></i> Xem
                                </button>`;

                                // Add copy embed link button
                                buttons += `<button class="btn btn-sm btn-info action-btn copy-embed me-1" data-guid="${row.guid}">
                                <i class="fas fa-code"></i> Sao chép
                                </button>`;
                            }

                            buttons += `<button class="btn btn-sm btn-danger action-btn delete-video" data-guid="${row.guid}" data-title="${row.title || 'Video không có tiêu đề'}">
                            <i class="fas fa-trash"></i> Xóa
                            </button>`;

                            return buttons;
                        }
                    }
                ],
                order: [[1, 'desc']] // Sort by newest creation date
            });

            // Refresh video list
            $('#refreshVideoList').on('click', function () {
                videoTable.ajax.reload();
            });

            // Thêm lại các sự kiện xử lý cho các nút

            // Handle watch video button
            $(document).on('click', '.view-video', function () {
                const guid = $(this).data('guid');
                const title = $(this).data('title');
                const iframeSrc = `{{$url_media_delivery}}/embed/{{$libraryId}}/${guid}`;

                $('#videoPlayerModalLabel').text(title);
                $('#videoPlayerIframe').attr('src', iframeSrc);

                const videoPlayerModal = new bootstrap.Modal(document.getElementById('videoPlayerModal'));
                videoPlayerModal.show();
            });

            // Handle copy embed link button
            $(document).on('click', '.copy-embed', function () {
                const guid = $(this).data('guid');
                const embedLink = `{{$url_media_delivery}}/embed/{{$libraryId}}/${guid}`;

                // Use modern clipboard API if available
                if (navigator.clipboard && window.isSecureContext) {
                    navigator.clipboard.writeText(embedLink).then(() => {
                        showAlert('Đã sao chép link nhúng video vào clipboard!', 'success');
                    }).catch(() => {
                        // Fallback to old method
                        copyToClipboardFallback(embedLink);
                    });
                } else {
                    // Fallback for older browsers
                    copyToClipboardFallback(embedLink);
                }
            });

            function copyToClipboardFallback(text) {
                const tempInput = document.createElement('input');
                tempInput.value = text;
                document.body.appendChild(tempInput);
                tempInput.select();
                document.execCommand('copy');
                document.body.removeChild(tempInput);
                showAlert('Đã sao chép link nhúng video vào clipboard!', 'success');
            }

            // Handle delete video button
            $(document).on('click', '.delete-video', function () {
                const guid = $(this).data('guid');
                const title = $(this).data('title');

                $('#deleteVideoTitle').text(title);
                $('#confirmDeleteBtn').data('guid', guid);

                const deleteConfirmModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
                deleteConfirmModal.show();
            });

            // Confirm delete video
            $(document).on('click', '#confirmDeleteBtn', function () {
                const guid = $(this).data('guid');
                const csrfToken = $('meta[name="csrf-token"]').attr('content');

                $.ajax({
                    url: '{{ route('admin.bunny.delete') }}',
                    type: 'POST',
                    data: {
                        guid: guid,
                        _token: csrfToken
                    },
                    beforeSend: function () {
                        $('#confirmDeleteBtn').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Đang xóa...');
                    },
                    success: function (response) {
                        bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal')).hide();
                        showAlert('Video đã được xóa thành công', 'success');
                        videoTable.ajax.reload();
                    },
                    error: function (xhr) {
                        let errorMessage = 'Có lỗi xảy ra khi xóa video';
                        if (xhr.responseJSON && xhr.responseJSON.error) {
                            errorMessage = xhr.responseJSON.error;
                        }
                        showAlert(errorMessage, 'danger');
                    },
                    complete: function () {
                        $('#confirmDeleteBtn').prop('disabled', false).text('Xóa');
                    }
                });
            });

            // Close video player when modal is closed
            $('#videoPlayerModal').on('hidden.bs.modal', function () {
                $('#videoPlayerIframe').attr('src', '');
            });

            // Thêm sự kiện khi bảng được làm mới
            videoTable.on('draw', function () {
                checkAllVideosReady();
            });
        });


        function startTableRefresh() {
            // Clear old interval if exists
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }

            // Set up new interval
            refreshInterval = setInterval(() => {
                // Check status of processing videos
                checkVideoStatusPeriodically();
            }, 10000); // 10 seconds
        }


        function checkAllVideosReady() {
            // If no videos are processing, stop refreshing
            if (allVideosReady) {
                return;
            }

            // Get data from table
            const tableData = videoTable.data();

            // Check if all videos are ready
            let allReady = true;
            for (let i = 0; i < tableData.length; i++) {
                const row = tableData[i];
                if (row.status !== 4) { // 4 is Ready status
                    allReady = false;
                    break;
                }
            }

            // If all videos are ready, stop refreshing
            if (allReady && tableData.length > 0) {
                allVideosReady = true;
                if (refreshInterval) {
                    clearInterval(refreshInterval);
                    refreshInterval = null;
                    console.log('All videos are ready, stopped auto-refresh');
                }
            }
        }


        function resetUploadState() {
            currentlyUploading = false;
            uploadAllBtn.disabled = false;

            // Mark all completed files as pending to allow re-upload
            fileQueue.forEach(file => {
                if (file.status === 'complete' || file.status === 'error') {
                    file.status = 'pending';
                    file.progress = 0;
                    updateFileStatus(file.id, 'pending', 0);
                }
            });

            updateQueueUI();
            updateQueueSummary();
        }


        function checkVideoStatusPeriodically() {
            // If no videos are processing, stop checking
            if (allVideosReady) {
                return;
            }

            // Get data from table
            if (videoTable) {
                videoTable.ajax.reload(function () {
                    // Callback after refreshing table
                    checkAllVideosReady();
                }, false);
            }
        }

        // Add statistics functionality
        $(document).ready(function () {
            // Load statistics on page load
            loadLibraryStatistics();

            // Refresh statistics button
            $('#refreshStats').on('click', function () {
                loadLibraryStatistics();
            });

            function loadLibraryStatistics() {
                // Show loading state
                $('#refreshStats').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>');

                $.ajax({
                    url: '{{ route('admin.bunny.statistics') }}',
                    type: 'GET',
                    success: function (response) {
                        updateStatisticsUI(response);
                    },
                    error: function (xhr) {
                        let errorMessage = 'Không thể tải thống kê. Vui lòng kiểm tra cấu hình dịch vụ.';
                        if (xhr.responseJSON && xhr.responseJSON.error) {
                            errorMessage = xhr.responseJSON.error;
                        }
                        showAlert(errorMessage, 'warning');
                    },
                    complete: function () {
                        $('#refreshStats').prop('disabled', false).html('<i class="fas fa-sync-alt"></i> Làm mới');
                    }
                });
            }

            function updateStatisticsUI(data) {
                // Format storage usage
                const storageUsage = formatBytes(data.StorageUsage);
                $('#storageUsage').text(storageUsage);

                // Format traffic usage
                const trafficUsage = formatBytes(data.TrafficUsage);
                $('#trafficUsage').text(trafficUsage);

                // Calculate costs
                // Storage: $0.01 per GB per month, doubled for replication
                const storageGB = data.StorageUsage / 1000000000;


                const storageCost = (storageGB * 0.01 * 2);


                // Traffic: $5 per TB
                const trafficTB = data.TrafficUsage / 1000000000;

                const trafficCost = trafficTB * 0.005;

                // Total cost
                const totalCost = (parseFloat(storageCost) + parseFloat(trafficCost)).toFixed(2);

                // Calculate VND equivalent (exchange rate: 1 USD = 26,000 VND)
                const exchangeRate = 26000;
                const totalCostVND = Math.round(totalCost * exchangeRate).toLocaleString('vi-VN');

                // Display both USD and VND
                $('#totalCost').html(`$${totalCost} <span class="text-muted">(≈ ${totalCostVND} đ)</span>`);

                // Set progress bars (assuming 1TB as max for visualization)
                const maxStorage = 1024 * 1024 * 1024 * 1024; // 1TB in bytes
                const storagePercentage = Math.min(100, (data.StorageUsage / maxStorage) * 100);
                $('#storageProgressBar').css('width', storagePercentage + '%');

                const trafficPercentage = Math.min(100, (data.TrafficUsage / maxStorage) * 100);
                $('#trafficProgressBar').css('width', trafficPercentage + '%');

                // Other library info
                $('#videoCount').text(data.VideoCount);
                $('#libraryName').text(data.Name);

                // Format date
                const createdDate = new Date(data.DateCreated);
                const formattedDate = `${createdDate.getDate().toString().padStart(2, '0')}/${(createdDate.getMonth() + 1).toString().padStart(2, '0')}/${createdDate.getFullYear()}`;
                $('#libraryCreated').text(formattedDate);
            }

            function formatBytes(bytes, decimals = 2) {
                if (bytes === 0) return '0 Bytes';

                const k = 1000; // Using 1000 for decimal conversion (SI units)
                const dm = decimals < 0 ? 0 : decimals;

                // First convert to MB
                const bytesInMB = bytes / 1000000;

                // If it's less than 1000 MB, return in MB
                if (bytesInMB < 1000) {
                    return parseFloat(bytesInMB.toFixed(dm)) + ' MB';
                }
                // Otherwise, return in GB
                else {
                    const bytesInGB = bytesInMB / 1000;
                    return parseFloat(bytesInGB.toFixed(dm)) + ' GB';
                }
            }
        });
    </script>
    @endif

@endsection
