@extends('layouts.admin')
@push('title', 'Mã giảm giá')
@push('meta')@endpush
@push('css')@endpush


@section('content')
    <!-- Mani section header and breadcrumb -->
    <div class="ol-card radius-8px">
        <div class="ol-card-body my-3 py-12px px-20px">
            <div class="d-flex align-items-center justify-content-between gap-3 flex-wrap flex-md-nowrap">
                <h4 class="title fs-16px">
                    <i class="fi-rr-settings-sliders me-2"></i>
                    <span>Mã giảm giá</span>
                </h4>
                <a href="javascript:void(0)" class="btn ol-btn-outline-secondary d-flex align-items-center cg-10px"
                    onclick="ajaxModal('{{ route('modal', ['admin.coupon.create']) }}', 'Thêm mã giảm giá')">
                    <span class="fi-rr-plus"></span>
                    <span>Thêm mã giảm giá</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Hướng dẫn sử dụng -->
    <div class="ol-card radius-8px mb-3">
        <div class="ol-card-body p-3">
            <div class="d-flex align-items-center mb-3">
                <i class="fi-rr-info me-2 text-primary"></i>
                <h5 class="mb-0 text-primary">Hướng dẫn sử dụng mã giảm giá</h5>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="border rounded p-3 h-100">
                        <h6 class="text-success mb-2">
                            <i class="fi-rr-users me-1"></i>
                            Mã giảm giá cho Affiliate
                        </h6>
                        <p class="mb-2 text-muted small">
                            Khi tạo mã giảm giá và gắn với <strong>Người dùng liên kết</strong>:
                        </p>
                        <ul class="small text-muted mb-0">
                            <li>Người dùng affiliate sẽ được cấp mã giảm giá riêng</li>
                            <li>Mã này chỉ dành riêng cho affiliate đó sử dụng</li>
                            <li>Giúp tăng tỷ lệ chuyển đổi khi giới thiệu khóa học</li>
                            <li>Affiliate có thể chia sẻ mã riêng cho khách hàng của mình</li>
                        </ul>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="border rounded p-3 h-100">
                        <h6 class="text-warning mb-2">
                            <i class="fi-rr-key me-1"></i>
                            Mã kích hoạt khóa học (100%)
                        </h6>
                        <p class="mb-2 text-muted small">
                            Khi tạo mã giảm giá <strong>100%</strong>:
                        </p>
                        <ul class="small text-muted mb-0">
                            <li>Có thể sử dụng như mã kích hoạt khóa học</li>
                            <li>Người dùng nhập mã sẽ được kích hoạt khóa học tự động</li>
                            <li>Không cần thanh toán, trực tiếp vào học</li>
                            <li>Phù hợp cho chương trình khuyến mãi hoặc tặng khóa học</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="alert alert-info mt-3 mb-0">
                <i class="fi-rr-bulb me-1"></i>
                <strong>Lưu ý:</strong> Mã giảm giá có thể được cấu hình với số lượng sử dụng và thời gian hết hạn để kiểm soát hiệu quả sử dụng.
            </div>
        </div>
    </div>

    <!-- Start Admin area -->
    <div class="row">
        <div class="col-12">
            <div class="ol-card">
                <div class="ol-card-body p-3">
                    <div class="row print-d-none mb-3 mt-3 row-gap-3">
                        <div class="col-md-6 pt-2 pt-md-0">
                            <div class="custom-dropdown">
                                <button class="dropdown-header btn ol-btn-light">
                                    Xuất dữ liệu
                                    <i class="fi-rr-file-export ms-2"></i>
                                </button>
                                <ul class="dropdown-list">
                                    <li>
                                        <a class="dropdown-item" href="#"
                                            onclick="downloadPDF('.print-table', 'coupon-list')"><i
                                                class="fi-rr-file-pdf"></i> PDF</a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="#" onclick="window.print();"><i
                                                class="fi-rr-print"></i> In</a>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div class="col-md-6 mt-3 mt-md-0">
                            <form action="{{ route('admin.coupons') }}" method="get"
                                class="d-flex gap-3 justify-content-end">
                                <div class="search-input flex-grow-1">
                                    <input type="text" name="search" value="{{ request('search') }}"
                                        placeholder="Tìm kiếm mã giảm giá"
                                        class="ol-form-control form-control" />

                                </div>
                                <button type="submit" class="btn ol-btn-primary"
                                    id="submit-button">Tìm kiếm</button>
                            </form>
                        </div>
                    </div>
                    <!-- Table -->
                    @if (count($coupons) > 0)
                        <div
                            class="admin-tInfo-pagi d-flex justify-content-between justify-content-center align-items-center flex-wrap gr-15">
                            <p class="admin-tInfo">
                                Hiển thị {{ count($coupons) }} trong tổng số {{ $coupons->total() }} dữ liệu
                            </p>
                        </div>
                        <div class="table-responsive course_list" id="course_list">
                            <table class="table eTable eTable-2 print-table">
                                <thead>
                                    <tr>
                                        <th scope="col">#</th>
                                        <th scope="col">Mã giảm giá</th>
                                        <th scope="col">Người dùng liên kết</th>
                                        <th scope="col">Khóa học</th>
                                        <th scope="col">Giảm giá</th>
                                        <th scope="col">Số lượng</th>
                                        <th scope="col">Hết hạn</th>
                                        <th scope="col">Trạng thái</th>
                                        <th scope="col" class="print-d-none">Tùy chọn</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($coupons as $key => $coupon)

                                        <tr>
                                            <th scope="row">
                                                <p class="row-number">{{ ++$key }}</p>
                                            </th>

                                            <td>
                                                <div class="dAdmin_profile d-flex align-items-center min-w-200px">
                                                    <div class="dAdmin_profile_name">
                                                        <h4 class="title fs-14px">{{ $coupon->code }}</h4>
                                                    </div>
                                                </div>
                                            </td>

                                            <td>
                                                <div class="dAdmin_info_name min-w-150px">
                                                    <p>{{ @$coupon->userAff->email }}</p>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="dAdmin_info_name min-w-150px">
                                                    <p>{{ @$coupon->course->title }}</p>
                                                </div>
                                            </td>

                                            <td>
                                                <div class="dAdmin_info_name min-w-150px">
                                                    <p>
                                                        {{ $coupon->discount }}%
                                                    </p>
                                                </div>
                                            </td>

                                            <td>
                                                <div class="dAdmin_info_name min-w-150px">
                                                    <p>
                                                        {{ $coupon->quantity === null ? 'Không giới hạn' : number_format($coupon->quantity) }}
                                                    </p>
                                                </div>
                                            </td>

                                            <td>
                                                <div class="dAdmin_info_name min-w-150px">
                                                    <p>{{ date('d/m/Y', $coupon->expiry) }}</p>
                                                </div>
                                            </td>

                                            <td>
                                                <div class="dAdmin_info_name min-w-150px">
                                                    <p>
                                                        @if($coupon->status == 1)
                                                            <span class="badge bg-success text-white">Công khai</span>
                                                        @elseif($coupon->status == 2)
                                                            <span class="badge bg-warning text-white">Riêng tư</span>
                                                        @else
                                                            <span class="badge bg-danger text-white">Vô hiệu hóa</span>
                                                        @endif
                                                    </p>
                                                </div>
                                            </td>

                                            <td class="print-d-none">
                                                <div class="dropdown ol-icon-dropdown ol-icon-dropdown-transparent">
                                                    <button class="btn ol-btn-secondary dropdown-toggle" type="button"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                        <span class="fi-rr-menu-dots-vertical"></span>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li><a class="dropdown-item" href="#"
                                                                onclick="confirmModal('{{ route('admin.coupon.status', $coupon->id) }}')">{{ $coupon->status ? 'Vô hiệu hóa' : 'Kích hoạt' }}</a>
                                                        </li>
                                                        <li><a class="dropdown-item" href="javascript:void(0);"
                                                                onclick="ajaxModal('{{ route('modal', ['admin.coupon.edit', 'id' => $coupon->id]) }}', 'Chỉnh sửa mã giảm giá')">Chỉnh sửa</a>
                                                        </li>
                                                        <li><a class="dropdown-item" href="javascript:void(0);"
                                                                onclick="confirmModal('{{ route('admin.coupon.delete', $coupon->id) }}')">Xóa</a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        @include('admin.no_data')
                    @endif
                    <!-- Data info and Pagination -->
                    @if (count($coupons) > 0)
                        <div
                            class="admin-tInfo-pagi d-flex justify-content-between justify-content-center align-items-center flex-wrap gr-15">
                            <p class="admin-tInfo">
                                Hiển thị {{ count($coupons) }} trong tổng số {{ $coupons->total() }} dữ liệu
                            </p>
                            {{ $coupons->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
    <!-- End Admin area -->
@endsection
@push('js')@endpush
