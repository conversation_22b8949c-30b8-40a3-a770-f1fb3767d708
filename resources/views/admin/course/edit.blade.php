@extends('layouts.admin')
@push('title', get_phrase('Edit course'))

@section('content')
    <div class="ol-card radius-8px">
        <div class="ol-card-body my-3 py-12px px-20px">
            <div class="d-flex align-items-center justify-content-between gap-3 flex-wrap flex-md-nowrap">
                <h4 class="title fs-16px d-flex align-items-center">
                    <span class="edit-badge py-2 px-3">
                        {{ get_phrase('Editing') }}
                    </span>
                    <span class="d-inline-block ms-3">
                        {{ $course_details->title }}
                    </span>
                </h4>
                <a href="{{ route('admin.courses') }}" class="btn ol-btn-outline-secondary d-flex align-items-center cg-10px ms-auto">
                    <span class="fi-rr-arrow-left"></span>
                    <span>{{ get_phrase('Back') }}</span>
                </a>
                <a href="https://topid.vn" class="btn ol-btn-outline-secondary d-flex align-items-center cg-10px" target="_blank">
                    <span class="fi-rr-arrow-up-right-from-square"></span>
                    <span>{{ get_phrase('Help') }}</span>
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-12">
        <form class="ajaxForm" action="{{ route('admin.course.update', $course_details->id) }}" method="post" enctype="multipart/form-data">@csrf
            <div class="ol-card">
                <div class="ol-card-body p-20px mb-3">

                    <div class="row mb-3">
                        <div class="col-sm-8">
                            <a href="{{ route('course.details', $course_details->slug) }}" target="_blank" class="btn ol-btn-outline-secondary me-3">
                                {{ get_phrase('Frontend View') }}
                                <i class="fi-rr-arrow-up-right-from-square"></i>
                            </a>

                            @php
                                $watch_history = App\Models\Watch_history::where('course_id', $course_details->course_id)
                                    ->where('student_id', auth()->user()->id)
                                    ->first();

                                $lesson = App\Models\Lesson::where('course_id', $course_details->course_id)
                                    ->orderBy('sort', 'asc')
                                    ->first();

                                if (!$watch_history && $lesson) {
                                    $url['slug'] = $course_details->slug;
                                    $lesson_id = '';
                                } else {
                                    if ($watch_history) {
                                        $lesson_id = $watch_history->watching_lesson_id;
                                    } elseif ($lesson) {
                                        $lesson_id = $lesson->id;
                                    }else{
                                        $lesson_id = '';
                                    }
                                    $url['id'] = $lesson_id;
                                }
                            @endphp

                            <a href="{{ route('course.player', ['slug' => $course_details->slug, 'id' => $lesson_id ?? '']) }}" target="_blank" class="btn ol-btn-outline-secondary">
                                {{ get_phrase('Course Player') }}
                                <i class="fi-rr-arrow-up-right-from-square"></i>
                            </a>
                        </div>
                        <div class="col-sm-4 mt-3 mt-sm-0 d-flex justify-content-start justify-content-sm-end">
                            <button type="submit" class="btn ol-btn-outline-secondary save-btn @if (request('tab') == 'live-class' || request('tab') == 'curriculum' || request('tab') == 'statistics') opacity-0 @endif" id="saveChangesBtn">
                                <span class="btn-text">{{ get_phrase('Save Changes') }}</span>
                                <span class="btn-loading d-none">
                                    <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                    Đang lưu...
                                </span>
                            </button>
                        </div>
                    </div>

                    <div class="d-flex gap-3 flex-wrap flex-md-nowrap">
                        <div class="ol-sidebar-tab">
                            <div class="d-flex flex-column">
                                @php
                                    $param = request()->route()->parameter('id');
                                    $tab = request('tab');
                                @endphp

                                <input type="hidden" name="tab" value="{{ $tab }}">

                                <a class="nav-link @if ($tab == 'curriculum' || $tab == '') active @endif" href="{{ route('admin.course.edit', [$param, 'tab' => 'curriculum']) }}">
                                    <span class="fi-rr-edit"></span>
                                    <span>{{ get_phrase('Curriculum') }}</span>
                                </a>

                                <a class="nav-link @if ($tab == 'basic') active @endif" href="{{ route('admin.course.edit', [$param, 'tab' => 'basic']) }}">
                                    <span class="icon fi-rr-duplicate"></span>
                                    <span>{{ get_phrase('Basic') }}</span>
                                </a>
                                @if(addon_check('my.live_class'))

                                <a class="nav-link @if ($tab == 'live-class') active @endif" href="{{ route('admin.course.edit', [$param, 'tab' => 'live-class']) }}">
                                    <span class="fi-rr-file-video"></span>
                                    <span>{{ get_phrase('Live Class') }}</span>
                                </a>
                                @endif
                                <a class="nav-link @if ($tab == 'pricing') active @endif" href="{{ route('admin.course.edit', [$param, 'tab' => 'pricing']) }}">
                                    <span class="fi-rr-comment-dollar"></span>
                                    <span>{{ get_phrase('Pricing') }}</span>
                                </a>

                                <a class="nav-link @if ($tab == 'info') active @endif" href="{{ route('admin.course.edit', [$param, 'tab' => 'info']) }}">
                                    <span class="fi-rr-tags"></span>
                                    <span>{{ get_phrase('Info') }}</span>
                                </a>

                                <a class="nav-link @if ($tab == 'media') active @endif" href="{{ route('admin.course.edit', [$param, 'tab' => 'media']) }}">
                                    <span class="fi fi-rr-gallery"></span>
                                    <span>{{ get_phrase('Media') }}</span>
                                </a>

                                <a class="nav-link @if ($tab == 'seo') active @endif" href="{{ route('admin.course.edit', [$param, 'tab' => 'seo']) }}">
                                    <span class="fi-rr-note-medical"></span>
                                    <span>{{ get_phrase('SEO') }}</span>
                                </a>

                                <a class="nav-link @if ($tab == 'checkout') active @endif" href="{{ route('admin.course.edit', [$param, 'tab' => 'checkout']) }}">
                                    <span class="fi-rr-shopping-cart"></span>
                                    <span>Cấu hình Checkout</span>
                                </a>

                                <a class="nav-link @if ($tab == 'statistics') active @endif" href="{{ route('admin.course.edit', [$param, 'tab' => 'statistics']) }}">
                                    <span class="fi-rr-stats"></span>
                                    <span>Thống kê khóa học</span>
                                </a>

                                @if(addon_check('my.sequential_learning'))
                                <a class="nav-link @if ($tab == 'drip-content') active @endif" href="{{ route('admin.course.edit', [$param, 'tab' => 'drip-content']) }}">
                                    <span class="fi-rr-settings-sliders"></span>
                                    <span>{{ get_phrase('Drip Content') }}</span>
                                </a>
                                @endif
                            </div>
                        </div>
                        <div class="tab-content w-100">
                            @includeWhen($tab == 'curriculum' || $tab == '', 'admin.course.curriculum')
                            @includeWhen($tab == 'basic', 'admin.course.edit_basic')
                            @includeWhen($tab == 'live-class', 'admin.course.live_class')
                            @includeWhen($tab == 'pricing', 'admin.course.edit_pricing')
                            @includeWhen($tab == 'info', 'admin.course.edit_info')
                            @includeWhen($tab == 'media', 'admin.course.edit_media')
                            @includeWhen($tab == 'seo', 'admin.course.edit_seo')
                            @includeWhen($tab == 'checkout', 'admin.course.edit_checkout')
                            @includeWhen($tab == 'statistics', 'admin.course.edit_statistics')
                            @includeWhen($tab == 'drip-content', 'admin.course.edit_drip_settings')
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
@endsection

<style>
/* Professional Save Button Loading States */
.save-btn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    min-width: 140px;
    border: 2px solid #007bff;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: #fff !important;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.save-btn:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    border-color: #0056b3;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 123, 255, 0.4);
}

.save-btn.loading {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    border-color: #ffc107;
    color: #212529;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.5);
    animation: loadingPulse 1.5s ease-in-out infinite;
}

.save-btn.loading:hover {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    transform: none;
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.5);
}

@keyframes loadingPulse {
    0%, 100% {
        box-shadow: 0 4px 12px rgba(255, 193, 7, 0.5);
    }
    50% {
        box-shadow: 0 6px 20px rgba(255, 193, 7, 0.7);
    }
}

.save-btn .btn-text {
    transition: all 0.3s ease;
    display: inline-block;
}

.save-btn .btn-loading {
    transition: all 0.3s ease;
    display: inline-block;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
}

.save-btn.loading .btn-text {
    opacity: 0;
    transform: scale(0.8);
}

.save-btn.loading .btn-loading {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
}

/* Success state animation */
.save-btn.success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-color: #28a745;
    color: white;
    animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 4px 20px rgba(40, 167, 69, 0.5);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
    }
}

/* Error state */
.save-btn.error {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border-color: #dc3545;
    color: white;
    animation: errorShake 0.5s ease-out;
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Spinner customization */
.save-btn .spinner-border-sm {
    width: 1.2rem;
    height: 1.2rem;
    border-width: 0.2em;
    border-color: #212529;
    border-right-color: transparent;
    animation: spinner-border 0.8s linear infinite;
}

.save-btn .btn-loading {
    font-weight: 600;
    color: #212529;
}
</style>

@push('js')
    <script type="text/javascript">
    $(document).ready(function() {
        // Add event listener to the save button
        $('.save-btn').on('click', function() {

        });
    });
    </script>
@endpush
