<style>
    .modern-form-section {
        background: #ffffff;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
        border: 1px solid #f0f0f0;
        transition: all 0.3s ease;
    }

    .modern-form-section:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .section-header {
        display: flex;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #f8f9fa;
    }

    .section-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 1.5rem;
        color: white;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .section-title {
        font-size: 1.4rem;
        font-weight: 600;
        color: #2d3748;
        margin: 0;
    }

    .section-subtitle {
        font-size: 0.9rem;
        color: #718096;
        margin: 0.25rem 0 0 0;
    }

    .modern-form-group {
        margin-bottom: 1.5rem;
    }

    .modern-label {
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.5rem;
        display: block;
        font-size: 0.95rem;
    }

    .modern-input {
        width: 100%;
        padding: 0.875rem 1rem;
        border: 2px solid #e2e8f0;
        border-radius: 10px;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        background: #ffffff;
    }

    .modern-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        transform: translateY(-1px);
    }

    .modern-textarea {
        min-height: 120px;
        resize: vertical;
    }

    .modern-select {
        appearance: none;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 0.5rem center;
        background-repeat: no-repeat;
        background-size: 1.5em 1.5em;
        padding-right: 2.5rem;
    }

    .url-input-group {
        display: flex;
        border: 2px solid #e2e8f0;
        border-radius: 10px;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .url-input-group:focus-within {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .url-prefix {
        background: #f8f9fa;
        padding: 0.875rem 1rem;
        border: none;
        color: #667eea;
        font-weight: 600;
        white-space: nowrap;
    }

    .url-input {
        flex: 1;
        border: none;
        padding: 0.875rem 1rem;
        font-size: 0.95rem;
        background: #ffffff;
    }

    .url-input:focus {
        outline: none;
    }

    .copy-btn {
        background: #667eea;
        color: white;
        border: none;
        padding: 0.875rem 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .copy-btn:hover {
        background: #5a67d8;
        transform: scale(1.05);
    }

    .url-preview {
        margin-top: 0.5rem;
        padding: 0.75rem 1rem;
        background: #f7fafc;
        border-radius: 8px;
        border-left: 4px solid #667eea;
    }

    .url-preview-link {
        color: #667eea;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .url-preview-link:hover {
        color: #5a67d8;
        text-decoration: underline;
    }

    .radio-group {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
        margin-top: 0.5rem;
    }

    .radio-item {
        position: relative;
    }

    .radio-input {
        position: absolute;
        opacity: 0;
        cursor: pointer;
    }

    .radio-label {
        display: block;
        padding: 0.75rem 1rem;
        border: 2px solid #e2e8f0;
        border-radius: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
        font-weight: 500;
        background: #ffffff;
    }

    .radio-input:checked + .radio-label {
        border-color: #667eea;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }

    .checkbox-item {
        display: flex;
        align-items: center;
        padding: 1rem;
        border: 2px solid #e2e8f0;
        border-radius: 10px;
        transition: all 0.3s ease;
        cursor: pointer;
        background: #ffffff;
    }

    .checkbox-item:hover {
        border-color: #667eea;
        background: #f7fafc;
    }

    .checkbox-input {
        margin-right: 0.75rem;
        transform: scale(1.2);
    }

    .feature-highlight {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1rem;
        border-radius: 10px;
        margin-bottom: 1rem;
    }

    .feature-highlight h6 {
        margin: 0 0 0.5rem 0;
        font-weight: 600;
    }

    .feature-highlight p {
        margin: 0;
        opacity: 0.9;
        font-size: 0.9rem;
    }

    @media (max-width: 768px) {
        .modern-form-section {
            padding: 1.5rem;
        }

        .radio-group {
            grid-template-columns: 1fr;
        }
    }
</style>

<input type="hidden" name="course_type" value="general" required>
<input type="hidden" name="instructors[]" value="{{ auth()->user()->id }}" required>

<!-- Thông tin cơ bản -->
<div class="modern-form-section">
    <div class="section-header">
        <div class="section-icon">
            <i class="fi-rr-document"></i>
        </div>
        <div>
            <h3 class="section-title">Thông tin cơ bản</h3>
            <p class="section-subtitle">Cập nhật thông tin cơ bản về khóa học của bạn</p>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="modern-form-group">
                <label class="modern-label">Tên khóa học <span class="text-danger">*</span></label>
                <input type="text" name="title" value="{{ $course_details->title }}" class="modern-input" id="title" required placeholder="Nhập tên khóa học">
            </div>
        </div>

        <div class="col-md-12">
            <div class="modern-form-group">
                <label class="modern-label">Đường dẫn URL <span class="text-danger">*</span></label>
                <div class="url-input-group">
                    <span class="url-prefix">{{ url('khoa-hoc') }}/</span>
                    <input type="text" name="slug" value="{{ $course_details->slug }}" class="url-input" id="slug" required placeholder="duong-dan-url">
                    <button class="copy-btn" type="button" id="copyUrlBtn" title="Sao chép URL đầy đủ">
                        <i class="fi-rr-copy"></i>
                    </button>
                </div>
                <div class="url-preview">
                    <small class="text-muted">URL sẽ là: </small>
                    <a href="{{ $course_details->slug ? route('course.details', ['slug' => $course_details->slug]) : '#' }}"
                       target="_blank"
                       class="url-preview-link"
                       id="full-url-link">
                        <span id="full-url">{{ url('course') }}/{{ $course_details->slug }}</span>
                        <i class="fi-rr-arrow-up-right-from-square ms-1"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="modern-form-group">
                <label class="modern-label">Danh mục <span class="text-danger">*</span></label>
                <select class="modern-input modern-select ol-select2" name="category_id" required>
                    <option value="">Chọn danh mục</option>
                    @foreach (App\Models\Category::where('parent_id', 0)->orderBy('title', 'desc')->get() as $category)
                        <option value="{{ $category->id }}" @if ($course_details->category_id == $category->id) selected @endif>
                            {{ $category->title }}</option>
                        @foreach ($category->childs as $sub_category)
                            <option value="{{ $sub_category->id }}" @if ($course_details->category_id == $sub_category->id) selected @endif>
                                -- {{ $sub_category->title }}</option>
                        @endforeach
                    @endforeach
                </select>
            </div>
        </div>

        <div class="col-md-6">
            <div class="modern-form-group">
                <label class="modern-label">Loại khóa học <span class="text-danger">*</span></label>
                <select class="modern-input modern-select ol-select2" name="level" required>
                    <option value="">Chọn loại khóa học</option>
                    <option value="video" @if ($course_details->level == 'video') selected @endif>Video</option>
                    <option value="ebook" @if ($course_details->level == 'ebook') selected @endif>Ebook</option>
                    <option value="videoandzoom" @if ($course_details->level == 'videoandzoom') selected @endif>Video + Zoom</option>
                </select>
            </div>
        </div>

        <div class="col-md-12">
            <div class="modern-form-group">
                <label class="modern-label">Mô tả ngắn</label>
                <textarea name="short_description" rows="3" class="modern-input modern-textarea" id="short_description" placeholder="Nhập mô tả ngắn về khóa học">{{ $course_details->short_description }}</textarea>
            </div>
        </div>

        <div class="col-md-12">
            <div class="modern-form-group">
                <label class="modern-label">Mô tả chi tiết</label>
                <textarea name="description" rows="5" class="modern-input modern-textarea text_editor" id="description" placeholder="Nhập mô tả chi tiết về khóa học">{!! removeScripts($course_details->description) !!}</textarea>
            </div>
        </div>
    </div>
</div>

<!-- Cài đặt nâng cao -->
<div class="modern-form-section">
    <div class="section-header">
        <div class="section-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <i class="fi-rr-settings"></i>
        </div>
        <div>
            <h3 class="section-title">Cài đặt nâng cao</h3>
            <p class="section-subtitle">Thiết lập các tùy chọn nâng cao cho khóa học</p>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="modern-form-group">
                <label class="modern-label">Template Builder</label>
                <div class="d-flex gap-2">
                    <select class="modern-input modern-select ol-select2 flex-grow-1" name="builder_ids">
                        <option value="">Chọn template builder</option>
                        @foreach(App\Models\Builder_page::where('is_page_courses',1)->get() as $page)
                            <option value="{{strtolower($page->id)}}" @if ($course_details->builder_ids == strtolower($page->id)) selected @endif class="text-capitalize">{{ $page->name }}</option>
                        @endforeach
                    </select>
                    <button type="button" class="btn btn-outline-primary" onclick="window.location.href='{{ route('admin.pages') }}'" title="Tạo template mới">
                        <i class="fi-rr-plus"></i>
                    </button>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="modern-form-group">
                <label class="modern-label">Học thử</label>
                <div class="radio-group">
                    <div class="radio-item">
                        <input type="radio" value="0" name="try_learning" class="radio-input" id="try_learning_disable" @if(!$course_details->try_learning) checked @endif>
                        <label for="try_learning_disable" class="radio-label">
                            <i class="fi-rr-cross me-2"></i>Tắt
                        </label>
                    </div>
                    <div class="radio-item">
                        <input type="radio" value="1" name="try_learning" class="radio-input" id="try_learning_enable" @if($course_details->try_learning) checked @endif>
                        <label for="try_learning_enable" class="radio-label">
                            <i class="fi-rr-check me-2"></i>Bật
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-12">
            <div class="checkbox-item">
                <input type="checkbox" name="is_best" value="1" class="checkbox-input" id="is_best" @if ($course_details->is_best == 1) checked @endif>
                <label for="is_best" class="mb-0">
                    <strong>Hiển thị trên trang chủ</strong><br>
                    <small class="text-muted">Khóa học sẽ được hiển thị trong danh sách khóa học nổi bật</small>
                </label>
            </div>
        </div>
    </div>
</div>

<!-- Trạng thái khóa học -->
<div class="modern-form-section">
    <div class="section-header">
        <div class="section-icon" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
            <i class="fi-rr-shield-check"></i>
        </div>
        <div>
            <h3 class="section-title">Trạng thái khóa học</h3>
            <p class="section-subtitle">Thiết lập trạng thái hiển thị của khóa học</p>
        </div>
    </div>

    <div class="feature-highlight">
        <h6><i class="fi-rr-info me-2"></i>Lưu ý về trạng thái</h6>
        <p>Chọn trạng thái phù hợp để kiểm soát việc hiển thị khóa học cho học viên</p>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="modern-form-group">
                <label class="modern-label">Trạng thái <span class="text-danger">*</span></label>
                <div class="radio-group">
                    <div class="radio-item">
                        <input type="radio" value="active" name="status" class="radio-input" id="status_active" @if ($course_details->status == 'active') checked @endif required>
                        <label for="status_active" class="radio-label">
                            <i class="fi-rr-check-circle me-2"></i>Hoạt động
                        </label>
                    </div>
                    <div class="radio-item">
                        <input type="radio" value="private" name="status" class="radio-input" id="status_private" @if ($course_details->status == 'private') checked @endif required>
                        <label for="status_private" class="radio-label">
                            <i class="fi-rr-lock me-2"></i>Riêng tư
                        </label>
                    </div>
                    <div class="radio-item">
                        <input type="radio" value="upcoming" name="status" class="radio-input" id="status_upcoming" @if ($course_details->status == 'upcoming') checked @endif required>
                        <label for="status_upcoming" class="radio-label">
                            <i class="fi-rr-clock me-2"></i>Sắp ra mắt
                        </label>
                    </div>
                    <div class="radio-item">
                        <input type="radio" value="pending" name="status" class="radio-input" id="status_pending" @if ($course_details->status == 'pending') checked @endif required>
                        <label for="status_pending" class="radio-label">
                            <i class="fi-rr-time-quarter-past me-2"></i>Chờ duyệt
                        </label>
                    </div>
                    <div class="radio-item">
                        <input type="radio" value="draft" name="status" class="radio-input" id="status_draft" @if ($course_details->status == 'draft') checked @endif required>
                        <label for="status_draft" class="radio-label">
                            <i class="fi-rr-edit me-2"></i>Bản nháp
                        </label>
                    </div>
                    <div class="radio-item">
                        <input type="radio" value="inactive" name="status" class="radio-input" id="status_inactive" @if ($course_details->status == 'inactive') checked @endif required>
                        <label for="status_inactive" class="radio-label">
                            <i class="fi-rr-cross-circle me-2"></i>Không hoạt động
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const titleInput = document.getElementById('title');
        const slugInput = document.getElementById('slug');
        const fullUrlSpan = document.getElementById('full-url');
        const fullUrlLink = document.getElementById('full-url-link');
        const copyUrlBtn = document.getElementById('copyUrlBtn');
        const baseUrl = '{{ url('khoa-hoc') }}/';
        let slugManuallyEdited = false;

        // Function to convert title to slug
        function titleToSlug(title) {
            let slug = title.toLowerCase()
                .replace(/\s+/g, '-') // Replace spaces with hyphens
                .replace(/-+/g, '-'); // Replace multiple hyphens with single hyphen

            // Convert Vietnamese characters to non-accented equivalents
            slug = slug.replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, 'a');
            slug = slug.replace(/[èéẹẻẽêềếệểễ]/g, 'e');
            slug = slug.replace(/[ìíịỉĩ]/g, 'i');
            slug = slug.replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, 'o');
            slug = slug.replace(/[ùúụủũưừứựửữ]/g, 'u');
            slug = slug.replace(/[ỳýỵỷỹ]/g, 'y');
            slug = slug.replace(/đ/g, 'd');
            slug = slug.replace(/[ÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴ]/g, 'a');
            slug = slug.replace(/[ÈÉẸẺẼÊỀẾỆỂỄ]/g, 'e');
            slug = slug.replace(/[ÌÍỊỈĨ]/g, 'i');
            slug = slug.replace(/[ÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠ]/g, 'o');
            slug = slug.replace(/[ÙÚỤỦŨƯỪỨỰỬỮ]/g, 'u');
            slug = slug.replace(/[ỲÝỴỶỸ]/g, 'y');
            slug = slug.replace(/Đ/g, 'd');

            // Remove special characters except hyphens
            slug = slug.replace(/[^\w\s-]/g, '');

            // Clean up hyphens
            slug = slug.replace(/\s+/g, '-') // Replace spaces with hyphens
                     .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
                     .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens

            return slug;
        }

        // Function to update the full URL display
        function updateFullUrl(slug) {
            const fullUrl = baseUrl + slug;
            fullUrlSpan.textContent = fullUrl;
            fullUrlLink.href = slug ? '{{ url('course') }}/' + slug : '#';
            return fullUrl;
        }

        // Update slug and full URL when title changes (if slug hasn't been manually edited)
        titleInput.addEventListener('input', function() {
            if (!slugManuallyEdited) {
                const newSlug = titleToSlug(this.value);
                slugInput.value = newSlug;
                updateFullUrl(newSlug);
            }
        });

        // Update full URL when slug is manually edited
        slugInput.addEventListener('input', function() {
            slugManuallyEdited = true;
            const newSlug = titleToSlug(this.value);
            this.value = newSlug; // Ensure the slug is always properly formatted
            updateFullUrl(newSlug);
        });

        // Copy URL functionality
        copyUrlBtn.addEventListener('click', function() {
            const fullUrl = baseUrl + slugInput.value;
            navigator.clipboard.writeText(fullUrl).then(() => {
                // Show success feedback
                const originalIcon = copyUrlBtn.innerHTML;
                copyUrlBtn.innerHTML = '<i class="fi-rr-check"></i>';
                copyUrlBtn.style.background = '#10b981';

                setTimeout(() => {
                    copyUrlBtn.innerHTML = originalIcon;
                    copyUrlBtn.style.background = '';
                }, 2000);
            }).catch(err => {
                console.error('Could not copy text: ', err);
            });
        });

        // Add floating labels effect
        $('.modern-input').on('focus blur', function() {
            $(this).toggleClass('focused');
        });

        // Auto-resize textareas
        $('textarea.modern-textarea').on('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });

        // Add ripple effect to radio buttons
        $('.radio-label').on('click', function(e) {
            const ripple = $('<span class="ripple"></span>');
            $(this).append(ripple);

            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.css({
                width: size,
                height: size,
                left: x,
                top: y
            }).addClass('ripple-animation');

            setTimeout(() => ripple.remove(), 600);
        });

        // Form validation
        $('form').on('submit', function(e) {
            let isValid = true;
            let errorMessage = '';

            // Check required fields
            const title = $('input[name="title"]').val().trim();
            const category = $('select[name="category_id"]').val();
            const level = $('select[name="level"]').val();

            if (!title) {
                isValid = false;
                errorMessage += '• Vui lòng nhập tên khóa học\n';
            }

            if (!category) {
                isValid = false;
                errorMessage += '• Vui lòng chọn danh mục\n';
            }

            if (!level) {
                isValid = false;
                errorMessage += '• Vui lòng chọn loại khóa học\n';
            }

            if (!isValid) {
                e.preventDefault();
                alert('Vui lòng kiểm tra lại thông tin:\n\n' + errorMessage);
                return false;
            }
        });

        // Smooth scroll to error fields
        $('.modern-input, .modern-select').on('invalid', function() {
            this.scrollIntoView({ behavior: 'smooth', block: 'center' });
        });

        // Initialize full URL on page load
        updateFullUrl(slugInput.value);
    });
</script>

<style>
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        pointer-events: none;
        transform: scale(0);
    }

    .ripple-animation {
        animation: ripple-effect 0.6s ease-out;
    }

    @keyframes ripple-effect {
        to {
            transform: scale(2);
            opacity: 0;
        }
    }

    .modern-input.focused {
        transform: translateY(-1px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
    }

    .btn-outline-primary {
        border: 2px solid #667eea;
        color: #667eea;
        background: transparent;
        border-radius: 10px;
        padding: 0.875rem 1rem;
        transition: all 0.3s ease;
    }

    .btn-outline-primary:hover {
        background: #667eea;
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }

    .d-flex.gap-2 {
        gap: 0.5rem;
    }

    /* Smooth transitions for form sections */
    .modern-form-section {
        animation: slideInUp 0.6s ease-out;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Enhanced select2 styling */
    .select2-container--default .select2-selection--single {
        border: 2px solid #e2e8f0 !important;
        border-radius: 10px !important;
        height: auto !important;
        padding: 0.375rem 0.5rem !important;
    }

    .select2-container--default .select2-selection--single:focus {
        border-color: #667eea !important;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        padding: 0.5rem 0.5rem !important;
        line-height: 1.5 !important;
    }
</style>
