@php
    $faqs = json_decode($course_details->faqs, true);
    $outcomes = json_decode($course_details->outcomes, true);
    $requirements = json_decode($course_details->requirements, true);
@endphp

<style>
.course-info-section {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    border: 1px solid #e8ecef;
    margin-bottom: 24px;
    overflow: hidden;
}

.section-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 24px;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.section-header i {
    font-size: 20px;
}

.section-header h5 {
    margin: 0;
    font-weight: 600;
    font-size: 16px;
}

.section-content {
    padding: 24px;
}

.faq-item, .requirement-item, .outcome-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    transition: all 0.3s ease;
}

.faq-item:hover, .requirement-item:hover, .outcome-item:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
}

.action-btn {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    transition: all 0.3s ease;
    margin-left: 8px;
}

.btn-add {
    background: #28a745;
    color: white;
}

.btn-add:hover {
    background: #218838;
    transform: translateY(-1px);
}

.btn-remove {
    background: #dc3545;
    color: white;
}

.btn-remove:hover {
    background: #c82333;
    transform: translateY(-1px);
}

.form-control {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    padding: 12px 16px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}
</style>

<!-- FAQ Section -->
<div class="course-info-section">
    <div class="section-header">
        <i class="fi-rr-interrogation"></i>
        <h5>Câu hỏi thường gặp (FAQ)</h5>
    </div>
    <div class="section-content">
        <div id="faq_area">
            @if(is_array($faqs) && count($faqs) > 0)
                @foreach($faqs as $key => $faq)
                <div class="faq-item">
                    <div class="d-flex align-items-start">
                        <div class="flex-grow-1">
                            <div class="form-group mb-3">
                                <label class="form-label fw-semibold text-dark mb-2">
                                    <i class="fi-rr-comment-question me-2"></i>Câu hỏi
                                </label>
                                <input type="text" value="{{$faq['title'] ?? ''}}" class="form-control" name="faq_title[]" id="faqs{{$key ?? ''}}" placeholder="Nhập câu hỏi thường gặp...">
                            </div>
                            <div class="form-group">
                                <label class="form-label fw-semibold text-dark mb-2">
                                    <i class="fi-rr-comment-check me-2"></i>Câu trả lời
                                </label>
                                <textarea name="faq_description[]" rows="3" class="form-control" placeholder="Nhập câu trả lời chi tiết...">{{$faq['description'] ?? ''}}</textarea>
                            </div>
                        </div>
                        <div class="d-flex flex-column">
                            @if($key == 0)
                                <button type="button" class="action-btn btn-add" data-bs-toggle="tooltip" title="Thêm FAQ mới" onclick="appendFaq()">
                                    <i class="fi-rr-plus"></i>
                                </button>
                            @else
                                <button type="button" class="action-btn btn-remove" data-bs-toggle="tooltip" title="Xóa FAQ này" onclick="removeFaq(this)">
                                    <i class="fi-rr-trash"></i>
                                </button>
                            @endif
                        </div>
                    </div>
                </div>
                @endforeach
            @else
                <div class="faq-item">
                    <div class="d-flex align-items-start">
                        <div class="flex-grow-1">
                            <div class="form-group mb-3">
                                <label class="form-label fw-semibold text-dark mb-2">
                                    <i class="fi-rr-comment-question me-2"></i>Câu hỏi
                                </label>
                                <input type="text" class="form-control" name="faq_title[]" id="faqs" placeholder="Nhập câu hỏi thường gặp...">
                            </div>
                            <div class="form-group">
                                <label class="form-label fw-semibold text-dark mb-2">
                                    <i class="fi-rr-comment-check me-2"></i>Câu trả lời
                                </label>
                                <textarea name="faq_description[]" rows="3" class="form-control" placeholder="Nhập câu trả lời chi tiết..."></textarea>
                            </div>
                        </div>
                        <div class="d-flex flex-column">
                            <button type="button" class="action-btn btn-add" data-bs-toggle="tooltip" title="Thêm FAQ mới" onclick="appendFaq()">
                                <i class="fi-rr-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
            @endif
            <div id="blank_faq_field" style="display: none;">
                <div class="faq-item">
                    <div class="d-flex align-items-start">
                        <div class="flex-grow-1">
                            <div class="form-group mb-3">
                                <label class="form-label fw-semibold text-dark mb-2">
                                    <i class="fi-rr-comment-question me-2"></i>Câu hỏi
                                </label>
                                <input type="text" class="form-control" name="faq_title[]" placeholder="Nhập câu hỏi thường gặp...">
                            </div>
                            <div class="form-group">
                                <label class="form-label fw-semibold text-dark mb-2">
                                    <i class="fi-rr-comment-check me-2"></i>Câu trả lời
                                </label>
                                <textarea name="faq_description[]" rows="3" class="form-control" placeholder="Nhập câu trả lời chi tiết..."></textarea>
                            </div>
                        </div>
                        <div class="d-flex flex-column">
                            <button type="button" class="action-btn btn-remove" data-bs-toggle="tooltip" title="Xóa FAQ này" onclick="removeFaq(this)">
                                <i class="fi-rr-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Requirements Section -->
<div class="course-info-section">
    <div class="section-header">
        <i class="fi-rr-list-check"></i>
        <h5>Yêu cầu khóa học</h5>
    </div>
    <div class="section-content">
        <div id="requirement_area">
            @if(is_array($requirements) && count($requirements) > 0)
                @foreach($requirements as $key => $requirement)
                <div class="requirement-item">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="form-group mb-0">
                                <div class="input-group">
                                    <span class="input-group-text bg-light border-end-0">
                                        <i class="fi-rr-check-circle text-success"></i>
                                    </span>
                                    <input type="text" value="{{$requirement}}" class="form-control border-start-0" name="requirements[]" placeholder="Nhập yêu cầu cho khóa học...">
                                </div>
                            </div>
                        </div>
                        <div class="ms-3">
                            @if($key == 0)
                                <button type="button" class="action-btn btn-add" data-bs-toggle="tooltip" title="Thêm yêu cầu mới" onclick="appendRequirement()">
                                    <i class="fi-rr-plus"></i>
                                </button>
                            @else
                                <button type="button" class="action-btn btn-remove" data-bs-toggle="tooltip" title="Xóa yêu cầu này" onclick="removeRequirement(this)">
                                    <i class="fi-rr-trash"></i>
                                </button>
                            @endif
                        </div>
                    </div>
                </div>
                @endforeach
            @else
                <div class="requirement-item">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="form-group mb-0">
                                <div class="input-group">
                                    <span class="input-group-text bg-light border-end-0">
                                        <i class="fi-rr-check-circle text-success"></i>
                                    </span>
                                    <input type="text" class="form-control border-start-0" name="requirements[]" placeholder="Nhập yêu cầu cho khóa học...">
                                </div>
                            </div>
                        </div>
                        <div class="ms-3">
                            <button type="button" class="action-btn btn-add" data-bs-toggle="tooltip" title="Thêm yêu cầu mới" onclick="appendRequirement()">
                                <i class="fi-rr-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
            @endif
            <div id="blank_requirement_field" style="display: none;">
                <div class="requirement-item">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="form-group mb-0">
                                <div class="input-group">
                                    <span class="input-group-text bg-light border-end-0">
                                        <i class="fi-rr-check-circle text-success"></i>
                                    </span>
                                    <input type="text" class="form-control border-start-0" name="requirements[]" placeholder="Nhập yêu cầu cho khóa học...">
                                </div>
                            </div>
                        </div>
                        <div class="ms-3">
                            <button type="button" class="action-btn btn-remove" data-bs-toggle="tooltip" title="Xóa yêu cầu này" onclick="removeRequirement(this)">
                                <i class="fi-rr-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Outcomes Section -->
<div class="course-info-section">
    <div class="section-header">
        <i class="fi-rr-target"></i>
        <h5>Kết quả đạt được</h5>
    </div>
    <div class="section-content">
        <div id="outcomes_area">
            @if(is_array($outcomes) && count($outcomes) > 0)
                @foreach($outcomes as $key => $outcome)
                <div class="outcome-item">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="form-group mb-0">
                                <div class="input-group">
                                    <span class="input-group-text bg-light border-end-0">
                                        <i class="fi-rr-star text-warning"></i>
                                    </span>
                                    <input type="text" value="{{$outcome}}" class="form-control border-start-0" name="outcomes[]" placeholder="Nhập kết quả học viên sẽ đạt được...">
                                </div>
                            </div>
                        </div>
                        <div class="ms-3">
                            @if($key == 0)
                                <button type="button" class="action-btn btn-add" data-bs-toggle="tooltip" title="Thêm kết quả mới" onclick="appendOutcome()">
                                    <i class="fi-rr-plus"></i>
                                </button>
                            @else
                                <button type="button" class="action-btn btn-remove" data-bs-toggle="tooltip" title="Xóa kết quả này" onclick="removeOutcome(this)">
                                    <i class="fi-rr-trash"></i>
                                </button>
                            @endif
                        </div>
                    </div>
                </div>
                @endforeach
            @else
                <div class="outcome-item">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="form-group mb-0">
                                <div class="input-group">
                                    <span class="input-group-text bg-light border-end-0">
                                        <i class="fi-rr-star text-warning"></i>
                                    </span>
                                    <input type="text" class="form-control border-start-0" name="outcomes[]" placeholder="Nhập kết quả học viên sẽ đạt được...">
                                </div>
                            </div>
                        </div>
                        <div class="ms-3">
                            <button type="button" class="action-btn btn-add" data-bs-toggle="tooltip" title="Thêm kết quả mới" onclick="appendOutcome()">
                                <i class="fi-rr-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
            @endif
            <div id="blank_outcome_field" style="display: none;">
                <div class="outcome-item">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="form-group mb-0">
                                <div class="input-group">
                                    <span class="input-group-text bg-light border-end-0">
                                        <i class="fi-rr-star text-warning"></i>
                                    </span>
                                    <input type="text" class="form-control border-start-0" name="outcomes[]" placeholder="Nhập kết quả học viên sẽ đạt được...">
                                </div>
                            </div>
                        </div>
                        <div class="ms-3">
                            <button type="button" class="action-btn btn-remove" data-bs-toggle="tooltip" title="Xóa kết quả này" onclick="removeOutcome(this)">
                                <i class="fi-rr-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Target Audience Section -->
<div class="course-info-section">
    <div class="section-header">
        <i class="fi-rr-users"></i>
        <h5>Khóa học dành cho Ai</h5>
    </div>
    <div class="section-content">
        <div class="form-group">
            <label class="form-label fw-semibold text-dark mb-3">
                <i class="fi-rr-user me-2 text-primary"></i>Đối tượng mục tiêu
            </label>
            <textarea name="target_audience" rows="4" class="form-control" placeholder="Mô tả đối tượng mục tiêu của khóa học này. Ví dụ: Người mới bắt đầu học lập trình, sinh viên IT, nhân viên văn phòng muốn chuyển nghề...">{{$course_details->target_audience ?? ''}}</textarea>
        </div>
    </div>
</div>

@push('js')
<script type="text/javascript">
    "use strict";

    var blank_faq = jQuery('#blank_faq_field').html();
    var blank_outcome = jQuery('#blank_outcome_field').html();
    var blank_requirement = jQuery('#blank_requirement_field').html();

    jQuery(document).ready(function() {
        jQuery('#blank_faq_field').hide();
        jQuery('#blank_outcome_field').hide();
        jQuery('#blank_requirement_field').hide();

        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Add smooth animations
        jQuery('.course-info-section').each(function(index) {
            jQuery(this).css('animation-delay', (index * 0.1) + 's');
        });
    });

    function appendFaq() {
        var newFaq = jQuery(blank_faq);
        newFaq.hide().appendTo('#faq_area').slideDown(300);

        // Reinitialize tooltips for new elements
        newFaq.find('[data-bs-toggle="tooltip"]').each(function() {
            new bootstrap.Tooltip(this);
        });
    }

    function removeFaq(faqElem) {
        jQuery(faqElem).closest('.faq-item').slideUp(300, function() {
            jQuery(this).remove();
        });
    }

    function appendOutcome() {
        var newOutcome = jQuery(blank_outcome);
        newOutcome.hide().appendTo('#outcomes_area').slideDown(300);

        // Reinitialize tooltips for new elements
        newOutcome.find('[data-bs-toggle="tooltip"]').each(function() {
            new bootstrap.Tooltip(this);
        });
    }

    function removeOutcome(outcomeElem) {
        jQuery(outcomeElem).closest('.outcome-item').slideUp(300, function() {
            jQuery(this).remove();
        });
    }

    function appendRequirement() {
        var newRequirement = jQuery(blank_requirement);
        newRequirement.hide().appendTo('#requirement_area').slideDown(300);

        // Reinitialize tooltips for new elements
        newRequirement.find('[data-bs-toggle="tooltip"]').each(function() {
            new bootstrap.Tooltip(this);
        });
    }

    function removeRequirement(requirementElem) {
        jQuery(requirementElem).closest('.requirement-item').slideUp(300, function() {
            jQuery(this).remove();
        });
    }
</script>

<style>
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.course-info-section {
    animation: fadeInUp 0.6s ease-out forwards;
}

.input-group-text {
    background: #f8f9fa !important;
    border-color: #dee2e6 !important;
}

.form-control:focus + .input-group-text,
.input-group-text + .form-control:focus {
    border-color: #667eea !important;
}

.section-header {
    position: relative;
    overflow: hidden;
}

.section-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.section-header:hover::before {
    left: 100%;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .section-content {
        padding: 16px;
    }

    .faq-item, .requirement-item, .outcome-item {
        padding: 12px;
    }

    .d-flex.align-items-start,
    .d-flex.align-items-center {
        flex-direction: column;
        align-items: stretch !important;
    }

    .ms-3 {
        margin-left: 0 !important;
        margin-top: 12px;
        text-align: center;
    }

    .action-btn {
        width: 100%;
        max-width: 120px;
        margin: 0 auto;
    }
}
</style>
@endpush
