@extends('layouts.admin')
@push('title', get_phrase('Dashboard'))
@push('meta')@endpush
@push('css')
    <style>
        /* Modern Dashboard Styles */
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            color: white;
            margin-bottom: 30px;
            overflow: hidden;
            position: relative;
        }

        .dashboard-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="50" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="30" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .dashboard-header .title {
            color: white !important;
            font-weight: 600;
            font-size: 18px;
        }

        /* Enhanced Statistics Cards */
        .stats-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            height: 100%;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--card-gradient);
            transition: height 0.3s ease;
        }

        .stats-card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        }

        .stats-card:hover::before {
            height: 6px;
        }

        .stats-card.courses {
            --card-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stats-card.lessons {
            --card-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .stats-card.enrollments {
            --card-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .stats-card.students {
            --card-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .stats-card.instructors {
            --card-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        .stats-card.storage {
            --card-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }

        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
            background: var(--card-gradient);
            color: white;
            font-size: 24px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        }

        .stats-number {
            font-size: 32px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 8px;
            line-height: 1;
        }

        .storage-number {
            font-size: 32px;
            font-weight: 700;
            color: #2d3748;
        }

        .storage-unit {
            font-size: 18px;
            font-weight: 500;
            color: #718096;
            margin-left: 4px;
            vertical-align: top;
        }

        .stats-label {
            font-size: 14px;
            color: #718096;
            font-weight: 500;
            margin-bottom: 12px;
        }

        .stats-trend {
            display: flex;
            align-items: center;
            font-size: 12px;
            font-weight: 600;
        }

        .stats-trend.up {
            color: #48bb78;
        }

        .stats-trend.down {
            color: #f56565;
        }

        .stats-trend-icon {
            margin-right: 4px;
            font-size: 10px;
        }

        /* Enhanced Chart Container */
        .chart-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 30px;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f7fafc;
        }

        .chart-title {
            font-size: 20px;
            font-weight: 700;
            color: #2d3748;
            margin: 0;
        }

        .chart-subtitle {
            font-size: 14px;
            color: #718096;
            margin-top: 4px;
        }

        /* Enhanced Pie Chart Section */
        .pie-chart-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.1);
            height: 100%;
        }

        .pie-chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f7fafc;
        }

        .pie-chart-title {
            font-size: 18px;
            font-weight: 700;
            color: #2d3748;
            margin: 0;
        }

        .color-info-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .color-info-list li {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            padding: 8px 12px;
            border-radius: 8px;
            transition: background-color 0.2s ease;
        }

        .color-info-list li:hover {
            background-color: #f7fafc;
        }

        .info-list-color {
            width: 16px;
            height: 16px;
            border-radius: 4px;
            margin-right: 12px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .color-info-list .title2 {
            font-weight: 600;
            color: #4a5568;
        }

        /* Background colors for pie chart legend */
        .bg-active { background-color: #12c093 !important; }
        .bg-upcoming { background-color: #1b84ff !important; }
        .bg-pending { background-color: #ff2583 !important; }
        .bg-private { background-color: #6c757d !important; }
        .bg-draft { background-color: #ffc107 !important; }
        .bg-inactive { background-color: #dc3545 !important; }

        /* Enhanced Table Styles */
        .table-responsive {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .eTable {
            margin-bottom: 0;
            background: white;
        }

        .eTable thead th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: none;
            font-weight: 600;
            color: #495057;
            padding: 16px 20px;
            font-size: 14px;
        }

        .eTable tbody tr {
            transition: all 0.2s ease;
            border-bottom: 1px solid #f1f3f4;
        }

        .eTable tbody tr:hover {
            background-color: #f8f9ff;
        }

        .eTable tbody td {
            padding: 16px 20px;
            vertical-align: middle;
            border: none;
        }

        .dAdmin_profile_img img {
            border: 3px solid #fff;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn-success, .btn-danger {
            border-radius: 8px;
            font-weight: 500;
            padding: 8px 16px;
            font-size: 12px;
            transition: all 0.2s ease;
        }

        .btn-success:hover {
            box-shadow: 0 3px 8px rgba(40, 167, 69, 0.25);
        }

        .btn-danger:hover {
            box-shadow: 0 3px 8px rgba(220, 53, 69, 0.25);
        }

        /* Leaderboard Styles */
        .leaderboard-section {
            margin-bottom: 20px;
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .medal-container {
            display: flex;
            justify-content: center;
            align-items: flex-end;
            margin-bottom: 30px;
            padding: 20px 0;
            position: relative;
        }

        .medal-item {
            text-align: center;
            position: relative;
            transition: transform 0.3s ease;
            margin: 0 20px;
        }

        .medal-item:hover {
            filter: brightness(1.1);
        }

        .medal-item.first-place {
            margin-bottom: -20px;
            z-index: 3;
        }

        .medal-img {
            width: 90px;
            height: auto;
            margin: 0 auto 10px;
            transition: all 0.3s ease;
            filter: drop-shadow(0 5px 10px rgba(0, 0, 0, 0.1));
        }

        .medal-username {
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 5px;
            color: #444;
            max-width: 120px;
            margin: 0 auto 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .medal-score {
            font-size: 22px;
            font-weight: bold;
            color: #333;
            background: linear-gradient(135deg, #3a7bd5, #00d2ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .podium {
            display: flex;
            justify-content: center;
            align-items: flex-end;
            height: 60px;
            margin-top: 15px;
        }

        .podium-step {
            width: 90px;
            background: linear-gradient(to bottom, #f0f0f0, #e0e0e0);
            border-radius: 5px 5px 0 0;
            margin: 0 5px;
            box-shadow: 0 -3px 5px rgba(0, 0, 0, 0.05);
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #666;
            font-weight: bold;
            font-size: 14px;
        }

        .podium-step.gold {
            height: 60px;
            background: linear-gradient(to bottom, #fceabb, #fccd4d);
        }

        .podium-step.silver {
            height: 45px;
            background: linear-gradient(to bottom, #e0e0e0, #c0c0c0);
        }

        .podium-step.bronze {
            height: 30px;
            background: linear-gradient(to bottom, #ebc298, #cd7f32);
        }

        .leaderboard-list {
            margin-top: 20px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            background: white;
        }

        .leaderboard-list-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 20px;
            border-bottom: 1px solid #eaeaea;
            transition: all 0.2s ease;
        }

        .leaderboard-list-item:hover {
            background-color: #f9f9f9;
        }

        .leaderboard-list-item:last-child {
            border-bottom: none;
        }

        .rank {
            font-weight: bold;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: #f4f4f4;
            box-shadow: inset 0 0 0 2px #e0e0e0;
        }

        .toggle-view {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 15px;
        }

        .toggle-btn {
            border: none;
            background: #f0f0f0;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
            margin-left: 5px;
            font-weight: 500;
            color: #666;
        }

        .toggle-btn.active {
            background: #007bff;
            color: white;
            box-shadow: 0 3px 8px rgba(0, 123, 255, 0.3);
        }

        .toggle-btn:hover:not(.active) {
            background: #e0e0e0;
        }

        .leaderboard-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .leaderboard-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-right: auto;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .stats-card {
                margin-bottom: 20px;
            }

            .chart-container,
            .pie-chart-container {
                padding: 20px;
            }

            .medal-container {
                flex-direction: column;
                align-items: center;
            }

            .medal-item {
                margin: 10px 0;
            }
        }

        /* Animation for page load */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .stats-card,
        .chart-container,
        .pie-chart-container {
            animation: fadeInUp 0.6s ease-out;
        }

        .stats-card:nth-child(1) { animation-delay: 0.1s; }
        .stats-card:nth-child(2) { animation-delay: 0.2s; }
        .stats-card:nth-child(3) { animation-delay: 0.3s; }
        .stats-card:nth-child(4) { animation-delay: 0.4s; }
        .stats-card:nth-child(5) { animation-delay: 0.5s; }
        .stats-card:nth-child(6) { animation-delay: 0.6s; }

        /* System expiry badge styles */
        .badge.bg-warning {
            background: var(--bs-form-valid-color) !important;
            animation: none !important;
        }

        .badge.bg-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
            animation: none !important;
        }
.expiry-date {
    display: block !important;
}
    </style>
@endpush
@section('content')
    <!-- Enhanced Dashboard Header -->
    <div class="dashboard-header">
        <div class="ol-card-body my-3 py-4 px-20px">


            <div class="d-flex align-items-center justify-content-between gap-3 flex-wrap flex-md-nowrap">
                <h4 class="title fs-16px">
                    <i class="fi-rr-settings-sliders me-2"></i>
                    Bảng điều khiển
                </h4>
                <div class="d-flex align-items-center gap-2">
                    <span class="badge bg-light text-dark">{{ date('d/m/Y') }}</span>
                    <span class="badge bg-light text-dark">{{ date('H:i') }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Statistics Cards -->
    <div class="row g-3 my-3">
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="stats-card courses">
                <div class="stats-icon">
                    <i class="fi-rr-book-alt"></i>
                </div>
                <div class="stats-number">{{ $statistics['course_count'] }}</div>
                <div class="stats-label">Số khóa học</div>
                <div class="stats-trend up">
                    <span class="stats-trend-icon">↗</span>
                    <span>+12% so với tháng trước</span>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="stats-card lessons">
                <div class="stats-icon">
                    <i class="fi-rr-play-alt"></i>
                </div>
                <div class="stats-number">{{ $statistics['lesson_count'] }}</div>
                <div class="stats-label">Số bài học</div>
                <div class="stats-trend up">
                    <span class="stats-trend-icon">↗</span>
                    <span>+8% so với tháng trước</span>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="stats-card enrollments">
                <div class="stats-icon">
                    <i class="fi-rr-graduation-cap"></i>
                </div>
                <div class="stats-number">{{ $statistics['enrollment_count'] }}</div>
                <div class="stats-label">Số đăng ký</div>
                <div class="stats-trend up">
                    <span class="stats-trend-icon">↗</span>
                    <span>+15% so với tháng trước</span>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="stats-card students">
                <div class="stats-icon">
                    <i class="fi-rr-users-alt"></i>
                </div>
                <div class="stats-number">{{ $statistics['student_count'] }}</div>
                <div class="stats-label">Số học viên</div>
                <div class="stats-trend up">
                    <span class="stats-trend-icon">↗</span>
                    <span>+5% so với tháng trước</span>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="stats-card instructors">
                <div class="stats-icon">
                    <i class="fi-rr-chalkboard-user"></i>
                </div>
                <div class="stats-number">{{ $statistics['instructor_count'] }}</div>
                <div class="stats-label">Số giảng viên</div>
                <div class="stats-trend up">
                    <span class="stats-trend-icon">↗</span>
                    <span>+3% so với tháng trước</span>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="stats-card storage">
                <div class="stats-icon">
                    <i class="fi-rr-disk"></i>
                </div>
                @php
                    $storageUsage = $statistics['storage_usage'] ?? null;
                    $totalFormatted = $storageUsage['formatted_total'] ?? '0 B';
                    $parts = explode(' ', $totalFormatted);
                    $number = $parts[0] ?? '0';
                    $unit = $parts[1] ?? 'B';
                @endphp
                <div class="stats-number" title="Tổng: {{ $storageUsage['formatted_total'] ?? 'Đang tính...' }}&#10;Code: {{ $storageUsage['formatted_project'] ?? 'Đang tính...' }}&#10;Database: {{ $storageUsage['formatted_database'] ?? 'Đang tính...' }}">
                    <span class="storage-number">{{ $number }}</span>
                    <span class="storage-unit">{{ $unit }}</span>
                </div>
                <div class="stats-label">Dung lượng sử dụng</div>
                <div class="stats-trend up">
                    <span class="stats-trend-icon">💾</span>
                    <span>{{ $storageUsage && $storageUsage['total_size'] > 0 ? 'Code + Database' : 'Đang tính toán...' }}</span>
                    @if(!$storageUsage || $storageUsage['total_size'] == 0)
                        <button onclick="refreshStorageCache()" class="btn btn-sm btn-outline-primary ms-2" style="font-size: 10px; padding: 2px 6px;">
                            <i class="fi-rr-refresh"></i>
                        </button>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Revenue Chart -->
    <div class="row">
        <div class="col-xl-12">
            <div class="chart-container">
                <div class="chart-header">
                    <div>
                        <h2 class="chart-title">Doanh thu năm {{ date('Y') }}</h2>
                        <p class="chart-subtitle">Thống kê doanh thu theo từng tháng trong năm</p>
                    </div>
                    <div class="d-flex align-items-center gap-3">
                        <div class="d-flex align-items-center gap-2">
                            <div style="width: 12px; height: 12px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%;"></div>
                            <span class="text-sm text-gray-600">Tổng doanh thu</span>
                        </div>
                        <div class="d-flex align-items-center gap-2">
                            <div style="width: 12px; height: 12px; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border-radius: 50%;"></div>
                            <span class="text-sm text-gray-600">Hoa hồng affiliate</span>
                        </div>
                        <div class="d-flex align-items-center gap-2">
                            <div style="width: 12px; height: 12px; background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border-radius: 50%;"></div>
                            <span class="text-sm text-gray-600">Doanh thu thực tế</span>
                        </div>
                        <a class="btn btn-outline-primary btn-sm" href="{{route('admin.revenue')}}" data-bs-toggle="tooltip"
                           data-bs-placement="bottom" title="Xem chi tiết doanh thu">
                            <i class="fi-rr-arrow-alt-right me-1"></i>
                            Chi tiết
                        </a>
                    </div>
                </div>
                <div class="chart-body">
                    <canvas id="myChart" class="mw-100 w-100" height="320px"></canvas>
                </div>
            </div>
        </div>
    </div>


    <div class="row my-4">
        <div class="col-md-5">
            <div class="pie-chart-container">
                <div class="pie-chart-header">
                    <div>
                        <h4 class="pie-chart-title">Trạng thái khóa học</h4>
                        <p class="chart-subtitle">Phân bố khóa học theo trạng thái</p>
                    </div>
                    <a class="btn btn-outline-primary btn-sm" href="{{route('admin.courses')}}" data-bs-toggle="tooltip"
                       data-bs-placement="bottom" title="Khám phá khóa học">
                        <i class="fi-rr-arrow-alt-right me-1"></i>
                        Xem tất cả
                    </a>
                </div>
                <div class="d-flex align-items-center g-30px flex-wrap flex-xl-nowrap justify-content-center">
                    <div class="pie-chart-sm">
                        <canvas id="pie2"></canvas>
                    </div>
                    <div class="pie-chart-sm-details">
                        <ul class="color-info-list">
                            <li>
                                <span class="info-list-color bg-active"></span>
                                <span class="title2 fs-14px">Đang hoạt động ({{ $course_statistics['active'] }})</span>
                            </li>
                            <li>
                                <span class="info-list-color bg-upcoming"></span>
                                <span class="title2 fs-14px">Sắp ra mắt ({{ $course_statistics['upcoming'] }})</span>
                            </li>
                            <li>
                                <span class="info-list-color bg-pending"></span>
                                <span class="title2 fs-14px">Chờ duyệt ({{ $course_statistics['pending'] }})</span>
                            </li>
                            <li>
                                <span class="info-list-color bg-private"></span>
                                <span class="title2 fs-14px">Riêng tư ({{ $course_statistics['private'] }})</span>
                            </li>
                            <li>
                                <span class="info-list-color bg-draft"></span>
                                <span class="title2 fs-14px">Bản nháp ({{ $course_statistics['draft'] }})</span>
                            </li>
                            <li>
                                <span class="info-list-color bg-inactive"></span>
                                <span class="title2 fs-14px">Không hoạt động ({{ $course_statistics['inactive'] }})</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        @if(addon_check('my.affiliate'))
            <div class="col-md-7">
                <div class="pie-chart-container">
                    <div class="pie-chart-header">
                        <div>
                            <h4 class="pie-chart-title">Bảng xếp hạng Affiliate 🏆</h4>
                            <p class="chart-subtitle">Top cộng tác viên xuất sắc nhất</p>
                        </div>
                        <div class="toggle-view">
                            <button class="toggle-btn active" data-view="amount">Doanh thu</button>
                            <button class="toggle-btn" data-view="orders">Đơn hàng</button>
                        </div>
                    </div>

                        <div class="leaderboard-section amount-view">
                            <div class="leaderboard-header">
                                <h4 class="leaderboard-title">Top Doanh Thu</h4>
                            </div>

                            <div class="medal-container">
                                @if($top_affiliates_amount->count() > 0)
                                    <div class="medal-item">
                                        <img src="{{ asset('assets/backend/images/silver-medal.png') }}"
                                            alt="Silver Medal" class="medal-img">

                                        <div
                                            class="medal-username">{{ isset($top_affiliates_amount[1]->user) ? $top_affiliates_amount[1]->user->name : 'N/A' }}</div>
                                        <div
                                            class="medal-score">{{ isset($top_affiliates_amount[1]) ? number_format($top_affiliates_amount[1]->total_amount) . ' đ' : '0 đ' }}</div>
                                    </div>

                                    <div class="medal-item first-place">
                                        <img src="{{ asset('assets/backend/images/gold-medal.png') }}" alt="Gold Medal"
                                            class="medal-img gold">

                                        <div
                                            class="medal-username">{{ isset($top_affiliates_amount[0]->user) ? $top_affiliates_amount[0]->user->name : 'N/A' }}</div>
                                        <div
                                            class="medal-score">{{ isset($top_affiliates_amount[0]) ? number_format($top_affiliates_amount[0]->total_amount) . ' đ' : '0 đ' }}</div>
                                    </div>

                                    <div class="medal-item">
                                        <img src="{{ asset('assets/backend/images/bronze-medal.png') }}"
                                            alt="Bronze Medal" class="medal-img">

                                        <div
                                            class="medal-username">{{ isset($top_affiliates_amount[2]->user) ? $top_affiliates_amount[2]->user->name : 'N/A' }}</div>
                                        <div
                                            class="medal-score">{{ isset($top_affiliates_amount[2]) ? number_format($top_affiliates_amount[2]->total_amount) . ' đ' : '0 đ' }}</div>
                                    </div>
                                @else
                                    <p class="text-center">Không có dữ liệu cộng tác viên</p>
                                @endif
                            </div>

                            <div class="podium">
                                <div class="podium-step silver">2</div>
                                <div class="podium-step gold">1</div>
                                <div class="podium-step bronze">3</div>
                            </div>

                            <div class="leaderboard-list">
                                @for($i = 3; $i < $top_affiliates_amount->count(); $i++)
                                    <div class="leaderboard-list-item">
                                        <div class="d-flex align-items-center">
                                            <span class="rank">{{ $i + 1 }}</span>
                                            <span class="ms-3">{{ $top_affiliates_amount[$i]->user->name }}</span>
                                        </div>
                                        <div>{{ number_format($top_affiliates_amount[$i]->total_amount) . ' đ' }}</div>
                                    </div>
                                @endfor
                            </div>
                        </div>

                        <div class="leaderboard-section orders-view" style="display: none;">
                            <div class="leaderboard-header">
                                <h4 class="leaderboard-title">Top Đơn Hàng</h4>
                            </div>

                            <div class="medal-container">
                                @if($top_affiliates_orders->count() > 0)
                                    <div class="medal-item">
                                        <img src="{{ asset('assets/backend/images/silver-medal.png') }}"
                                            alt="Silver Medal" class="medal-img">

                                        <div
                                            class="medal-username">{{ isset($top_affiliates_orders[1]->user) ? $top_affiliates_orders[1]->user->name : 'N/A' }}</div>
                                        <div
                                            class="medal-score">{{ isset($top_affiliates_orders[1]) ? number_format($top_affiliates_orders[1]->total_orders) : '0' }}</div>
                                    </div>

                                    <div class="medal-item first-place">
                                        <img src="{{ asset('assets/backend/images/gold-medal.png') }}" alt="Gold Medal"
                                            class="medal-img gold">

                                        <div
                                            class="medal-username">{{ isset($top_affiliates_orders[0]->user) ? $top_affiliates_orders[0]->user->name : 'N/A' }}</div>
                                        <div
                                            class="medal-score">{{ isset($top_affiliates_orders[0]) ? number_format($top_affiliates_orders[0]->total_orders) : '0' }}</div>
                                    </div>

                                    <div class="medal-item">
                                        <img src="{{ asset('assets/backend/images/bronze-medal.png') }}"
                                            alt="Bronze Medal" class="medal-img">

                                        <div
                                            class="medal-username">{{ isset($top_affiliates_orders[2]->user) ? $top_affiliates_orders[2]->user->name : 'N/A' }}</div>
                                        <div
                                            class="medal-score">{{ isset($top_affiliates_orders[2]) ? number_format($top_affiliates_orders[2]->total_orders) : '0' }}</div>
                                    </div>
                                @else
                                    <p class="text-center">Không có dữ liệu cộng tác viên</p>
                                @endif
                            </div>

                            <div class="podium">
                                <div class="podium-step silver">2</div>
                                <div class="podium-step gold">1</div>
                                <div class="podium-step bronze">3</div>
                            </div>

                            <div class="leaderboard-list">
                                @for($i = 3; $i < $top_affiliates_orders->count(); $i++)
                                    <div class="leaderboard-list-item">
                                        <div class="d-flex align-items-center">
                                            <span class="rank">{{ $i + 1 }}</span>
                                            <span class="ms-3">{{ $top_affiliates_orders[$i]->user->name }}</span>
                                        </div>
                                        <div>{{ number_format($top_affiliates_orders[$i]->total_orders) }}</div>
                                    </div>
                                @endfor
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif
        <div class="col-md-12 mt-4">
            <div class="chart-container" id='unpaid-instructor-revenue'>
                <div class="chart-header">
                    <div>
                        <h4 class="chart-title">Yêu cầu rút tiền đang chờ</h4>
                        <p class="chart-subtitle">Danh sách các yêu cầu rút tiền cần được xử lý</p>
                    </div>
                    <div class="d-flex align-items-center gap-3">
                        <div class="badge bg-warning text-dark">
                            {{ $pending_withdraws->total() }} yêu cầu
                        </div>
                        @if($pending_withdraws->total() > 0)
                            <div class="badge bg-info text-white">
                                Tổng: {{ currency($pending_withdraws->sum('amount')) }}
                            </div>
                        @endif
                        <a class="btn btn-outline-primary btn-sm" href="{{route('admin.affiliate.withdraws')}}" data-bs-toggle="tooltip"
                           data-bs-placement="bottom" title="Xem tất cả thanh toán">
                            <i class="fi-rr-arrow-alt-right me-1"></i>
                            Xem tất cả
                        </a>
                    </div>
                </div>
                    <div class="table-responsive purchase_list mt-4" id="purchase_list">
                        <table class="table eTable eTable-2 print-table">
                            <thead>
                            <tr>
                                <th scope="col">#</th>
                                <th scope="col">{{ get_phrase('Name') }}</th>
                                <th scope="col">{{ get_phrase('Payout amount') }}</th>
                                <th scope="col">{{ get_phrase('Request date') }}</th>
                                <th scope="col">{{ get_phrase('Bank Info') }}</th>
                                <th scope="col" class="print-d-none">{{ get_phrase('Action') }}</th>
                            </tr>
                            </thead>
                            <tbody>
                            @if ($pending_withdraws->total() > 0)
                                @foreach ($pending_withdraws as $key => $pending_withdraw)
                                    <tr>
                                        <th scope="row">
                                            <p class="row-number">{{ ++$key }}</p>
                                        </th>
                                        <td>
                                            <div class="dAdmin_profile d-flex align-items-center min-w-200px">
                                                <div class="dAdmin_profile_img">
                                                    <img class="img-fluid rounded-circle image-45" width="45"
                                                         height="45"
                                                         src="{{ get_image($pending_withdraw->user->photo) }}"/>
                                                </div>
                                                <div class="ms-1">
                                                    <h4 class="title fs-14px">
                                                        {{ $pending_withdraw->user->name }}
                                                    </h4>
                                                    <p class="sub-title2 text-12px">
                                                        {{ $pending_withdraw->user->email }}
                                                    </p>
                                                </div>
                                            </div>
                                        </td>

                                        <td>
                                            <div class="dAdmin_info_name">
                                                <p>{{ currency($pending_withdraw->amount) }}</p>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="dAdmin_info_name">
                                                <p>
                                                    @if(is_string($pending_withdraw->request_date))
                                                        {{ $pending_withdraw->request_date }}
                                                    @elseif($pending_withdraw->request_date instanceof \DateTime || $pending_withdraw->request_date instanceof \Carbon\Carbon)
                                                        {{ $pending_withdraw->request_date->format('d/m/Y') }}
                                                    @else
                                                        N/A
                                                    @endif
                                                </p>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="dAdmin_info_name">
                                                @if(!empty($pending_withdraw->note))
                                                    <small>{!! nl2br(e($pending_withdraw->note)) !!}</small>
                                                @else
                                                    <small class="text-muted">{{ get_phrase('No bank info') }}</small>
                                                @endif
                                            </div>
                                        </td>

                                        <td class="print-d-none">
                                            <div class="d-flex gap-2">
                                                <form
                                                    action="{{ route('admin.affiliate.withdraw.status', [$pending_withdraw->id, 1]) }}"
                                                    method="post">
                                                    @csrf
                                                    <button type="submit" class="btn btn-success btn-sm">
                                                        <i class="fi-rr-check"></i>
                                                        {{ get_phrase('Completed') }}</button>
                                                </form>
                                                <form
                                                    action="{{ route('admin.affiliate.withdraw.status', [$pending_withdraw->id, 2]) }}"
                                                    method="post">
                                                    @csrf
                                                    <button type="submit" class="btn btn-danger btn-sm">
                                                        <i class="fi-rr-ban"></i>
                                                        {{ get_phrase('Canceled') }}</button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            @else
                                <tr>
                                    <th colspan="6" class="text-center">
                                        <div class="alert alert-info">
                                            <i class="fi-rr-info me-2"></i>
                                            Hiện tại không có yêu cầu rút tiền nào đang chờ xử lý.
                                        </div>
                                    </th>
                                </tr>

                            @endif
                            <tr>
                                <th></th>
                                <th></th>
                                <th>{{ get_phrase('Total') }} :
                                    {{ currency($pending_withdraws->sum('amount')) }}
                                </th>
                                <th></th>
                                <th></th>
                                <th></th>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @php
        $active = $course_statistics['active'];
        $upcoming = $course_statistics['upcoming'];
        $pending = $course_statistics['pending'];
        $private = $course_statistics['private'];
        $draft = $course_statistics['draft'];
        $inactive = $course_statistics['inactive'];
    @endphp
@endsection

@push('js')

    {{-- Oliv template start --}}
    <script src="{{ asset('assets/backend/vendors/apexcharts/apexcharts.min.js') }}"></script>
    <script src="{{ asset('assets/backend/vendors/chart-js/chart.js') }}"></script>
    {{-- Oliv template end --}}

    {{-- Modern Dashboard Enhancements --}}
    <script src="{{ asset('assets/backend/js/dashboard-modern.js') }}"></script>


    <script>
        "use strict";

        // Function to refresh storage cache
        function refreshStorageCache() {
            const button = event.target.closest('button');
            const originalHtml = button.innerHTML;

            // Show loading state
            button.innerHTML = '<i class="fi-rr-spinner"></i>';
            button.disabled = true;

            fetch('{{ route("admin.dashboard.clear.storage.cache") }}')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Reload page to show updated data
                        window.location.reload();
                    } else {
                        alert('Có lỗi: ' + data.message);
                        button.innerHTML = originalHtml;
                        button.disabled = false;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Có lỗi khi làm mới cache');
                    button.innerHTML = originalHtml;
                    button.disabled = false;
                });
        }

        // Format số tiền với dấu phân cách hàng nghìn
        const formatMoney = (value) => {
            return new Intl.NumberFormat('vi-VN', {
                style: 'decimal',
                maximumFractionDigits: 0
            }).format(value);
        };

        // Lấy dữ liệu từ controller
        const chartData = <?php echo json_encode($monthly_amount); ?>;

        // Định nghĩa các màu sắc hiện đại cho các loại doanh thu
        const chartColors = {
            total: {
                backgroundColor: "rgba(102, 126, 234, 0.8)",
                borderColor: "#667eea",
                indicatorColor: "#667eea",
                gradient: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
            },
            affiliate: {
                backgroundColor: "rgba(240, 147, 251, 0.8)",
                borderColor: "#f093fb",
                indicatorColor: "#f093fb",
                gradient: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)"
            },
            actual: {
                backgroundColor: "rgba(79, 172, 254, 0.8)",
                borderColor: "#4facfe",
                indicatorColor: "#4facfe",
                gradient: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)"
            }
        };

        // Định nghĩa external tooltip
        const getOrCreateTooltip = (chart) => {
            let tooltipEl = chart.canvas.parentNode.querySelector('div.custom-tooltip');

            if (!tooltipEl) {
                tooltipEl = document.createElement('div');
                tooltipEl.className = 'custom-tooltip';
                tooltipEl.style.opacity = 0;
                tooltipEl.style.pointerEvents = 'none';
                tooltipEl.style.position = 'absolute';
                tooltipEl.style.transform = 'translate(-50%, 0)';
                tooltipEl.style.transition = 'all .1s ease';
                tooltipEl.style.zIndex = 1000;

                const canvas = chart.canvas;
                canvas.parentNode.appendChild(tooltipEl);
            }

            return tooltipEl;
        };

        // Tạo biểu đồ với Chart.js
        const revenueChart = new Chart("myChart", {
            type: "bar",
            data: {
                labels: chartData.labels,
                datasets: [
                    {
                        label: "Tổng doanh thu",
                        backgroundColor: chartColors.total.backgroundColor,
                        borderColor: chartColors.total.borderColor,
                        borderWidth: 1,
                        data: chartData.total_revenue
                    },
                    {
                        label: "Hoa hồng affiliate",
                        backgroundColor: chartColors.affiliate.backgroundColor,
                        borderColor: chartColors.affiliate.borderColor,
                        borderWidth: 1,
                        data: chartData.affiliate_amount
                    },
                    {
                        label: "Doanh thu thực tế",
                        backgroundColor: chartColors.actual.backgroundColor,
                        borderColor: chartColors.actual.borderColor,
                        borderWidth: 1,
                        data: chartData.actual_revenue
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            pointStyle: 'circle',
                            padding: 20,
                            font: {
                                size: 12,
                                weight: '500'
                            }
                        }
                    },
                    tooltip: {
                        enabled: false,
                        external: function(context) {
                            const {chart, tooltip} = context;
                            const tooltipEl = getOrCreateTooltip(chart);

                            // Ẩn tooltip nếu không có dữ liệu
                            if (tooltip.opacity === 0) {
                                tooltipEl.style.opacity = 0;
                                return;
                            }

                            // Lấy dữ liệu từ tooltip
                            if (tooltip.body) {
                                const titleLines = tooltip.title || [];
                                const bodyLines = tooltip.body.map(b => b.lines);

                                // Lấy index của tháng đang được hover
                                const dataIndex = context.tooltip.dataPoints[0].dataIndex;

                                // Lấy dữ liệu doanh thu cho tháng đó
                                const totalRevenue = chartData.total_revenue[dataIndex];
                                const affiliateAmount = chartData.affiliate_amount[dataIndex];
                                const actualRevenue = chartData.actual_revenue[dataIndex];

                                // Tính tỷ lệ hoa hồng
                                const percentage = totalRevenue > 0
                                    ? (affiliateAmount / totalRevenue * 100).toFixed(1)
                                    : 0;

                                // Tạo HTML cho tooltip
                                const tooltipHTML = `
                                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 16px 20px; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.3); max-width: 280px; width: auto; text-align: left; backdrop-filter: blur(10px);">
                                        <div style="font-weight: 700; font-size: 16px; padding-bottom: 12px; border-bottom: 1px solid rgba(255,255,255,0.2); margin-bottom: 12px;">
                                            📊 ${titleLines[0]}
                                        </div>
                                        <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                            <div style="width: 12px; height: 12px; border-radius: 50%; background: ${chartColors.total.indicatorColor}; margin-right: 10px; box-shadow: 0 0 0 2px rgba(255,255,255,0.3);"></div>
                                            <div style="flex: 1;">
                                                <div style="font-size: 12px; opacity: 0.8;">Tổng doanh thu</div>
                                                <div style="font-weight: 700; font-size: 14px;">${formatMoney(totalRevenue)} đ</div>
                                            </div>
                                        </div>
                                        <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                            <div style="width: 12px; height: 12px; border-radius: 50%; background: ${chartColors.affiliate.indicatorColor}; margin-right: 10px; box-shadow: 0 0 0 2px rgba(255,255,255,0.3);"></div>
                                            <div style="flex: 1;">
                                                <div style="font-size: 12px; opacity: 0.8;">Hoa hồng affiliate</div>
                                                <div style="font-weight: 700; font-size: 14px;">${formatMoney(affiliateAmount)} đ</div>
                                            </div>
                                        </div>
                                        <div style="display: flex; align-items: center; margin-bottom: 12px;">
                                            <div style="width: 12px; height: 12px; border-radius: 50%; background: ${chartColors.actual.indicatorColor}; margin-right: 10px; box-shadow: 0 0 0 2px rgba(255,255,255,0.3);"></div>
                                            <div style="flex: 1;">
                                                <div style="font-size: 12px; opacity: 0.8;">Doanh thu thực tế</div>
                                                <div style="font-weight: 700; font-size: 14px;">${formatMoney(actualRevenue)} đ</div>
                                            </div>
                                        </div>
                                        <div style="margin-top: 12px; padding-top: 12px; border-top: 1px solid rgba(255,255,255,0.2); text-align: center;">
                                            <div style="font-size: 12px; opacity: 0.8;">Tỷ lệ hoa hồng</div>
                                            <div style="font-weight: 700; font-size: 16px; color: #ffd700;">${percentage}%</div>
                                        </div>
                                    </div>
                                `;

                                tooltipEl.innerHTML = tooltipHTML;
                            }

                            // Cập nhật vị trí tooltip
                            const position = context.chart.canvas.getBoundingClientRect();
                            tooltipEl.style.opacity = 1;

                            // Đặt cố định ở trên cùng giữa biểu đồ
                            tooltipEl.style.left = position.left + position.width / 2 + 'px';
                            tooltipEl.style.top = position.top + 20 + 'px';
                            tooltipEl.style.transform = 'translate(-50%, 0)';
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return formatMoney(value) + ' đ';
                            },
                            font: {
                                size: 11,
                                weight: '500'
                            },
                            color: '#718096'
                        },
                        grid: {
                            color: 'rgba(113, 128, 150, 0.1)',
                            drawBorder: false
                        },
                        border: {
                            display: false
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                size: 11,
                                weight: '500'
                            },
                            color: '#718096'
                        },
                        grid: {
                            display: false
                        },
                        border: {
                            display: false
                        }
                    }
                },
                // Chỉ hiển thị một tooltip khi hover
                interaction: {
                    mode: 'index',
                    intersect: false
                },
                elements: {
                    bar: {
                        borderRadius: 8,
                        borderSkipped: false,
                    }
                },
                layout: {
                    padding: {
                        top: 20,
                        bottom: 20
                    }
                }
            }
        });


        // Pie Chart 2
        const project_progress2 = document.getElementById('pie2');
        const progressData2 = {
            labels: ['Active', 'Upcoming', 'Pending', 'Private', 'Draft', 'Inactive'],
            data: [{{$active}}, {{$upcoming}}, {{$pending}}, {{$private}}, {{$draft}}, {{$inactive}}],
        };
        var barColors = [
            "#12c093",  // Active - Green
            "#1b84ff",  // Upcoming - Blue
            "#ff2583",  // Pending - Pink
            "#6c757d",  // Private - Gray
            "#ffc107",  // Draft - Yellow
            "#dc3545",  // Inactive - Red
        ];

        new Chart(project_progress2, {
            type: 'doughnut',
            data: {
                labels: progressData2.labels,
                datasets: [{
                    backgroundColor: barColors,
                    label: ' Khóa học',
                    data: progressData2.data,
                    borderWidth: 3,
                    borderColor: '#ffffff',
                    hoverBorderWidth: 4,
                    hoverBorderColor: '#ffffff',
                    hoverBackgroundColor: barColors.map(color => color + 'dd'), // Add transparency on hover
                },],
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                cutout: '65%', // Makes the doughnut thicker
                plugins: {
                    legend: {
                        display: false,
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#ffffff',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: true,
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                },
                elements: {
                    arc: {
                        borderJoinStyle: 'round'
                    }
                },
                animation: {
                    animateRotate: true,
                    animateScale: true,
                    duration: 1000,
                    easing: 'easeOutQuart'
                }
            },
        });

        // Toggle between amount and orders view
        document.addEventListener('DOMContentLoaded', function () {
            const toggleBtns = document.querySelectorAll('.toggle-btn');
            const amountView = document.querySelector('.amount-view');
            const ordersView = document.querySelector('.orders-view');

            toggleBtns.forEach(btn => {
                btn.addEventListener('click', function () {
                    // Remove active class from all buttons
                    toggleBtns.forEach(b => b.classList.remove('active'));
                    // Add active class to clicked button
                    this.classList.add('active');

                    // Toggle views
                    if (this.dataset.view === 'amount') {
                        amountView.style.display = 'block';
                        ordersView.style.display = 'none';
                    } else {
                        amountView.style.display = 'none';
                        ordersView.style.display = 'block';
                    }
                });
            });
        });

        // Kiểm tra và hiển thị popup hết hạn
        @if($system_expired)
            $(document).ready(function() {
                showSystemExpiredModal();
            });
        @else
            // Force check nếu helper function báo expired nhưng session chưa có
            @if(is_system_expired())
                $(document).ready(function() {
                    console.log('Force showing expired modal - helper function detected expiry');
                    showSystemExpiredModal();
                    // Update session via AJAX
                    $.post('{{ route("admin.dashboard") }}', {
                        _token: '{{ csrf_token() }}',
                        force_update_session: true
                    });
                });
            @endif
        @endif

        function showSystemExpiredModal() {
            const modal = `
                <div class="modal fade" id="systemExpiredModal" tabindex="-1" aria-labelledby="systemExpiredModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content border-0 shadow-lg">
                            <div class="modal-header bg-danger text-white border-0">
                                <h5 class="modal-title" id="systemExpiredModalLabel">
                                    <i class="fi-rr-triangle-warning me-2"></i>
                                    Hệ thống đã hết hạn
                                </h5>
                            </div>
                            <div class="modal-body text-center py-4">
                                <div class="mb-3">
                                    <i class="fi-rr-calendar-exclamation text-danger" style="font-size: 48px;"></i>
                                </div>
                                <h6 class="mb-3">Hệ thống đã hết hạn vào ngày {{ $expiry_date }}</h6>
                                <p class="text-muted mb-4">
                                    Vui lòng liên hệ với nhà cung cấp để gia hạn hệ thống.
                                    Trong thời gian này, bạn chỉ có thể xem dashboard và không thể thực hiện các thao tác khác.
                                </p>
                                <div class="d-flex gap-2 justify-content-center">
                                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                                        <i class="fi-rr-eye me-1"></i>
                                        Xem dashboard
                                    </button>
                                    <a href="{{ route('logout') }}" class="btn btn-danger">
                                        <i class="fi-rr-sign-out me-1"></i>
                                        Đăng xuất
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('body').append(modal);
            $('#systemExpiredModal').modal('show');

            // Chặn tất cả các link và form khi hết hạn
            $('a:not([href="#"]):not([href*="logout"]):not([data-bs-dismiss])').on('click', function(e) {
                e.preventDefault();
                alert('Hệ thống đã hết hạn. Vui lòng gia hạn để tiếp tục sử dụng.');
            });

            $('form:not([action*="logout"])').on('submit', function(e) {
                e.preventDefault();
                alert('Hệ thống đã hết hạn. Vui lòng gia hạn để tiếp tục sử dụng.');
            });

            // Chặn tất cả AJAX requests khi hết hạn
            $(document).ajaxComplete(function(event, xhr, settings) {
                if (xhr.status === 403) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.system_expired) {
                            alert(response.message);
                        }
                    } catch (e) {
                        // Ignore parsing errors
                    }
                }
            });
        }
    </script>
@endpush
