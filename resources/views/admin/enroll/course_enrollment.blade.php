@extends('layouts.admin')
@push('title', '<PERSON><PERSON><PERSON> ký học viên')
@push('meta')@endpush
@push('css')
<style>
.enrollment-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    border: 1px solid #e8ecef;
}
.form-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}
.duration-option {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}
.duration-option:hover {
    border-color: #007bff;
    background-color: #f8f9ff;
}
.duration-option.selected {
    border-color: #007bff;
    background-color: #e7f3ff;
}
.duration-option input[type="radio"] {
    display: none;
}
.duration-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}
.duration-desc {
    font-size: 12px;
    color: #6c757d;
}
.enrolled-badge {
    background-color: #fff3cd;
    color: #856404;
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 4px;
    margin-left: 5px;
}
.select-disabled {
    opacity: 0.6;
    pointer-events: none;
}
.spin {
    animation: spin 1s linear infinite;
}
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
</style>
@endpush
@section('content')
    @php
        $course = App\Models\Course::where('status', 'active')->orWhere('status', 'private')->orderBy('title', 'asc')->get();
    @endphp

    <div class="ol-card radius-8px">
        <div class="ol-card-body my-3 py-4 px-20px">
            <div class="d-flex align-items-center justify-content-between gap-3 flex-wrap flex-md-nowrap">
                <h4 class="title fs-16px">
                    <i class="fi-rr-user-add me-2"></i>
                    Đăng ký học viên vào khóa học
                </h4>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="enrollment-card p-4">
                <div class="text-center mb-4">
                    <h3 class="title fs-18px mb-2">Đăng ký học viên</h3>
                    <p class="text-muted">Thêm học viên vào khóa học với thời gian học tùy chọn</p>
                </div>

                <form action="{{ route('admin.student.post') }}" method="post" enctype="multipart/form-data">
                    @csrf

                    <!-- Chọn khóa học -->
                    <div class="form-section">
                        <h5 class="mb-3"><i class="fi-rr-book me-2"></i>Thông tin khóa học</h5>
                        <div class="mb-3">
                            <label class="form-label ol-form-label" for="course_id">Khóa học <span class="text-danger">*</span></label>
                            <select class="ol-select2 form-control" name="course_id" id="course_id" required onchange="fetchAvailableStudents()">
                                <option value="">Chọn khóa học...</option>
                                @foreach ($course as $row)
                                    <option value="{{ $row->id }}" data-expiry="{{ $row->expiry_period }}">{{ $row->title }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <!-- Chọn học viên -->
                    <div class="form-section">
                        <h5 class="mb-3"><i class="fi-rr-users me-2"></i>Chọn học viên</h5>
                        <div class="mb-3">
                            <label class="form-label ol-form-label" for="multiple_user_id">
                                Chọn học viên <span class="text-danger">*</span>
                                <span id="student_loading" style="display: none;" class="text-primary">
                                    <i class="fi-rr-spinner spin me-1"></i>Đang tải...
                                </span>
                            </label>
                            <select class="ol-select2" name="user_id[]" id="multiple_user_id" multiple="multiple" required disabled>
                                <option value="">Vui lòng chọn khóa học trước</option>
                            </select>
                            <small class="text-muted" id="student_help_text">
                                <i class="fi-rr-info me-1"></i>
                                <span id="help_default">Vui lòng chọn khóa học trước để hiển thị danh sách học viên</span>
                                <span id="help_active" style="display: none;">
                                    Có thể chọn nhiều học viên. Gõ tên hoặc email để tìm kiếm.
                                    <span class="text-warning">⚠️ Học viên đã đăng ký sẽ được ghi đè</span>
                                </span>
                            </small>
                        </div>
                    </div>

                    <!-- Chọn thời gian -->
                    <div class="form-section">
                        <h5 class="mb-3"><i class="fi-rr-calendar me-2"></i>Thời gian học</h5>
                        <div class="row g-3 mb-3">
                            <div class="col-md-4">
                                <div class="duration-option" onclick="selectDuration('lifetime')">
                                    <input type="radio" name="duration_type" value="lifetime" id="lifetime">
                                    <div class="duration-label">Trọn đời</div>
                                    <div class="duration-desc">Không giới hạn thời gian</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="duration-option" onclick="selectDuration('months')">
                                    <input type="radio" name="duration_type" value="months" id="months">
                                    <div class="duration-label">Theo tháng</div>
                                    <div class="duration-desc">Tùy chọn số tháng</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="duration-option" onclick="selectDuration('course_default')">
                                    <input type="radio" name="duration_type" value="course_default" id="course_default">
                                    <div class="duration-label">Mặc định khóa học</div>
                                    <div class="duration-desc" id="course_default_desc">Theo cài đặt khóa học</div>
                                </div>
                            </div>
                        </div>

                        <div id="months_input" style="display: none;">
                            <label class="form-label">Số tháng</label>
                            <input type="number" class="form-control" name="duration_months" min="1" max="60" placeholder="Nhập số tháng">
                        </div>
                    </div>

                    <!-- Số tiền -->
                    <div class="form-section">
                        <h5 class="mb-3"><i class="fi-rr-money me-2"></i>Thông tin thanh toán</h5>
                        <div class="mb-3">
                            <label class="form-label ol-form-label" for="amount_received">Số tiền đã thu <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="number" class="form-control ol-form-control" name="amount_received" id="amount_received"
                                       placeholder="0" min="0" step="1000" required>
                                <span class="input-group-text">VNĐ</span>
                            </div>
                            <small class="text-muted">Nhập số tiền đã thu từ khách hàng</small>
                        </div>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn ol-btn-primary px-5 py-2">
                            <i class="fi-rr-check me-2"></i>Đăng ký học viên
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script type="text/javascript">
        function fetchAvailableStudents() {
            var courseId = $('#course_id').val();
            var selectedOption = $('#course_id option:selected');
            var expiryPeriod = selectedOption.data('expiry');

            // Cập nhật mô tả mặc định khóa học
            if (expiryPeriod && expiryPeriod > 0) {
                $('#course_default_desc').text(expiryPeriod + ' tháng');
            } else {
                $('#course_default_desc').text('Trọn đời');
            }

            if (!courseId) {
                // Disable student selection và reset
                $('#multiple_user_id').prop('disabled', true).html('<option value="">Vui lòng chọn khóa học trước</option>');
                $('#help_default').show();
                $('#help_active').hide();
                $('#student_loading').hide();
                return;
            }

            // Enable student selection và load data
            $('#student_loading').show();
            $('#help_default').hide();
            loadStudents(courseId, '');
        }

        function loadStudents(courseId, search = '') {
            $.ajax({
                url: "{{ route('admin.available.students') }}",
                type: "POST",
                data: {
                    course_ids: [courseId],
                    search: search,
                    _token: "{{ csrf_token() }}"
                },
                success: function(response) {
                    // Enable student selection
                    $('#multiple_user_id').prop('disabled', false);
                    $('#student_loading').hide();
                    $('#help_active').show();

                    $('#multiple_user_id').html('');
                    if (response.students.length > 0) {
                        $.each(response.students, function(key, student) {
                            var enrolledBadge = student.is_enrolled ? ' <span class="badge bg-warning text-dark">Đã đăng ký</span>' : '';
                            var optionText = student.name + ' (' + student.email + ')' + (student.is_enrolled ? ' - Đã đăng ký' : '');
                            $('#multiple_user_id').append('<option value="' + student.id + '">' + optionText + '</option>');
                        });
                    } else {
                        var noResultText = search ? 'Không tìm thấy học viên với từ khóa "' + search + '"' : 'Không tìm thấy học viên nào';
                        $('#multiple_user_id').html('<option value="">' + noResultText + '</option>');
                    }

                    // Destroy and reinitialize Select2 with search
                    $('#multiple_user_id').select2('destroy').select2({
                        placeholder: "Tìm kiếm học viên theo tên hoặc email...",
                        allowClear: true,
                        disabled: false,
                        ajax: {
                            url: "{{ route('admin.available.students') }}",
                            type: "POST",
                            dataType: 'json',
                            delay: 250,
                            data: function (params) {
                                return {
                                    course_ids: [courseId],
                                    search: params.term,
                                    _token: "{{ csrf_token() }}"
                                };
                            },
                            processResults: function (data) {
                                return {
                                    results: data.students.map(function(student) {
                                        var text = student.name + ' (' + student.email + ')';
                                        if (student.is_enrolled) {
                                            text += ' - Đã đăng ký';
                                        }
                                        return {
                                            id: student.id,
                                            text: text
                                        };
                                    })
                                };
                            },
                            cache: true
                        },
                        minimumInputLength: 0
                    });
                },
                error: function() {
                    $('#student_loading').hide();
                    $('#multiple_user_id').html('<option value="">Lỗi khi tải danh sách học viên</option>');
                }
            });
        }

        function selectDuration(type) {
            // Remove selected class from all options
            $('.duration-option').removeClass('selected');

            // Add selected class to clicked option
            $('input[name="duration_type"][value="' + type + '"]').closest('.duration-option').addClass('selected');

            // Check the radio button
            $('input[name="duration_type"][value="' + type + '"]').prop('checked', true);

            // Show/hide months input
            if (type === 'months') {
                $('#months_input').show();
                $('input[name="duration_months"]').attr('required', true);
            } else {
                $('#months_input').hide();
                $('input[name="duration_months"]').removeAttr('required');
            }
        }

        $(document).ready(function() {
            // Format number input for amount
            $('#amount_received').on('input', function() {
                let value = $(this).val().replace(/[^0-9]/g, '');
                if (value) {
                    let formatted = new Intl.NumberFormat('vi-VN').format(value);
                    $(this).attr('title', formatted + ' VNĐ');
                }
            });

            // Form validation
            $('form').on('submit', function(e) {
                let courseId = $('#course_id').val();
                let userIds = $('#multiple_user_id').val();
                let amount = $('#amount_received').val();
                let durationType = $('input[name="duration_type"]:checked').val();

                // Kiểm tra khóa học
                if (!courseId) {
                    e.preventDefault();
                    alert('Vui lòng chọn khóa học trước');
                    $('#course_id').focus();
                    return false;
                }

                // Kiểm tra học viên
                if (!userIds || userIds.length === 0) {
                    e.preventDefault();
                    alert('Vui lòng chọn ít nhất một học viên');
                    $('#multiple_user_id').focus();
                    return false;
                }

                // Kiểm tra số tiền
                if (!amount || amount <= 0) {
                    e.preventDefault();
                    alert('Vui lòng nhập số tiền đã thu hợp lệ');
                    $('#amount_received').focus();
                    return false;
                }

                // Kiểm tra thời gian học
                if (!durationType) {
                    e.preventDefault();
                    alert('Vui lòng chọn thời gian học');
                    return false;
                }

                // Kiểm tra số tháng nếu chọn "theo tháng"
                if (durationType === 'months') {
                    let months = $('input[name="duration_months"]').val();
                    if (!months || months <= 0) {
                        e.preventDefault();
                        alert('Vui lòng nhập số tháng hợp lệ');
                        $('input[name="duration_months"]').focus();
                        return false;
                    }
                }

                // Hiển thị loading state
                $(this).find('button[type="submit"]').prop('disabled', true).html('<i class="fi-rr-spinner spin me-2"></i>Đang xử lý...');
            });

            // Initialize Select2 for course selection
            $('#course_id').select2({
                placeholder: "Chọn khóa học...",
                allowClear: true
            });

            // Initialize disabled student selection
            $('#multiple_user_id').select2({
                placeholder: "Vui lòng chọn khóa học trước",
                disabled: true
            });

            // Đảm bảo trạng thái ban đầu đúng
            $('#multiple_user_id').prop('disabled', true);
            $('#help_default').show();
            $('#help_active').hide();
            $('#student_loading').hide();
        });
    </script>
@endsection
