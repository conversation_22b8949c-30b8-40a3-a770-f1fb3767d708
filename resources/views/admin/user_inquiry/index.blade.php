@extends('layouts.admin')
@push('title', '<PERSON><PERSON> nhận thông tin người dùng')

@section('content')
    <div class="ol-card radius-8px">
        <div class="ol-card-body my-3 py-4 px-20px">
            <div class="d-flex align-items-center justify-content-between gap-3 flex-wrap flex-md-nowrap">
                <h4 class="title fs-16px">
                    <i class="fi-rr-settings-sliders me-2"></i>
                    <PERSON><PERSON> nhận thông tin người dùng
                </h4>
                
                <div class="d-flex align-items-center gap-3">
                    <!-- Search Form -->
                    <form action="{{ route('admin.user.inquiries.index') }}" method="GET" class="d-flex">
                        <div class="input-group">
                            <input type="text" name="search" class="form-control" placeholder="Tìm kiếm..." 
                                   value="{{ request('search') }}">
                            <button class="btn ol-btn-outline-secondary" type="submit">
                                <i class="fi-rr-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="ol-card">
        <div class="ol-card-body p-20px mb-3">
            @if($inquiries->count() > 0)
                <div class="admin-tInfo-pagi d-flex justify-content-between justify-content-center align-items-center flex-wrap">
                    <p class="admin-tInfo">
                        Hiển thị {{ $inquiries->firstItem() }}-{{ $inquiries->lastItem() }} 
                        trong tổng số {{ $inquiries->total() }} kết quả
                    </p>
                </div>

                <div class="table-responsive course_list" id="course_list">
                    <table class="table eTable eTable-2">
                        <thead>
                            <tr>
                                <th scope="col">#</th>
                                <th scope="col">Họ và tên</th>
                                <th scope="col">Số điện thoại</th>
                                <th scope="col">Email</th>
                                <th scope="col">Ghi chú</th>
                                <th scope="col">Thời gian</th>
                                <th scope="col">Trạng thái</th>
                                <th scope="col">Hành động</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($inquiries as $inquiry)
                                <tr>
                                    <td>{{ $loop->iteration + ($inquiries->currentPage() - 1) * $inquiries->perPage() }}</td>
                                    <td>
                                        <div class="dAdmin_profile d-flex align-items-center min-w-200px">
                                            <div class="dAdmin_profile_name">
                                                <h4 class="title fs-14px">{{ $inquiry->name }}</h4>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="dAdmin_info_name min-w-150px">
                                            <p>{{ $inquiry->phone }}</p>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="dAdmin_info_name min-w-150px">
                                            <p>{{ $inquiry->email ?: 'Không có' }}</p>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="dAdmin_info_name min-w-200px">
                                            <p>{{ Str::limit($inquiry->note, 50) }}</p>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="dAdmin_info_name min-w-150px">
                                            <p>{{ $inquiry->created_at->format('d/m/Y H:i') }}</p>
                                        </div>
                                    </td>
                                    <td>
                                        @if($inquiry->is_read)
                                            <span class="badge bg-success">Đã đọc</span>
                                        @else
                                            <span class="badge bg-warning">Chưa đọc</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="dropdown ol-icon-dropdown ol-icon-dropdown-transparent">
                                            <button class="btn ol-btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                <span class="fi-rr-menu-dots-vertical"></span>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item" href="{{ route('admin.user.inquiries.show', $inquiry->id) }}">
                                                        <span class="fi-rr-eye"></span> Xem chi tiết
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="javascript:void(0);" 
                                                       onclick="confirmModal('{{ route('admin.user.inquiries.delete', $inquiry->id) }}', 'DELETE')">
                                                        <span class="fi-rr-trash"></span> Xóa
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="admin-tInfo-pagi d-flex justify-content-between justify-content-center align-items-center flex-wrap">
                    <p class="admin-tInfo">
                        Hiển thị {{ $inquiries->firstItem() }}-{{ $inquiries->lastItem() }} 
                        trong tổng số {{ $inquiries->total() }} kết quả
                    </p>
                    {{ $inquiries->appends(request()->query())->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <img src="{{ asset('assets/backend/images/empty-box.png') }}" alt="" class="mb-3" style="width: 100px;">
                    <h4>Chưa có thông tin nào</h4>
                    <p class="text-muted">Chưa có người dùng nào gửi thông tin từ landing page</p>
                </div>
            @endif
        </div>
    </div>
@endsection

@push('js')
<script>
    function confirmModal(url, method = 'GET') {
        if (confirm('Bạn có chắc chắn muốn thực hiện hành động này?')) {
            if (method === 'DELETE') {
                // Create a form for DELETE request
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = url;
                
                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = '{{ csrf_token() }}';
                
                const methodField = document.createElement('input');
                methodField.type = 'hidden';
                methodField.name = '_method';
                methodField.value = 'DELETE';
                
                form.appendChild(csrfToken);
                form.appendChild(methodField);
                document.body.appendChild(form);
                form.submit();
            } else {
                window.location.href = url;
            }
        }
    }
</script>
@endpush
