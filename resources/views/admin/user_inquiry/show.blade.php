@extends('layouts.admin')
@push('title', 'Chi tiết thông tin người dùng')

@section('content')
    <div class="ol-card radius-8px">
        <div class="ol-card-body my-3 py-4 px-20px">
            <div class="d-flex align-items-center justify-content-between gap-3 flex-wrap flex-md-nowrap">
                <h4 class="title fs-16px">
                    <i class="fi-rr-eye me-2"></i>
                    Chi tiết thông tin người dùng
                </h4>
                
                <div class="d-flex align-items-center gap-3">
                    <a href="{{ route('admin.user.inquiries.index') }}" class="btn ol-btn-outline-secondary">
                        <i class="fi-rr-arrow-left me-2"></i>
                        Quay lại
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="ol-card">
                <div class="ol-card-body p-20px">
                    <h5 class="mb-4">Thông tin chi tiết</h5>
                    
                    <div class="row mb-3">
                        <div class="col-sm-3">
                            <strong>Họ và tên:</strong>
                        </div>
                        <div class="col-sm-9">
                            {{ $inquiry->name }}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-sm-3">
                            <strong>Số điện thoại:</strong>
                        </div>
                        <div class="col-sm-9">
                            <a href="tel:{{ $inquiry->phone }}" class="text-decoration-none">
                                {{ $inquiry->phone }}
                            </a>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-sm-3">
                            <strong>Email:</strong>
                        </div>
                        <div class="col-sm-9">
                            @if($inquiry->email)
                                <a href="mailto:{{ $inquiry->email }}" class="text-decoration-none">
                                    {{ $inquiry->email }}
                                </a>
                            @else
                                <span class="text-muted">Không có</span>
                            @endif
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-sm-3">
                            <strong>Ghi chú:</strong>
                        </div>
                        <div class="col-sm-9">
                            @if($inquiry->note)
                                <div class="bg-light p-3 rounded">
                                    {!! nl2br(e($inquiry->note)) !!}
                                </div>
                            @else
                                <span class="text-muted">Không có ghi chú</span>
                            @endif
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-sm-3">
                            <strong>Thời gian gửi:</strong>
                        </div>
                        <div class="col-sm-9">
                            {{ $inquiry->created_at->format('d/m/Y H:i:s') }}
                            <small class="text-muted">({{ $inquiry->created_at->diffForHumans() }})</small>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-sm-3">
                            <strong>Trạng thái:</strong>
                        </div>
                        <div class="col-sm-9">
                            @if($inquiry->is_read)
                                <span class="badge bg-success">Đã đọc</span>
                            @else
                                <span class="badge bg-warning">Chưa đọc</span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="ol-card">
                <div class="ol-card-body p-20px">
                    <h5 class="mb-4">Thông tin kỹ thuật</h5>
                    
                    <div class="mb-3">
                        <strong>IP Address:</strong><br>
                        <code>{{ $inquiry->ip_address ?: 'Không có' }}</code>
                    </div>

                    <div class="mb-3">
                        <strong>User Agent:</strong><br>
                        <small class="text-muted">{{ Str::limit($inquiry->user_agent, 100) ?: 'Không có' }}</small>
                    </div>

                    @if($inquiry->last_submission)
                        <div class="mb-3">
                            <strong>Lần gửi cuối:</strong><br>
                            {{ $inquiry->last_submission->format('d/m/Y H:i:s') }}
                        </div>
                    @endif
                </div>
            </div>

            <div class="ol-card mt-3">
                <div class="ol-card-body p-20px">
                    <h5 class="mb-4">Hành động</h5>
                    
                    <div class="d-grid gap-2">
                        @if($inquiry->phone)
                            <a href="tel:{{ $inquiry->phone }}" class="btn ol-btn-primary">
                                <i class="fi-rr-phone-call me-2"></i>
                                Gọi điện
                            </a>
                        @endif

                        @if($inquiry->email)
                            <a href="mailto:{{ $inquiry->email }}" class="btn ol-btn-outline-primary">
                                <i class="fi-rr-envelope me-2"></i>
                                Gửi email
                            </a>
                        @endif

                        <button class="btn ol-btn-outline-danger" 
                                onclick="confirmModal('{{ route('admin.user.inquiries.delete', $inquiry->id) }}', 'DELETE')">
                            <i class="fi-rr-trash me-2"></i>
                            Xóa thông tin
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
<script>
    function confirmModal(url, method = 'GET') {
        if (confirm('Bạn có chắc chắn muốn thực hiện hành động này?')) {
            if (method === 'DELETE') {
                // Create a form for DELETE request
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = url;
                
                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = '{{ csrf_token() }}';
                
                const methodField = document.createElement('input');
                methodField.type = 'hidden';
                methodField.name = '_method';
                methodField.value = 'DELETE';
                
                form.appendChild(csrfToken);
                form.appendChild(methodField);
                document.body.appendChild(form);
                form.submit();
            } else {
                window.location.href = url;
            }
        }
    }
</script>
@endpush
