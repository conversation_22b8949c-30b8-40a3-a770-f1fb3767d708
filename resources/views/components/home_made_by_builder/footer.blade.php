{{-- To make a editable image or text need to be add a "builder editable" class and builder identity attribute with a unique value --}}
{{-- builder identity and builder editable --}}
{{-- builder identity value have to be unique under a single file --}}

@if (get_frontend_settings('recaptcha_status'))
    @push('js')
        <script src="https://www.google.com/recaptcha/api.js" async defer></script>
    @endpush
@endif

<footer class="footer-area">
    <div class="container">
        <div class="row">
            <div class="col-lg-4 col-md-4">
                <div class="footer-content">
                    <img src="{{ get_image(get_frontend_settings('light_logo')) }}" alt="system logo">

                </div>
            </div>

        </div>
    </div>


</footer>


@push('js')

    <script>
        "use strict";

        function onNewslaterSubmit(token) {
            document.getElementById("newslater-form").submit();
        }

    </script>
@endpush
