{{-- To make a editable image or text need to be add a "builder editable" class and builder identity attribute with a unique value --}}
{{-- builder identity and builder editable --}}
{{-- builder identity value have to be unique under a single file --}}

<style>
    .heart {
        z-index: 99999;
        display: inline-block;
        top: 11px;
        position: absolute;
        right: 14px;
        background: #fff;
    }
    .mt-54{
        margin-top: 40px;
    }
    .h-190{
        height: 150px !important;
    }
    .p-15{
        padding: 15px !important;
    }
</style>

<section class="feature-wrapper section-padding">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="res-control d-flex align-items-center justify-content-between">
                    <div class="section-title mb-0">
                        <span class="title-head builder-editable" builder-identity="1">{{get_phrase('Courses')}}</span>
                        <h2 class="title builder-editable" builder-identity="2">{{get_phrase('Featured Courses')}}
                        </h2>
                    </div>
                    <span class="featured-course-all-button">
                        <a href="{{ route('courses') }}" class="eBtn gradient builder-editable" builder-identity="3">{{ get_phrase('View All Courses') }}</a>
                    </span>
                </div>
            </div>
        </div>
        <span class="featured-course">

            @php
                $featured_courses = App\Models\Course::where('status', 'active')->latest('id')->get();
            @endphp

            <div class="row mt-50 gx-5">
                @foreach ($featured_courses->take(4) as $key => $row)
                    <div class="col-lg-6 col-md-12 col-sm-6 mb-30">
                        <a href="{{ route('course.details', $row->slug) }}" class="single-feature p-15 w-100 checkPropagation">
                            <div class="row">
                                <div class="col-lg-5 col-md-5">
                                    <div class="courses-img">
                                        <img class="h-190" src="{{ get_image($row->thumbnail) }}" alt="...">
                                        <div class="cText d-flex">

                                            @if (isset($row->is_paid) && $row->is_paid == 0)
                                                <h4>{{ get_phrase('Free') }}</h4>
                                            @elseif (isset($row->discount_flag) && $row->discount_flag == 1)
                                                <h4>{{ currency($row->discounted_price, 2) }}
                                                </h4>
                                                <del>{{ currency($row->price, 2) }}</del>
                                            @else
                                                <h4>{{ currency($row->price, 2) }}</h4>
                                            @endif
                                        </div>
                                        @auth
                                        @if ($row->wishlists()->where('course_id', $row->id)->count() > 0)
                                            <span data-bs-toggle="tooltip" data-bs-title="{{ get_phrase('Remove from wishlist') }}" class="heart checkPropagation inList" onclick="wishlistToggle('{{ $row->id }}', this)"><i class="fa-regular fa-heart"></i></span>
                                        @else
                                            <span data-bs-toggle="tooltip" data-bs-title="{{ get_phrase('Add to wishlist') }}" class="heart checkPropagation" onclick="wishlistToggle('{{ $row->id }}', this)"><i class="fa-regular fa-heart"></i></span>
                                        @endif
                                    @endauth
                                    </div>
                                </div>
                                <div class="col-lg-7 col-md-7">
                                    <div class="entry-details">
                                        <div class="entry-title">
                                            <h3 class="ellipsis-line-2">{{ ellipsis(ucfirst($row->title), 160) }}</h3>

                                           
                                        </div>
                                        <ul>
                                            <li>
                                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M12.6521 9.54004L14.8477 8.27081C14.9758 8.19549 15.0399 8.08879 15.0399 7.95071C15.0399 7.81263 14.9758 7.70458 14.8477 7.62658L12.6521 6.35735C12.5395 6.29326 12.4144 6.26121 12.2768 6.26121C12.1392 6.26121 12.0133 6.29326 11.8989 6.35735L9.70344 7.62658C9.57523 7.7019 9.51113 7.8086 9.51113 7.94669C9.51113 8.08479 9.57523 8.19283 9.70344 8.27081L11.8989 9.54004C12.0115 9.60414 12.1366 9.63619 12.2743 9.63619C12.4119 9.63619 12.5378 9.60414 12.6521 9.54004ZM12.6521 11.9968L14.1265 11.1554C14.2395 11.0928 14.3305 11.0021 14.3995 10.8832C14.4686 10.7643 14.5031 10.636 14.5031 10.4984V9.32371L12.6521 10.399C12.5392 10.4685 12.4136 10.5032 12.2755 10.5032C12.1374 10.5032 12.0119 10.4685 11.8989 10.399L10.048 9.32371V10.4984C10.048 10.636 10.0825 10.7643 10.1515 10.8832C10.2206 11.0021 10.3116 11.0928 10.4246 11.1554L11.8989 11.9968C12.0115 12.0609 12.1366 12.0929 12.2743 12.0929C12.4119 12.0929 12.5378 12.0609 12.6521 11.9968ZM16.4101 16.25H12.2755C12.2755 16.0416 12.2684 15.8333 12.2543 15.625C12.2401 15.4166 12.2189 15.2083 12.1906 15H16.4101C16.4849 15 16.5464 14.9759 16.5944 14.9279C16.6425 14.8798 16.6666 14.8183 16.6666 14.7435V5.25642C16.6666 5.18163 16.6425 5.12019 16.5944 5.0721C16.5464 5.02402 16.4849 4.99998 16.4101 4.99998H3.58967C3.51488 4.99998 3.45344 5.02402 3.40536 5.0721C3.35727 5.12019 3.33323 5.18163 3.33323 5.25642V6.14263C3.1249 6.11431 2.91657 6.09307 2.70825 6.07892C2.49992 6.06476 2.29159 6.05769 2.08325 6.05769V5.25642C2.08325 4.84215 2.23076 4.48752 2.52577 4.19252C2.82077 3.89751 3.17541 3.75 3.58967 3.75H16.4101C16.8244 3.75 17.179 3.89751 17.474 4.19252C17.769 4.48752 17.9165 4.84215 17.9165 5.25642V14.7435C17.9165 15.1578 17.769 15.5124 17.474 15.8074C17.179 16.1025 16.8244 16.25 16.4101 16.25ZM6.58498 16.25C6.41715 16.25 6.27127 16.1973 6.14734 16.0921C6.02341 15.9869 5.94595 15.8498 5.91498 15.681C5.79425 14.8274 5.42592 14.1017 4.81 13.504C4.19409 12.9062 3.45664 12.5443 2.59767 12.4182C2.43692 12.3974 2.31099 12.3219 2.2199 12.1917C2.1288 12.0615 2.08325 11.913 2.08325 11.7464C2.08325 11.5693 2.14362 11.4209 2.26436 11.3013C2.38508 11.1816 2.52556 11.133 2.68581 11.1555C3.8685 11.2901 4.87623 11.7759 5.70902 12.613C6.54181 13.45 7.02712 14.4599 7.16494 15.6426C7.18737 15.8114 7.1409 15.9548 7.02552 16.0729C6.91013 16.1909 6.76329 16.25 6.58498 16.25ZM9.82361 16.25C9.64091 16.25 9.49107 16.1869 9.37409 16.0609C9.25711 15.9348 9.18741 15.7777 9.16496 15.5897C9.00257 13.86 8.31027 12.3918 7.08804 11.1851C5.86582 9.97834 4.3872 9.3034 2.65217 9.16023C2.48336 9.14313 2.34634 9.07206 2.24111 8.94702C2.13587 8.82197 2.08325 8.67607 2.08325 8.50933C2.08325 8.33217 2.14148 8.18162 2.25794 8.05769C2.37438 7.93377 2.5154 7.88035 2.681 7.89744C4.7622 8.0406 6.53275 8.84135 7.99267 10.2997C9.4526 11.758 10.2627 13.5235 10.4229 15.5961C10.44 15.7788 10.3887 15.9334 10.2689 16.06C10.1491 16.1867 10.0007 16.25 9.82361 16.25ZM2.93215 16.25C2.6942 16.25 2.49324 16.1678 2.32925 16.0035C2.16525 15.8392 2.08325 15.6381 2.08325 15.4001C2.08325 15.1622 2.1654 14.9612 2.32971 14.7972C2.494 14.6332 2.69512 14.5512 2.93306 14.5512C3.17101 14.5512 3.37197 14.6334 3.53596 14.7977C3.69996 14.962 3.78196 15.1631 3.78196 15.4011C3.78196 15.639 3.69981 15.84 3.5355 16.004C3.37121 16.168 3.17009 16.25 2.93215 16.25Z"
                                                        fill="#6B7385" />
                                                </svg>
                                                {{ lesson_count($row->id) }} {{ get_phrase('lesson') }}
                                            </li>
                                            <li>
                                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M2.25174 16.0896C2.03834 16.0896 1.85945 16.0174 1.71508 15.873C1.57072 15.7287 1.49854 15.5498 1.49854 15.3364V14.237C1.49854 13.8075 1.60938 13.4243 1.83108 13.0872C2.05276 12.7501 2.34881 12.4879 2.71924 12.3005C3.5112 11.9131 4.30755 11.6145 5.10828 11.4045C5.90904 11.1946 6.78911 11.0896 7.74852 11.0896C8.70792 11.0896 9.58799 11.1946 10.3887 11.4045C11.1895 11.6145 11.9858 11.9131 12.7778 12.3005C13.1482 12.4879 13.4443 12.7501 13.666 13.0872C13.8876 13.4243 13.9985 13.8075 13.9985 14.237V15.3364C13.9985 15.5498 13.9263 15.7287 13.7819 15.873C13.6376 16.0174 13.4587 16.0896 13.2453 16.0896H2.25174ZM15.3414 16.0896C15.4437 16.0024 15.5232 15.8934 15.58 15.7626C15.6367 15.6318 15.6651 15.4855 15.6651 15.3236V14.1345C15.6651 13.5875 15.5312 13.0662 15.2633 12.5705C14.9955 12.0749 14.6155 11.6497 14.1235 11.2948C14.6823 11.3781 15.2127 11.5071 15.7148 11.6818C16.217 11.8565 16.6961 12.0629 17.1523 12.3012C17.5829 12.5309 17.9154 12.8016 18.1499 13.1133C18.3844 13.4251 18.5017 13.7655 18.5017 14.1345V15.3364C18.5017 15.5498 18.4295 15.7287 18.2851 15.873C18.1407 16.0174 17.9619 16.0896 17.7485 16.0896H15.3414ZM7.74852 9.74343C6.94645 9.74343 6.25982 9.45784 5.68864 8.88668C5.11746 8.3155 4.83187 7.62887 4.83187 6.8268C4.83187 6.02472 5.11746 5.33809 5.68864 4.76693C6.25982 4.19575 6.94645 3.91016 7.74852 3.91016C8.55058 3.91016 9.23721 4.19575 9.80839 4.76693C10.3796 5.33809 10.6651 6.02472 10.6651 6.8268C10.6651 7.62887 10.3796 8.3155 9.80839 8.88668C9.23721 9.45784 8.55058 9.74343 7.74852 9.74343ZM14.944 6.8268C14.944 7.62887 14.6584 8.3155 14.0872 8.88668C13.516 9.45784 12.8294 9.74343 12.0273 9.74343C11.9333 9.74343 11.8136 9.73275 11.6683 9.71139C11.5231 9.69003 11.4034 9.66653 11.3094 9.64089C11.638 9.24579 11.8906 8.80748 12.0671 8.32595C12.2436 7.84442 12.3318 7.34437 12.3318 6.8258C12.3318 6.30725 12.2418 5.80918 12.0618 5.33162C11.8818 4.85406 11.631 4.41443 11.3094 4.01272C11.429 3.96998 11.5487 3.94221 11.6683 3.92939C11.788 3.91657 11.9077 3.91016 12.0273 3.91016C12.8294 3.91016 13.516 4.19575 14.0872 4.76693C14.6584 5.33809 14.944 6.02472 14.944 6.8268ZM2.74851 14.8396H12.7485V14.237C12.7485 14.0629 12.705 13.908 12.6179 13.7723C12.5308 13.6366 12.3927 13.518 12.2036 13.4165C11.5177 13.0629 10.8115 12.795 10.0851 12.6128C9.35856 12.4307 8.57971 12.3396 7.74852 12.3396C6.91732 12.3396 6.13847 12.4307 5.41197 12.6128C4.68549 12.795 3.97929 13.0629 3.29339 13.4165C3.10429 13.518 2.9662 13.6366 2.87912 13.7723C2.79205 13.908 2.74851 14.0629 2.74851 14.237V14.8396ZM7.74852 8.49347C8.20685 8.49347 8.59921 8.33028 8.9256 8.00389C9.25199 7.6775 9.41518 7.28514 9.41518 6.8268C9.41518 6.36847 9.25199 5.97611 8.9256 5.64972C8.59921 5.32333 8.20685 5.16014 7.74852 5.16014C7.29018 5.16014 6.89782 5.32333 6.57143 5.64972C6.24504 5.97611 6.08185 6.36847 6.08185 6.8268C6.08185 7.28514 6.24504 7.6775 6.57143 8.00389C6.89782 8.33028 7.29018 8.49347 7.74852 8.49347Z"
                                                        fill="#6B7385" />
                                                </svg>
                                                {{ course_enrollments($row->id) }} {{ get_phrase('Students') }}
                                            </li>
                                        </ul>
                                        <div class="creator mt-54">
                                            <img src="{{ course_instructor_image($row->id) }}" alt="...">
                                            <p>{{ get_phrase('by') }} <span>{{ course_by_instructor($row->id)->name }}</span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                @endforeach
            </div>
        </span>
    </div>
</section>
