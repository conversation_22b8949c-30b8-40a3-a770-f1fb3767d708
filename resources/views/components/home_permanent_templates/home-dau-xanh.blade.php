@extends('layouts.default_dau_xanh')
@section('content')

    <script src="{{ asset('assets/frontend/course-shopee/assets/js/source.js') }}"></script>
    <style>
        .toast-top-right {
            top: 90px;
        }
        .modal-regis .modal-dialog {
            max-width: 690px;
        }

        .modal-auth #authTabs {
            border: none;
        }

        .modal-auth {
            max-width: 690px;
        }

        .modal-auth .modal-body {
            padding: 20px 50px 40px !important;
        }

        .modal-auth .form-login {
            border-radius: 28px;
            border: 2px solid var(--gradient-2, #ff5a07);
            background: #fff;
            box-shadow: 0px 7px 35px 0px rgba(0, 0, 0, 0.15);
            overflow: hidden;
        }

        .form-wrap {
            position: relative;
        }

        .modal-auth .form-wrap {
            position: relative;
        }

        .modal-auth .modal-dialog {
            max-width: 500px;
            margin: 1.75rem auto;
        }

        .modal-auth .modal-content {
            border-radius: 25px;
            overflow: hidden;
            border: none;
        }

        .modal-auth .modal-header {
            background: #105ce4 !important;
            color: #fbed0b;
            border-bottom: none;
            padding: 1.25rem 1.5rem 0.5rem;
            position: relative;
            text-align: center;
        }

        .modal-auth .modal-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 0;
            line-height: 1.2;
            margin-bottom: 5px;
        }

        .modal-auth .close-btn {
            position: absolute;
            right: 15px;
            top: 10px;
            background: transparent;
            border: none;
            color: white;
            font-size: 21px;
            padding: 0;
        }

        .modal-auth .modal-header p {
            color: white;
            font-size: 0.95rem;
            margin-top: 0.5rem;
        }

        .modal-auth .google-btn {
            margin-bottom: 1rem;
            padding: 0.75rem;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            color: #222;
            border-radius: 10px;
            border: 1px solid #ffe6e8;
            background: linear-gradient(180deg, #fff 0%, #ffeff0 100%);
        }

        .modal-auth .google-logo {
            margin-right: 0.5rem;
        }

        .modal-auth .tabs-container {
            display: flex;
            position: relative;
            overflow: hidden;
            border-radius: 25px 25px 0 0;
        }

        .modal-auth .tab-switch-auth span {
            color: #fff;
            -webkit-text-fill-color: #fff;
        }

        .modal-auth .tab-switch-auth .active span {
            background: linear-gradient(270deg, #eb2805 0%, #fbc30b 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            border: none !important;
        }

        .modal-auth .tab-switch-auth .tab.tab-active {
            background: #fff;
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: #fff;
        }

        .modal-auth .tab-switch-auth .tab img {
            position: absolute;
            width: 100%;
            left: 0;
            z-index: 0;
            height: 100%;
            opacity: 1;
            transition: opacity 0.3s;
        }

        .modal-auth .tab-switch-auth .active img {
            opacity: 0;
        }

        .modal-auth .tab {
            flex: 1;
            text-align: center;
            height: 60px;
            font-weight: 700;
            color: white;
            cursor: pointer;
            position: relative;
            z-index: 1;
            font-size: 1.1rem;
        }

        .modal-auth .tab-active {
            color: #f33;
        }

        .modal-auth .tab-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 50%;
            height: 100%;
            background-color: white;
            z-index: 0;
            transition: transform 0.3s ease;
        }

        .modal-auth .tab-content {
            border: 1px solid #ffe0dc;
            border-top: none;
            border-radius: 0 0 25px 25px;
            padding: 20px 24px;
            background-color: white;
        }

        .form-label {
            margin-bottom: 0.25rem;
            font-size: 16px;
            position: absolute;
            background: #fff;
            top: -12px;
            z-index: 10;
            left: 14px;
            padding: 0 3px;
            color: #333;
        }

        .modal-auth .form-label {
            margin-bottom: 0.25rem;
            font-size: 16px;
            position: absolute;
            background: #fff;
            top: -12px;
            z-index: 10;
            left: 14px;
            padding: 0 3px;
            color: #333;
        }

        .modal-auth .form-control {
            border-radius: 10px;
            padding: 0.75rem;
            border-radius: 5px;
            border: 1px solid #dadada;
            height: 52px;
        }

        .modal-auth .form-text {
            color: #dc3f2e;
            font-weight: 500;
        }

        .modal-auth .red-text {
            color: #f33;
            font-weight: 500;
        }

        .password-field {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            background: none;
            border: none;
            cursor: pointer;
        }

        .modal-auth .signup-btn {
            background: linear-gradient(90deg, #ffb700 0%, #f33 100%);
            border-radius: 15px;
            margin-top: 1.5rem;
            width: 100%;
            border-radius: 10px;
            background: var(
                --gradient-2,
                linear-gradient(270deg, #eb2805 0%, #fbc30b 100%)
            );
            border: none;
            outline: none;
            font-size: 20px !important;
            font-weight: bold;
            color: #fff;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .modal-auth .free-badge {
            position: relative;
            width: 60px;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-auth .free-circle {
            width: 50px;
            height: 50px;
            background-color: #f33;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transform: rotate(-15deg);
        }

        .modal-auth .free-circle span {
            color: white;
            font-weight: bold;
            font-size: 0.8rem;
        }

        .modal-auth .btn-text {
            text-align: left;
            padding-left: 0.5rem;
            font-size: 19px !important;
        }

        .modal-auth .btn-title {
            font-size: 20px;
            line-height: 1.2;
        }

        .modal-auth .btn-subtitle {
            font-size: 14px;
            font-weight: 400;
            opacity: 0.9;
        }

        .modal-auth .nav-tabs .nav-link {
            width: 100%;
            border: none;
            background: none;
            padding: 0;
            position: relative;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        .modal-regis .img-bank {
            position: absolute;
            width: calc(100% - 40px);
            top: 45%;
            left: 50%;
            transform: translate(-50%, -50%);
            object-fit: contain;
        }

        .modal-regis .border-bank {
            aspect-ratio: 1;
            width: 100%;
        }

        .modal-regis .body-method-option {
            background: #f7f7f7;
            padding: 0 10px;
        }

        .modal-regis #btn-checkout-regis {
            border-radius: 10px;
            background: var(
                --gradient-2,
                linear-gradient(270deg, #eb2805 0%, #fbc30b 100%)
            );
            border: none;
            display: block;
            width: 100%;
            height: 56px;
            font-size: 18px;
            text-transform: uppercase;
            font-weight: bold;
            color: #fff;
        }

        .modal-regis .phone-group {
            border: 1px solid #dadada;
            border-radius: 5px;
        }

        .modal-regis .phone-group input {
            border: none;
        }

        .modal-regis .phone-group button {
            border: none;
            border-right: 1px solid #dadada;
            border-radius: 0;
            display: flex;
            width: 110px;
            font-size: 16px;
            align-items: center;
            gap: 6px;
            justify-content: center;
        }

        .modal-regis input#couponInput {
            color: #dc3f2e;
        }

        .modal-regis .step.active .step-label,
        .modal-regis .step.completed .step-label {
            font-weight: bold;
            color: #105ce4;
        }

        .modal-regis .step-label {
            color: #666;
        }

        .modal-regis .payment-header-t .input-group {
            gap: 8px;
            display: flex;
        }

        .modal-regis button#applyCoupon {
            border-radius: 5px;
            background: #fa8128;
            width: 110px;
            font-size: 16px;
        }

        .modal-regis .payment-header-t {
            padding: 0 20px;
            border-radius: 20px 20px 0px 0px;
            background: linear-gradient(180deg, #fff 0%, #fff4e9 100%);
        }

        .modal-regis .payment-container {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        }

        .modal-regis .progress-steps {
            display: flex;
            justify-content: space-between;
            padding: 20px 0;
            position: relative;
        }

        .modal-regis .progress-line {
            position: absolute;
            top: 34px;
            left: 30px;
            right: 100px;
            height: 1px;
            background-color: #fa8128;
            z-index: 0;
            width: calc(100% - 60px);
        }

        .modal-regis .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 1;
            /* color: #105CE4;
                   */
        }

        .modal-regis .step-circle {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            background-color: white;
            border: 1px solid #fa8128;
        }

        .modal-regis .step.active .step-circle {
            background-color: #ff8c00;
            border-color: #ff8c00;
            color: white;
        }

        .modal-regis .step.completed .step-circle {
            background-color: #ff8c00;
            border-color: #ff8c00;
            color: white;
        }

        .modal-regis .payment-header {
            margin-bottom: 10px;
        }

        .modal-regis .payment-body {
            padding: 20px 40px;
        }

        .modal-regis .price-info {
            display: flex;
            justify-content: space-between;
            /* margin-bottom: 5px;
                   */
        }

        .modal-regis .total-price {
            display: flex;
            justify-content: space-between;
            padding: 15px 0;
            margin-top: 10px;
            border-top: 1px dashed #e0e0e0;
        }

        .modal-regis .price {
            color: #ff5722;
            font-weight: bold;
        }

        .modal-regis .payment-methods-container {
            background-color: white;
            padding: 20px 15px;
            border-radius: 15px;
            border: 1px solid #ffd8a8;
        }

        .modal-regis .method-option {
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .modal-regis .method-title {
            /* font-weight: bold;
                   */
            margin-bottom: 15px;
            color: #333;
        }

        .modal-regis .qr-container {
            position: relative;
        }

        .modal-regis .bank-info {
            margin-bottom: 10px;
            font-size: 14px;
        }

        .modal-regis .bank-info-row {
            display: flex;
            justify-content: space-between;
            border-bottom: 1px solid #fff;
            padding: 8px 0;
            color: #333;
        }

        .modal-regis .bank-info-label {
            color: #333;
        }

        .modal-regis .bank-info-value {
            font-weight: 600;
            color: #333;
        }

        .modal-regis .warning-box {
            background-color: #fff9f0;
            border: 1px dashed #ffca80;
            border-radius: 8px;
            padding: 10px;
            margin: 15px 0;
            border: 1px dashed var(--Primary-Color, #fa8128);
            background: #fff9ec;
            font-size: 12px;
            color: #333;
        }

        .modal-regis .warning-icon {
            color: #ff8c00;
            margin-right: 5px;
        }

        .modal-regis .download-btn {
            background-color: #105ce4;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 5px 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            font-size: 12px;
        }

        .modal-regis .timer-container {
            background-color: #fff5eb;
            border-radius: 10px;
            padding: 10px 15px;
            /* margin-top: 20px; */
            display: flex;
            justify-content: space-between;
            border-radius: 15px;
            background: linear-gradient(180deg, #fff0f0 0%, #fed 100%);
            font-size: 16px;
            color: #333;
        }

        .modal-regis .timer {
            color: #105ce4;
            font-weight: bold;
            font-size: 24px;
        }

        .modal-regis .status-container {
            text-align: center;
            margin-top: 15px;
            color: #666;
        }

        .modal-regis .status-container .loading-icon {
            color: #fa8128;
        }

        .modal-regis .loading-icon {
            display: inline-block;
            margin-left: 5px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        .modal-regis .close-btn {
            position: absolute;
            top: 6px;
            right: 10px;
            background: none;
            border: none;
            font-size: 25px;
            cursor: pointer;
            background: #fff;
            /* padding: 5px; */
            /* border: 1px solid #DC3F2E; */
            border-radius: 50%;
            color: #dc3f2e !important;
            font-weight: normal !important;
            width: 30px;
            z-index: 99999;
            height: 30px;
        }

        .modal-regis .nav-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }

        .modal-regis .nav-btn {
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
            cursor: pointer;
        }

        .modal-regis .prev-btn {
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            color: #666;
        }

        .modal-regis .next-btn {
            background-color: #ffa726;
            border: none;
            color: white;
        }

        .modal-regis .tab-content {
            display: none;
        }

        .modal-regis .tab-content.active {
            display: block;
        }

        .modal-regis .user-info-container {
            background-color: white;
            padding: 20px;
            border-radius: 15px;
            border: 1px solid #ffd8a8;
            background: linear-gradient(to right, #fffcf5, #fff5eb);
            border-radius: 20px;
            border: 2px solid var(--gradient-2, #eb2805);
            background: #fff;
            box-shadow: 0px 7px 35px 0px rgba(0, 0, 0, 0.15);
        }

        .user-info-container h6 {
            font-size: 20px;
            color: #333;
        }

        .modal-regis .user-info-container a {
            color: #fa8128;
            text-decoration: none;
        }

        .modal-regis .success-container {
            background-color: white;
            padding: 40px 20px;
            border-radius: 15px;
            text-align: center;
            /* background: linear-gradient(to right, #fffaf0, #fff5eb);
                   */
        }

        .modal-regis .success-icon {
            width: 65px;
            height: 65px;
            background-color: #4caf50;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            color: white;
            font-size: 40px;
        }

        .modal-regis .success-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }

        .modal-regis .success-text {
            color: #666;
            font-size: 16px;
            line-height: 1.5;
            margin-bottom: 20px;
        }

        .modal-regis .start-course-btn {
            background: linear-gradient(to right, #ff8a00, #ff5722);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 30px;
            font-weight: bold;
            font-size: 16px;
            text-transform: uppercase;
            box-shadow: 0 4px 10px rgba(255, 138, 0, 0.2);
            width: 240px;
        }

        .modal-regis .contact-text {
            color: #666;
            margin-top: 20px;
        }

        .modal-regis .hotline {
            color: #ff5722;
            font-weight: bold;
        }

        .modal-regis .coupon-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
            margin-bottom: 15px;
        }

        .modal-regis .coupon-btn {
            background-color: white;
            border: 1px dashed #ff5722;
            border-radius: 30px;
            padding: 6px 15px;
            color: #ff5722;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s;
        }

        .modal-regis .coupon-btn:hover {
            background-color: #fff5f0;
            transform: translateY(-2px);
        }

        .modal-regis .coupon-tag {
            font-size: 10px;
            color: white;
            background-color: #ff5722;
            padding: 2px 4px;
            border-radius: 2px;
            margin-right: 5px;
        }

        .tab-switch-auth {
            border: none !important;
        }

        @media (max-width: 769px) {
            * {
            }

            .modal-auth .modal-body {
                padding: 20px 15px !important;
            }
        }

        @media (max-width: 500px) {
            * {
            }

            .col-password {
                flex-wrap: wrap;
            }

            .modal-auth .form-wrap {
                width: 100% !important;
            }

            .modal-auth .modal-header {
                font-size: 13px;
            }

            .modal-auth .modal-title {
                font-size: 18px;
            }

            .modal-auth .tab-content {
                padding: 20px 15px;
            }

            .modal-auth .tab {
                height: 51px;
            }

            .modal-auth .tab-switch-auth span {
                font-size: 16px;
            }
        }

    </style>

    <header id="header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-4 col-md-4 col-lg-2 col-xl-2">
                    <div id="logo">
                        <a href="/">
                            <img src="{{ asset('assets/frontend/dau-xanh/assets/images/logo.png') }}" alt="Logo">
                        </a>
                    </div>
                </div>
                <div class="col-4 col-md-7 col-lg-7 col-xl-7 d-none d-md-block d-lg-block d-md-block">
                    <div id="main-menu">
                        <ul>
                            <li><a href="#sec-abouts">Giới thiệu</a></li>
                            <li><a href="#sec-course">Khóa học</a></li>
                            <li><a href="#sec-document">Phương pháp</a></li>
                            <li><a href="#sec-author">Tác giả</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-8 col-md-8 col-lg-3 col-xl-3">
                    <div class="header-right">
                        @include("menu-profile-course")
                    </div>
                </div>
            </div>
        </div>
    </header>

    <main id="main">
        <section class="section sec-banner banner-home">
            <div class="banner-container">
                <div class="container">
                    <div class="row align-middle">
                        <div class="col-12 col-md-6 col-lg-6 col-xl-6">
                            <h3 class="banner_heading">Đột phá tư duy học tập</h3>
                            <div class="banner-text">
                                <h3 style="text-align: left;font-size: 34px;"
                                    class="d-none d-md-block d-lg-block d-xl-block">CHINH PHỤC KIẾN THỨC DỄ DÀNG <br>VỚI
                                    PHƯƠNG
                                    PHÁP HỌC THÔNG MINH</h3>
                                <h3 style="text-align: left;" class="d-block d-md-none d-lg-none d-xl-none">CHINH PHỤC KIẾN
                                    THỨC DỄ DÀNG VỚI PHƯƠNG
                                    PHÁP HỌC THÔNG MINH</h3>
                                <div class="banner-list banner-nap">
                                    <div class="banner-list-left nap">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                             preserveAspectRatio="none" viewBox="0 0 1416.54 1896.08" class=""
                                             fill="rgba(255, 255, 255, 1)">
                                            <use xlink:href="#shape_AncYUuInWN"></use>
                                        </svg>
                                    </div>
                                    <div class="banner-list-right nap">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                             preserveAspectRatio="none" viewBox="0 0 1416.54 1896.08" class=""
                                             fill="rgba(255, 255, 255, 1)">
                                            <use xlink:href="#shape_AncYUuInWN"></use>
                                        </svg>
                                    </div>
                                    <div class="ladi-paragraph"><span style="color: rgb(254, 222, 30);">▸</span> Các khóa
                                        học tại Đậu Xanh được xây dựng dựa trên những <span style="font-weight: bold;">phương pháp ghi nhớ thông minh</span>
                                        và <span style="font-weight: bold;">tư duy hệ thống</span>.
                                    </div>
                                    <div class="ladi-paragraph"><span style="color: rgb(254, 242, 31);">▸</span> Giúp
                                        bạn<span style="font-weight: bold;"> <span style="color: rgb(0, 0, 0);">học nhanh, nhớ lâu,&nbsp;</span></span>
                                        biến việc học trở thành hành trình<span style="font-weight: bold;"> thú vị và đầy cảm hứng.</span>
                                    </div>
                                </div>
                                <div class="banner-plain">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                         preserveAspectRatio="none" viewBox="0 0 1792.0013 1896.0833" class=""
                                         fill="rgba(255, 255, 255, 1)">
                                        <path d="M1764 11q33 24 27 64l-256 1536q-5 29-32 45-14 8-31 8-11 0-24-5l-453-185-242 295q-18 23-49 23-13 0-22-4-19-7-30.5-23.5T640 1728v-349l864-1059-1069 925-395-162q-37-14-40-55-2-40 32-59L1696 9q15-9 32-9 20 0 36 11z"></path>
                                    </svg>
                                </div>
                                <div class="banner-net">
                                    <img src="{{ asset('assets/frontend/dau-xanh/assets/images/net-dut.png')}}" alt="">
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-6 col-lg-6 col-xl-6" style="position: relative">
                            <div class="bannerImage">
                                <img src="{{ asset('assets/frontend/dau-xanh/assets/images/lai-may-bay-209-min-20250518093515-7izym.png')}}" alt="">
                            </div>
                            <div class="bannerLine">
                                <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                     preserveAspectRatio="none" viewBox="0 0 300 300" fill="rgba(255, 255, 255, 1)"
                                     class="">
                                    <path d="M12,202.2l-0.8,2.8l-0.3,1l-0.7,2.8c-0.7,2.6,0.4,4.4,0.4,4.4c0,0,1.7-1.1,2.4-3.7l0.7-2.8l0.3-1l0.8-2.8   c0.7-2.6-0.2-4.4-0.2-4.4C14.5,198.6,12.7,199.6,12,202.2z"></path>
                                    <path d="M20.9,173.1l-0.9,2.7l-0.3,1l-0.9,2.7c-0.8,2.6,0.1,4.4,0.1,4.4c0,0,1.8-1,2.6-3.5l0.9-2.7l0.3-1l0.9-2.7   c0.9-2.6,0-4.4,0-4.4C23.6,169.7,21.8,170.6,20.9,173.1z"></path>
                                    <path d="M31.8,144.7l-1.1,2.7l-0.4,1l-1.1,2.7c-1,2.5-0.2,4.4-0.2,4.4c0,0,1.9-0.8,2.9-3.3l1.1-2.7l0.4-1l1.1-2.6 c1.1-2.5,0.4-4.4,0.4-4.4C34.7,141.4,32.8,142.2,31.8,144.7z"></path>
                                    <path d="M45.5,117.4l-1.5,2.5l-0.5,0.9l-1.4,2.5c-1.3,2.4-0.7,4.4-0.7,4.4c0,0,1.9-0.7,3.2-3l1.4-2.5l0.5-0.9l1.5-2.5 c1.4-2.3,1-4.3,1-4.3C48.9,114.6,46.9,115.1,45.5,117.4z"></path>
                                    <path d="M65,93.7c-2.2,1.7-2.3,1.9-2.3,1.9l-0.8,0.7c0,0-0.2,0.1-2.1,2c-2,2-1.8,4.1-1.8,4.1c0,0,2-0.2,3.8-2.1   c1.9-1.9,2-1.9,2-2l0.8-0.7c0,0,0.1-0.1,2.1-1.8c2-1.6,2.5-3.5,2.5-3.5C69.3,92.4,67.3,92,65,93.7z"></path>
                                    <path d="M94.8,89.3C92.3,88,92,88,92.1,88c-0.3-0.2-0.7-0.3-1.1-0.4c0,0-0.1-0.2-3-0.8c-2.9-0.5-4.4,1.1-4.4,1   c0,0,1.5,1.3,3.9,1.8c2.5,0.5,2.6,0.7,2.6,0.7c0.3,0.1,0.6,0.2,0.9,0.3c0,0,0.2,0,2.4,1.2c2.1,1.2,4.1,1.2,4.1,1.2  C97.5,93,97.4,90.8,94.8,89.3z"></path>
                                    <path d="M110.5,112.4l-0.3-1c0,0,0-0.2-1-2.7c-1-2.6-3-3.3-2.9-3.3c0,0-0.6,1.9,0.3,4.4c1,2.5,1,2.7,1,2.7l0.3,1   c0,0,0.1,0.2,0.9,2.7c0.8,2.5,2.5,3.6,2.5,3.6c0,0,1-1.8,0.2-4.4C110.6,112.5,110.5,112.4,110.5,112.4z"></path>
                                    <path d="M117.4,142.1l-0.2-1l-0.5-2.8c-0.5-2.7-2.2-3.9-2.2-3.9c0,0-1.1,1.7-0.6,4.4l0.5,2.8l0.2,1.1l0.5,2.8    c0.5,2.7,2.1,3.9,2.1,3.9s1.1-1.7,0.7-4.4L117.4,142.1z"></path>
                                    <path d="M123.1,171.8l-0.2-1l-0.6-2.8c-0.6-2.6-2.2-3.8-2.2-3.8c0,0-1.1,1.7-0.5,4.4l0.6,2.8l0.2,1l0.7,2.8    c0.7,2.7,2.5,3.7,2.5,3.7c0,0,0.9-1.8,0.2-4.4L123.1,171.8z"></path>
                                    <path d="M136.7,197c-0.3-0.1-0.3-0.2-0.3-0.2c-0.3-0.2-0.6-0.3-0.8-0.5c0,0-0.2,0-2.1-1.6c-1.8-1.7-3.7-2.1-3.7-2.1    c0,0-0.4,2.1,1.8,4.2c2.2,1.9,2.5,1.8,2.5,1.9c0.3,0.2,0.7,0.4,1,0.6c0,0,0,0,0.4,0.2c0.4,0.2,1.1,0.5,2.6,0.9  c1.5,0.4,2.7,0.1,3.4-0.3c0.7-0.3,1.1-0.7,1.1-0.7c0,0-1.5-1.2-3.8-1.8C137.6,197.5,137,197.2,136.7,197z"></path>
                                    <path d="M160.9,183.6c-1.2,2.3-1.3,2.5-1.3,2.5L159,187c0,0,0,0.2-1.5,2.3c-1.5,2.1-1.5,4-1.5,4c0,0,2.1-0.1,3.8-2.4   c1.6-2.3,1.6-2.5,1.6-2.5l0.6-0.9c0,0,0.1-0.1,1.4-2.6c1.2-2.5,0.5-4.4,0.5-4.4C163.8,180.5,162,181.3,160.9,183.6z"></path>
                                    <path d="M170.3,155.1l-0.8,2.8l-0.3,1l-0.8,2.8c-0.7,2.6,0.2,4.4,0.2,4.4c0,0,1.8-1,2.5-3.6l0.8-2.8l0.3-1l0.8-2.8   c0.7-2.6-0.2-4.4-0.2-4.4C172.8,151.5,171,152.5,170.3,155.1z"></path>
                                    <path d="M185,125c-0.8,0.2-1.9,0.7-2.9,1.8c-1.8,2.2-1.8,2.4-1.9,2.4l-0.6,0.9c0,0-0.2,0.1-1.5,2.6  c-1.3,2.5-0.5,4.4-0.5,4.4c0,0,1.8-0.8,3-3.1c1.2-2.3,1.4-2.4,1.4-2.4l0.6-0.8c0,0,0-0.2,1.7-2.1c1.6-1.8,2-3.7,2-3.7   C186.3,124.9,185.8,124.8,185,125z"></path>
                                    <path d="M208.1,128.2c-0.2-0.3-0.4-0.6-0.7-0.9c0,0,0-0.3-2.2-2.3c-1.1-1-2.4-1.3-3.2-1.3c-0.8,0-1.3,0.2-1.3,0.2  c0,0,0.9,1.7,2.6,3.2c1.8,1.6,1.7,1.9,1.8,1.9c0.2,0.3,0.4,0.5,0.6,0.8c0,0,0.2,0.1,1.3,2.3c1,2.3,2.7,3.3,2.7,3.3  c0,0,1-1.9-0.2-4.5C208.4,128.3,208.1,128.2,208.1,128.2z"></path>
                                    <path d="M217.6,159.8c-1.3-2.1-1.2-2.3-1.3-2.3l-0.4-0.9c0,0-0.1-0.1-0.9-2.6c-0.7-2.5-2.3-3.7-2.3-3.7  c0,0-1.1,1.7-0.4,4.4c0.8,2.7,1,2.9,1,2.9l0.4,1.1l0.2,0.4c0.2,0.4,0.5,1.1,1.3,2.3c0.9,1.2,2,1.9,2.8,2.1c0.8,0.2,1.3,0.1,1.3,0.1  c0,0-0.1-0.5-0.4-1.2C218.7,161.6,218.3,160.8,217.6,159.8z"></path>
                                    <path d="M249.4,165.5c0,0-1.5-1.4-4.2-1.4h-2.9h-1.1h-2.9c-2.7,0-4.2,1.4-4.2,1.4s1.5,1.4,4.2,1.4h2.9h1.1h2.9 C247.9,166.9,249.4,165.5,249.4,165.5z"></path>
                                    <path d="M266.3,156c-14.6-6.3-23.5-7.6-23.5-7.6s7,5.6,21.6,12l8.6,3.7h-1.7h-0.9h-2.4c-2.3,0-3.5,1.4-3.5,1.4 s1.2,1.4,3.5,1.4h2.4h0.9h2.4c1.6,0,2.7-0.7,3.2-1.1l0.9,0.4l-13.3,5.4c-14.8,6-21.9,11.4-21.9,11.4s8.9-1.1,23.7-7l18.1-7.3    l5.5-2.2l-6.1-2.6L266.3,156z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="sec-down">
                <svg xmlns="http://www.w3.org/2000/svg" height="100%" viewBox="0 0 960 960" width="100%"
                     preserveAspectRatio="none" class="" fill="rgba(1, 133, 197, 1)">
                    <use xlink:href="#shape_fIYeneYUPl"></use>
                </svg>
            </div>
            <div class="banner-dot1">
                <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none"
                     viewBox="0 0 195.9 196.2" class="" fill="rgba(82, 196, 251, 1.0)">
                    <use xlink:href="#shape_cenMemItpV"></use>
                </svg>
            </div>
            <div class="banner-cloud1">
                <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none"
                     viewBox="0 0 249.8 79.6" class="" fill="url(&quot;#SHAPE72_desktop_gradient&quot;)">
                    <defs id="SHAPE72_defs">
                        <linearGradient id="SHAPE72_desktop_gradient" gradientTransform="rotate(90)">
                            <stop offset="0%" stop-color="rgba(215, 239, 255, 1)"></stop>
                            <stop offset="100%" stop-color="#70D2FF"></stop>
                        </linearGradient>
                    </defs>
                    <path d="M249.8,79.6c-1.7-42.9-28.8-46.2-48.7-40-3.1,1-7.2.9-7.6-3.4-1.6-17.3-10.6-28.8-27.7-31S139.2,14.6,131,28.1c-7-9.9-13.1-19.3-24.4-24.4C81.1-7.8,57.5,9.3,51.4,28.6c-2.7,8.5-4.4,12.7-15.6,9.9C13.8,32.9-.2,52,0,79.6"></path>
                </svg>
            </div>
            <div class="banner-cloud2">
                <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none"
                     viewBox="0 0 249.8 79.6" class="" fill="url(&quot;#SHAPE79_desktop_gradient&quot;)">
                    <defs id="SHAPE79_defs">
                        <linearGradient id="SHAPE79_desktop_gradient" gradientTransform="rotate(90)">
                            <stop offset="0%" stop-color="rgba(255, 255, 255, 1)"></stop>
                            <stop offset="100%" stop-color="#AEE6FF"></stop>
                        </linearGradient>
                    </defs>
                    <path d="M249.8,79.6c-1.7-42.9-28.8-46.2-48.7-40-3.1,1-7.2.9-7.6-3.4-1.6-17.3-10.6-28.8-27.7-31S139.2,14.6,131,28.1c-7-9.9-13.1-19.3-24.4-24.4C81.1-7.8,57.5,9.3,51.4,28.6c-2.7,8.5-4.4,12.7-15.6,9.9C13.8,32.9-.2,52,0,79.6"></path>
                </svg>
            </div>
            <div class="banner-cloud3">
                <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none"
                     viewBox="0 0 249.8 79.6" class="" fill="url(&quot;#SHAPE77_desktop_gradient&quot;)">
                    <defs id="SHAPE77_defs">
                        <linearGradient id="SHAPE77_desktop_gradient" gradientTransform="rotate(90)">
                            <stop offset="0%" stop-color="rgba(255, 255, 255, 1)"></stop>
                            <stop offset="100%" stop-color="#AEE6FF"></stop>
                        </linearGradient>
                    </defs>
                    <path d="M249.8,79.6c-1.7-42.9-28.8-46.2-48.7-40-3.1,1-7.2.9-7.6-3.4-1.6-17.3-10.6-28.8-27.7-31S139.2,14.6,131,28.1c-7-9.9-13.1-19.3-24.4-24.4C81.1-7.8,57.5,9.3,51.4,28.6c-2.7,8.5-4.4,12.7-15.6,9.9C13.8,32.9-.2,52,0,79.6"></path>
                </svg>
            </div>
            <div class="banner-mattroi">
                <svg xmlns="http://www.w3.org/2000/svg" height="100%" viewBox="0 -960 960 960" width="100%"
                     preserveAspectRatio="none" class="" fill="rgba(255, 255, 255, 1)">
                    <path d="M450-770v-150h60v150h-60Zm256 106-42-42 106-107 42 43-106 106Zm64 214v-60h150v60H770ZM450-40v-150h60v150h-60ZM253-665 148-770l42-42 106 106-43 41Zm518 517L664-254l41-41 108 104-42 43ZM40-450v-60h150v60H40Zm151 302-43-42 105-105 22 20 22 21-106 106Zm289-92q-100 0-170-70t-70-170q0-100 70-170t170-70q100 0 170 70t70 170q0 100-70 170t-170 70Zm0-60q75 0 127.5-52.5T660-480q0-75-52.5-127.5T480-660q-75 0-127.5 52.5T300-480q0 75 52.5 127.5T480-300Zm0-180Z"></path>
                </svg>
            </div>
            <div class="banner-dot2">
                <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none"
                     viewBox="0 0 195.9 196.2" class="" fill="rgba(24, 158, 222, 1.0)">
                    <use xlink:href="#shape_cenMemItpV"></use>
                </svg>
            </div>
        </section><!--sec-banner-->

        <section class="section sec-dx" id="sec-abouts">
            <div class="container">
                <div class="row">
                    <div class="col-12 col-md-4 col-lg-4 col-xl-4">
                        <div class="author-story">
                            <div class="story-right" style="width: 100%">Về Đậu Xanh Edu</div>
                        </div>
                        <div class="dx-about-author">
                            <img src="{{ asset('assets/frontend/dau-xanh/assets/images/ngoc-4-20250404090917-6wr2t.webp')}}" alt="">
                        </div>
                    </div>
                    <div class="col-12 col-md-8 col-lg-8 col-xl-8">
                        <div class="dx-line mb-4">
                            <div class="dx-heading">
                                <h3>Việc học tập <span style="text-decoration-line: underline;">không nên</span> là một cuộc
                                    chạy đua căng thẳng.<br></h3>
                            </div>
                            <div class="dx-line-body">
                                <div class="ladi-paragraph">Bạn cảm thấy <span
                                        style="font-weight: bold; color: rgb(255, 92, 0);">mệt mỏi, chán nản, và áp lực</span>
                                    vì học mãi mà vẫn chẳng tiến bộ?<br>Bạn không hề cô đơn đâu! 😉&nbsp;<br>_____________________________<br>Tại
                                    Đậu Xanh, chúng mình hiểu rõ điều đó – và cũng từng như bạn.<br>✔️&nbsp; Đó là lí do vì
                                    sao chúng mình tạo ra những khóa học với mong muốn biến những kiến thức khô khan trở nên
                                    <span style="font-weight: bold; color: rgb(255, 92, 0);">vui </span><span
                                        style="font-weight: bold; color: rgb(255, 92, 0);">vẻ</span><span
                                        style="font-weight: bold; color: rgb(255, 92, 0);">, gần gũi, và "thân thiện hơn với bộ nhớ".</span><br>
                                </div>

                                <div class="ladi-paragraph">✅ Bằng cách kết hợp giữa ...</div>

                                <div class="row">
                                    <div class="col-12 col-md-4 col-lg-4 mt-footer">
                                        <div class="dxItem">
                                            <h4>KHOA HỌC<br>TRÍ NHỚ</h4>
                                            <div class="dxIcon">
                                                <svg xmlns="http://www.w3.org/2000/svg" height="100%"
                                                     viewBox="0 -960 960 960" width="100%" preserveAspectRatio="none"
                                                     class="" fill="rgba(1, 133, 197, 1)">
                                                    <path d="M490.74-349Q557-349 603-391.08T649-493q0-52-33.58-89T534-619q-42 0-72 27.5t-30 66.61q0 16.89 6.5 32.89t19.5 30l44-42q-5-3.5-7.5-8.75T492-524q0-14 11-24t31-10q23 0 39.5 18.5T590-492q0 35.19-28 59.59Q534-408 492-408q-51.2 0-86.6-40.5Q370-489 370-548.58 370-580 381.5-608t33.5-50l-43-43q-30 29-46.5 68.28-16.5 39.29-16.5 82.4 0 84.32 53.01 142.82T490.74-349ZM240-80v-172q-57-52-88.5-121.5T120-520q0-150 105-255t255-105q125 0 221.5 73.5T827-615l55 218q4 14-5 25.5T853-360h-93v140q0 24.75-17.62 42.37Q724.75-160 700-160H600v80h-60v-140h160v-200h114l-45-180q-24-97-105-158.5T480-820q-125 0-212.5 86.5T180-522.46q0 64.42 26.32 122.39Q232.65-342.09 281-297l19 18v199h-60Zm257-370Z"></path>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-4 col-lg-4 mt-footer">
                                        <div class="dxItem">
                                            <h4 style="width: calc(100% - 53px)">TƯ DUY<br>HỆ THỐNG</h4>
                                            <div class="dxIcon">
                                                <svg style="width: 53px;height: 53px" xmlns="http://www.w3.org/2000/svg"
                                                     width="100%" height="100%"
                                                     preserveAspectRatio="none" viewBox="0 0 1920 1896.08" class=""
                                                     fill="rgba(1, 133, 197, 1)">
                                                    <use xlink:href="#shape_iKgJnHoohr"></use>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-4 col-lg-4 mt-footer">
                                        <div class="dxItem">
                                            <h4 style="width: calc(100% - 53px)">PHƯƠNG PHÁP<br>SÁNG TẠO</h4>
                                            <div class="dxIcon">
                                                <svg style="width: 53px;height: 53px" xmlns="http://www.w3.org/2000/svg"
                                                     height="100%"
                                                     viewBox="0 -960 960 960" width="100%" preserveAspectRatio="none"
                                                     class="" fill="rgba(1, 133, 197, 1)">
                                                    <path d="M740-226q13.59 0 23.29-9.71Q773-245.41 773-259t-9.71-23.29Q753.59-292 740-292t-23.29 9.71Q707-272.59 707-259t9.71 23.29Q726.41-226 740-226Zm-304-74q9 0 15.5 9.5T458-264q0 17-4 34.5t-4 49.5h68q0-34-6-52.5t-6-37.5q0-17 7.5-25.5T530-304q9 0 17 3t23 11q20 11 33.5 15.5T626-270q21 0 33.5-15.5T672-318q0-14-10-22t-30-16q-17-6-24.5-12t-7.5-14q0-9 12.5-12.5T660-400q30-2 47-8t17-24q0-17-9.5-30.5T690-476q-7 0-36 10-11 5-17 6.5t-11 1.5q-8 0-13-5.5t-5-14.5q0-14 13-25.5t51-28.5q41-18 54.5-32t13.5-28q0-20-17.5-40T690-652q-14 0-26.5 13T622-584q-20 29-34.5 39.5T558-534q-14 0-27-11t-27-41q-12-27-21.5-38.5T458-636q-18 0-30 7.5T416-612q0 9 20 42 9 14 12.5 21t3.5 15q0 8-9.5 14t-18.5 6q-11 0-21-5.5T376-542q-18-17-26-22.5t-20-5.5q-15 0-25.5 10.5T294-538q0 6 2 11.5t8 14.5q6 9 9 15.5t3 12.5q0 9-8 14t-22 10q-17 6-23.5 13t-6.5 19q0 8 2.5 17.5T268-396q10 8 25 9t27 1q23 0 29.5 8.5T356-352q0 12-5 24t-17 26q-14 15-17 21t-3 11q0 14 12.5 23t23.5 9q12 0 22.5-6.5T400-272q15-18 21.5-23t14.5-5ZM140-120q-24 0-42-18t-18-42v-513q0-23 18-41.5t42-18.5h147l73-87h240l73 87h147q23 0 41.5 18.5T880-693v513q0 24-18.5 42T820-120H140Zm0-60h680v-513H645l-73-87H388l-73 87H140v513Zm340-257Z"></path>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="ladi-paragraph">Chúng mình sẽ giúp bạn <span
                                        style="font-weight: bold; color: rgb(255, 92, 0);">tiếp thu nhanh, ghi nhớ chắc,</span>&nbsp;để
                                    việc học trở thành một hành trình<span style="color: rgb(255, 92, 0);"><span
                                            style="font-weight: bold;">&nbsp;hiệu quả, vui vẻ và tràn đầy cảm hứng!</span></span><br>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex justify-content-center">
                            <div class="line-down">
                                <svg xmlns="http://www.w3.org/2000/svg" height="100%" viewBox="0 0 960 960" width="100%"
                                     preserveAspectRatio="none" class="" fill="rgba(72, 188, 244, 1)">
                                    <use xlink:href="#shape_DijKrreWIZ"></use>
                                </svg>
                            </div>
                            <div class="line-down">
                                <svg xmlns="http://www.w3.org/2000/svg" height="100%" viewBox="0 0 960 960" width="100%"
                                     preserveAspectRatio="none" class="" fill="rgba(72, 188, 244, 1)">
                                    <use xlink:href="#shape_DijKrreWIZ"></use>
                                </svg>
                            </div>
                            <div class="line-down">
                                <svg xmlns="http://www.w3.org/2000/svg" height="100%" viewBox="0 0 960 960" width="100%"
                                     preserveAspectRatio="none" class="" fill="rgba(72, 188, 244, 1)">
                                    <use xlink:href="#shape_DijKrreWIZ"></use>
                                </svg>
                            </div>
                            <div class="line-down">
                                <svg xmlns="http://www.w3.org/2000/svg" height="100%" viewBox="0 0 960 960" width="100%"
                                     preserveAspectRatio="none" class="" fill="rgba(72, 188, 244, 1)">
                                    <use xlink:href="#shape_DijKrreWIZ"></use>
                                </svg>
                            </div>
                            <div class="line-down">
                                <svg xmlns="http://www.w3.org/2000/svg" height="100%" viewBox="0 0 960 960" width="100%"
                                     preserveAspectRatio="none" class="" fill="rgba(72, 188, 244, 1)">
                                    <use xlink:href="#shape_DijKrreWIZ"></use>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="dx-cloud1">
                <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none"
                     viewBox="0 0 163.6 150" class="" fill="url(&quot;#SHAPE36_desktop_gradient&quot;)">
                    <defs id="SHAPE36_defs">
                        <linearGradient id="SHAPE36_desktop_gradient" gradientTransform="rotate(85)">
                            <stop offset="0%" stop-color="rgba(72, 188, 244, 1)"></stop>
                            <stop offset="100%" stop-color="rgba(237, 246, 255, 1)"></stop>
                        </linearGradient>
                    </defs>
                    <path d="M143.3,42.1A36.2,36.2,0,0,0,88.7,14a24.8,24.8,0,0,0-46.9,8,51.9,51.9,0,0,0,7.2,102.7,51.9,51.9,0,0,0,96.4-26.5c0-.4,0-.8,0-1.2a29.2,29.2,0,0,0-2-54.9Z"
                          transform="translate(0 0)"></path>
                </svg>
            </div>
            <div class="dx-cloud2">
                <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none"
                     viewBox="0 0 163.6 150" class="" fill="url(&quot;#SHAPE106_desktop_gradient&quot;)">
                    <defs id="SHAPE106_defs">
                        <linearGradient id="SHAPE106_desktop_gradient" gradientTransform="rotate(90)">
                            <stop offset="0%" stop-color="rgba(3, 89, 131, 1.0)"></stop>
                            <stop offset="100%" stop-color="rgba(1, 116, 172, 1)"></stop>
                        </linearGradient>
                    </defs>
                    <path d="M143.3,42.1A36.2,36.2,0,0,0,88.7,14a24.8,24.8,0,0,0-46.9,8,51.9,51.9,0,0,0,7.2,102.7,51.9,51.9,0,0,0,96.4-26.5c0-.4,0-.8,0-1.2a29.2,29.2,0,0,0-2-54.9Z"
                          transform="translate(0 0)"></path>
                </svg>
            </div>
            <div class="dx-cloud3">
                <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none"
                     viewBox="0 0 949 438.3" class="" fill="rgba(237, 246, 255, 1)">
                    <defs>
                        <style>.shape_sKcSKoguNS.cls-1 {
                                opacity: 0.1;
                            }

                            .shape_sKcSKoguNS.cls-2 {
                                opacity: 0.55;
                            }</style>
                    </defs>
                    <path class="cls-1 shape_sKcSKoguNS"
                          d="M0,407.8s65.9-.1,162.3-16.8,223.2-46.1,345-96.6S740,173.8,820.4,116.2,948.8-.4,948.8-.4V438H0Z"
                          transform="translate(0 0.4)"></path>
                    <path class="cls-2 shape_sKcSKoguNS"
                          d="M948.8,22.6V438H0V422.2c15.1-1,87.2-6.7,171-23,41.8-8.3,89.2-19,140-33C373.6,349,441.1,327,509.3,298.9c38.4-15.8,76.6-33.6,114-52.7a1769.5,1769.5,0,0,0,164.8-96.3q16.5-10.9,32.2-21.8C882.4,84.9,928.5,41.2,948.8,22.6Z"
                          transform="translate(0 0.4)"></path>
                    <path class="cls-3 shape_sKcSKoguNS"
                          d="M948.8,438H0s131.5-11.4,316.9-66.3c92.7-27.5,198.5-65.8,307.2-119.1a1717.8,1717.8,0,0,0,164-91.6A1528.7,1528.7,0,0,0,948.8,43.8h0l.2,197.1v98.5Z"
                          transform="translate(0 0.4)"></path>
                </svg>
            </div>
            <div class="dx-cloud4">
                <svg xmlns="http://www.w3.org/2000/svg" height="100%" viewBox="0 -960 960 960" width="100%"
                     preserveAspectRatio="none" class="" fill="rgba(163, 216, 241, 1)">
                    <path d="M480-437Zm353-243q0-81-56.03-139T640-877v-43q100 0 170 70t70 170h-47Zm-87 0q0-45-30.5-76T640-787v-46q63.75 0 108.38 45Q793-743 789-680h-43ZM140-120q-24 0-42-18t-18-42v-513q0-23 18-41.5t42-18.5h147l73-87h240v60H388l-73 87H140v513h680v-460h60v460q0 24-18.5 42T820-120H140Zm339.5-147q72.5 0 121.5-49t49-121.5q0-72.5-49-121T479.5-607q-72.5 0-121 48.5t-48.5 121q0 72.5 48.5 121.5t121 49Zm0-60q-47.5 0-78.5-31.5t-31-79q0-47.5 31-78.5t78.5-31q47.5 0 79 31t31.5 78.5q0 47.5-31.5 79t-79 31.5Z"></path>
                </svg>
            </div>
        </section><!--sec-abouts-->

        <section class="section sec-course2" id="sec-course" style="background: rgb(1, 133, 197)">
            <div class="container">
                <div class="row">
                    <div class="col-12 col-md-12 col-lg-12">
                        <div class="home-title text-center notline">
                            <h3 class="font-46" style="color:#fff;background: transparent;-webkit-text-fill-color:unset">
                                Danh sách khóa học</h3>
                            <div class="home-line">
                                <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                     preserveAspectRatio="none" viewBox="0 0 422.41 34.48" class="" fill="#FDDD1E">
                                    <use xlink:href="#shape_LDdVChIGcB"></use>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="list-course">
                    <div class="row">
                        @if(isset($courses) && $courses->count()>0)
                            @foreach($courses as $course)
                                <div class="col-12 col-lg-4 col-xl-4 mt-footer">
                                    <div class="course-item">
                                        <div class="course-thumbnail">
                                            <a href="{{ route('course.details', $course->slug) }}"><img
                                                    {{--                                                    src="{{ asset('assets/frontend/dau-xanh/assets/images/thumb-khoa-13-20250518143734-puaoz.jpg') }}"--}}
                                                    src="{{ get_image($course->thumbnail) }}"
                                                    alt="{{$course->title}}"></a>
                                        </div>
                                        <div class="course-content">
                                            <h3>
                                                <a href="{{ route('course.details', $course->slug) }}">{{$course->title}}</a>
                                            </h3>
                                            <div class="course-button">
                                                <a href="{{ route('course.details', $course->slug) }}">Xem chi tiết <i
                                                        class="fa-sharp fa-regular fa-arrow-right"></i></a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        @endif
                    </div>
                </div>

                <div class="row">
                    <div class="col-12 col-md-12">
                        <div class="d-flex justify-content-center">
                            <div class="line-down">
                                <svg xmlns="http://www.w3.org/2000/svg" height="100%" viewBox="0 0 960 960" width="100%"
                                     preserveAspectRatio="none" class="" fill="rgba(237, 246, 255, 1)">
                                    <use xlink:href="#shape_DijKrreWIZ"></use>
                                </svg>
                            </div>
                            <div class="line-down">
                                <svg xmlns="http://www.w3.org/2000/svg" height="100%" viewBox="0 0 960 960" width="100%"
                                     preserveAspectRatio="none" class="" fill="rgba(237, 246, 255, 1)">
                                    <use xlink:href="#shape_DijKrreWIZ"></use>
                                </svg>
                            </div>
                            <div class="line-down">
                                <svg xmlns="http://www.w3.org/2000/svg" height="100%" viewBox="0 0 960 960" width="100%"
                                     preserveAspectRatio="none" class="" fill="rgba(237, 246, 255, 1)">
                                    <use xlink:href="#shape_DijKrreWIZ"></use>
                                </svg>
                            </div>
                            <div class="line-down">
                                <svg xmlns="http://www.w3.org/2000/svg" height="100%" viewBox="0 0 960 960" width="100%"
                                     preserveAspectRatio="none" class="" fill="rgba(237, 246, 255, 1)">
                                    <use xlink:href="#shape_DijKrreWIZ"></use>
                                </svg>
                            </div>
                            <div class="line-down">
                                <svg xmlns="http://www.w3.org/2000/svg" height="100%" viewBox="0 0 960 960" width="100%"
                                     preserveAspectRatio="none" class="" fill="rgba(237, 246, 255, 1)">
                                    <use xlink:href="#shape_DijKrreWIZ"></use>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="line-bottom"></section>

        <section class="section sec-document home-document" id="sec-document">
            <div class="container">
                <div class="row">
                    <div class="col-12 col-md-12 col-lg-12">
                        <div class="home-title text-center notline">
                            <h3 class="font-46">Điểm đặc biệt của các khóa học</h3>
                            <div class="home-line">
                                <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                     preserveAspectRatio="none" viewBox="0 0 422.41 34.48" class="" fill="#FDDD1E">
                                    <use xlink:href="#shape_LDdVChIGcB"></use>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-md-10 col-lg-10 col-xl-10 offset-md-1 offset-lg-1">
                        <div class="row">
                            <div class="col-12 col-md-6 col-lg-6 col-xl-6">
                                <div class="document-item">
                                    <div class="document-icon">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                             preserveAspectRatio="none" viewBox="0 0 1085.3815 1896.0833" class=""
                                             fill="rgba(254, 222, 30, 1)">
                                            <path d="M736 576q0 13-9.5 22.5T704 608t-22.5-9.5T672 576q0-46-54-71t-106-25q-13 0-22.5-9.5T480 448t9.5-22.5T512 416q50 0 99.5 16t87 54 37.5 90zm160 0q0-72-34.5-134t-90-101.5-123-62T512 256t-136.5 22.5-123 62-90 101.5T128 576q0 101 68 180 10 11 30.5 33t30.5 33q128 153 141 298h228q13-145 141-298 10-11 30.5-33t30.5-33q68-79 68-180zm128 0q0 155-103 268-45 49-74.5 87t-59.5 95.5-34 107.5q47 28 47 82 0 37-25 64 25 27 25 64 0 52-45 81 13 23 13 47 0 46-31.5 71t-77.5 25q-20 44-60 70t-87 26-87-26-60-70q-46 0-77.5-25t-31.5-71q0-24 13-47-45-29-45-81 0-37 25-64-25-27-25-64 0-54 47-82-4-50-34-107.5T177.5 931 103 844Q0 731 0 576q0-99 44.5-184.5t117-142 164-89T512 128t186.5 32.5 164 89 117 142T1024 576z"></path>
                                        </svg>
                                    </div>
                                    <div class="document-text">
                                        <h4>Phương pháp độc đáo</h4>
                                        <p>Mỗi nội dung đều được nghiên cứu kỹ lưỡng, xây dựng dựa trên nền tảng <span
                                                style="font-weight: bold;">khoa học trí nhớ</span> và <span
                                                style="font-weight: bold;">sáng tạo</span> không ngừng.<br></p>
                                    </div>
                                </div>
                                <div class="document-item">
                                    <div class="document-icon">
                                        <svg style="width: 47px;height: auto" xmlns="http://www.w3.org/2000/svg"
                                             width="47px" height="58px"
                                             preserveAspectRatio="none" viewBox="0 0 1536 1896.0833" class=""
                                             fill="rgba(254, 222, 30, 1)">
                                            <path d="M363 1536l91-91-235-235-91 91v107h128v128h107zm523-928q0-22-22-22-10 0-17 7l-542 542q-7 7-7 17 0 22 22 22 10 0 17-7l542-542q7-7 7-17zm-54-192l416 416-832 832H0v-416zm683 96q0 53-37 90l-166 166-416-416 166-165q36-38 90-38 53 0 91 38l235 234q37 39 37 91z"></path>
                                        </svg>
                                    </div>
                                    <div class="document-text">
                                        <h4>Minh họa sinh động</h4>
                                        <p>Giúp kiến thức <span style="font-weight: bold;">“bám rễ”</span> vào trí nhớ
                                            tự
                                            nhiên, giảm học vẹt, tăng hứng thú.<br></p>
                                    </div>
                                </div>
                                <div class="document-item">
                                    <div class="document-icon">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="51px" height="51px"
                                             preserveAspectRatio="none" viewBox="0 0 24 24" class=""
                                             fill="rgba(254, 222, 30, 1)">
                                            <path d="M12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M16.24,7.76C15.07,6.58 13.53,6 12,6V12L7.76,16.24C10.1,18.58 13.9,18.58 16.24,16.24C18.59,13.9 18.59,10.1 16.24,7.76Z"></path>
                                        </svg>
                                    </div>
                                    <div class="document-text">
                                        <h4>Tính ứng dụng cao</h4>
                                        <p>Học viên sẽ được hướng dẫn áp dụng kiến thức đã học vào xử lý những  <span
                                                style="font-weight: bold;">tình huống thực tế, cụ thể.</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 col-lg-6 col-xl-6">
                                <div class="document-item">
                                    <div class="document-icon">
                                        <i class="fa-solid fa-arrows-rotate"></i>
                                    </div>
                                    <div class="document-text">
                                        <h4>Cập nhật liên tục</h4>
                                        <p>Nội dung các khóa học sẽ được <span
                                                style="font-weight: bold;">liên tục cập nhật</span> theo thời gian.</p>
                                    </div>
                                </div>
                                <div class="document-item">
                                    <div class="document-icon">
                                        <svg style="width: 70px;height: auto" xmlns="http://www.w3.org/2000/svg"
                                             height="100%" viewBox="0 -960 960 960"
                                             width="100%" preserveAspectRatio="none" class=""
                                             fill="rgba(254, 222, 30, 1)">
                                            <path d="M0-240v-53q0-38.57 41.5-62.78Q83-380 150.38-380q12.16 0 23.39.5t22.23 2.15q-8 17.35-12 35.17-4 17.81-4 37.18v65H0Zm240 0v-65q0-32 17.5-58.5T307-410q32-20 76.5-30t96.5-10q53 0 97.5 10t76.5 30q32 20 49 46.5t17 58.5v65H240Zm540 0v-65q0-19.86-3.5-37.43T765-377.27q11-1.73 22.17-2.23 11.17-.5 22.83-.5 67.5 0 108.75 23.77T960-293v53H780ZM149.57-410q-28.57 0-49.07-20.56Q80-451.13 80-480q0-29 20.56-49.5Q121.13-550 150-550q29 0 49.5 20.5t20.5 49.93q0 28.57-20.5 49.07T149.57-410Zm660 0q-28.57 0-49.07-20.56Q740-451.13 740-480q0-29 20.56-49.5Q781.13-550 810-550q29 0 49.5 20.5t20.5 49.93q0 28.57-20.5 49.07T809.57-410ZM480-480q-50 0-85-35t-35-85q0-51 35-85.5t85-34.5q51 0 85.5 34.5T600-600q0 50-34.5 85T480-480Z"></path>
                                        </svg>
                                    </div>
                                    <div class="document-text">
                                        <h4>Hỗ trợ tận tình</h4>
                                        <p>Tất cả những câu hỏi của bạn đều được chúng mình lắng nghe và giải đáp
                                            <b>24/7.</b></p>
                                    </div>
                                </div>

                                <div class="dx document-dx">
                                    <img src="{{ asset('assets/frontend/dau-xanh/assets/images/dx-ngoi-ghe.png')}}" alt="">
                                    <div class="document-muiten">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                             preserveAspectRatio="none" viewBox="0 0 300 300"
                                             fill="rgba(5, 147, 216, 1)"
                                             class="">
                                            <path d="M284.7,122.4l-3.7-6l-9.3-15.2c-8.7-14.3-16.6-20.1-16.6-20.1s1.6,9.7,10.4,23.9l9.3,15.2l1.7,2.8c-5.2-4.4-12.9-10.5-24.2-16.7c-5.9-3.2-12.9-6.4-21.1-8.7c-8.1-2.3-17.6-3.7-27.8-2.5c-10.1,1.2-20.8,4.5-31.1,10.1c-5.4,3-10.8,6.6-15.8,10.9c-0.3-0.5-0.6-0.9-0.9-1.4c-2.7-3.7-5.9-7.5-9.9-11c-4.1-3.5-9.1-6.7-15.2-8.4c-6.1-1.7-13-1.8-19.7-0.1c-6.7,1.7-12.7,4.7-18,8c-5.3,3.4-10,7.1-14.4,11c-8.6,7.7-15.7,15.8-21.9,23.5c-12.3,15.5-21.1,29.7-27.8,41.2c-13.4,23.2-18.5,36.2-18.6,36.1c0.1,0,6.9-12.1,21.7-34.2c7.5-11,16.9-24.5,29.5-39.2c6.3-7.3,13.4-15,21.7-22.2c4.2-3.6,8.7-7,13.6-10.1c4.9-3,10.2-5.6,15.9-7c5.7-1.4,11.1-1.4,16,0c4.8,1.4,9,3.9,12.5,6.9c4.2,3.6,7.5,7.7,10.2,11.8c-2.5,2.6-4.9,5.3-7.2,8.2c-8.1,10.6-12.7,21.9-14.7,32.1c-2.1,10.3-1.8,19.5-0.7,27.2c1,7.7,3.3,14.5,6.8,19.6c3.4,5.1,8.4,8.2,12.8,9.1c4.5,0.9,8,0.1,10.4-0.7c2.5-0.9,4-1.9,5.1-2.6c2.1-1.5,2.4-1.8,2.4-1.8c5.9-5.1,9.3-12,11.2-18.9c1.9-7,2.3-14.2,1.7-21.2l-0.3-2.7c-0.3-2.3-0.8-6.5-2.8-13.7c-2-7.2-5.7-17.4-13.6-30.5c-0.6-1-1.2-1.9-1.8-2.9c4.8-4.3,9.9-7.9,15.2-10.9c9.4-5.4,19.2-8.7,28.5-10.1c9.2-1.4,18-0.6,25.8,1.2c7.8,1.8,14.7,4.4,20.7,7.1c12,5.5,20.4,11.1,26,15c0.4,0.3,0.8,0.6,1.2,0.8l-7,1.1c-16.5,2.6-24.9,7.6-24.9,7.6s9.6,2.2,26.1-0.3L290,131L284.7,122.4z M168.8,157.8c1.9,6.7,2.3,10.5,2.6,12.5l0.3,2.5c0.5,6.3,0.2,12.7-1.4,18.7c-1.6,5.9-4.6,11.5-8.9,15.2c0,0-0.2,0.3-1.7,1.3c-0.8,0.5-1.9,1.2-3.5,1.8c-1.6,0.5-3.8,1-6.4,0.5c-2.6-0.5-5.7-2.3-8.3-6.1c-2.6-3.8-4.6-9.3-5.6-16.4c-1-7.1-1.3-15.4,0.6-24.7c1.9-9.2,6-19.4,13.4-29.1c1.7-2.2,3.5-4.3,5.4-6.3c0.3,0.5,0.6,1,0.9,1.4C163.4,141.6,166.9,151.1,168.8,157.8z"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section><!--sec-document-->

        <section class="section sec-author home-author" id="sec-author" style="background: rgb(1, 133, 197)">
            <div class="container">
                <div class="row">
                    <div class="col-12 col-md-12 col-lg-12">
                        <div class="home-title text-center notline">
                            <h3 class="font-46"
                                style="color:#fff;background: transparent;-webkit-text-fill-color:unset">
                                <span style="color: rgb(254, 222, 30);background: transparent;-webkit-text-fill-color:unset">Tác giả</span>
                                Khóa Học</h3>
                            <div class="home-line">
                                <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                     preserveAspectRatio="none" viewBox="0 0 422.41 34.48" class="" fill="#FDDD1E">
                                    <use xlink:href="#shape_LDdVChIGcB"></use>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-md-12 col-lg-12">
                        <div class="box-author">
                            <div class="row align-items-end">
                                <div class="col-12 col-md-9 col-lg-9">
                                    <div class="author-left author-lf-1">
                                        <div class="row align-items-end">
                                            <div class="col-12 col-md-4 col-lg-4">
                                                <div class="author-thumbnail">
                                                    <img src="{{ asset('assets/frontend/dau-xanh/assets/images/ngoc-nho.png')}}" alt="">
                                                </div>
                                            </div>
                                            <div class="col-12 col-md-8 col-lg-8">
                                                <div class="author-content">
                                                    <span style="font-weight: bold;">Nguyễn Thị Minh Ngọc</span><br>✨
                                                    <span
                                                        style="font-weight: bold;">Giải Nhất</span> Tuần cuộc thi
                                                    Đường lên
                                                    đỉnh
                                                    Olympia năm thứ 15.<br>✨ Tốt nghiệp chuyên ngành Bác sĩ Đa Khoa <br>&nbsp;
                                                    &nbsp; &nbsp;- Đại học Y Hà Nội.<br>✨ Chứng chỉ IELTS
                                                    7.5&nbsp;<br><span
                                                        style="font-weight: bold;">✨Giáo viên IELTS</span> tại Edupia
                                                    IELTS.<br>
                                                </div>
                                                <div class="author-cloud2 d-block d-md-block d-lg-block d-xl-block">
                                                    <div class="author-mini d-md-block d-lg-none">
                                                        <img src="{{ asset('assets/frontend/dau-xanh/assets/images/ngoc-min2.jpg')}}" alt="">
                                                    </div>
                                                    <div class="cloud d-md-block d-lg-none">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                                             preserveAspectRatio="none" viewBox="0 0 249.8 79.6" class=""
                                                             fill="#FEDE1E">
                                                            <path d="M249.8,79.6c-1.7-42.9-28.8-46.2-48.7-40-3.1,1-7.2.9-7.6-3.4-1.6-17.3-10.6-28.8-27.7-31S139.2,14.6,131,28.1c-7-9.9-13.1-19.3-24.4-24.4C81.1-7.8,57.5,9.3,51.4,28.6c-2.7,8.5-4.4,12.7-15.6,9.9C13.8,32.9-.2,52,0,79.6"></path>
                                                        </svg>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-3 col-lg-3">
                                    <div class="author-cloud">
                                        <img src="{{ asset('assets/frontend/dau-xanh/assets/images/khoai2.png')}}" alt="">
                                        <div class="author-mini d-none d-md-block d-lg-block">
                                            <img src="{{ asset('assets/frontend/dau-xanh/assets/images/ngoc-min2.jpg')}}" alt="">
                                        </div>
                                        <div class="cloud d-none d-md-block d-lg-block">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                                 preserveAspectRatio="none" viewBox="0 0 249.8 79.6" class=""
                                                 fill="#FEDE1E">
                                                <path d="M249.8,79.6c-1.7-42.9-28.8-46.2-48.7-40-3.1,1-7.2.9-7.6-3.4-1.6-17.3-10.6-28.8-27.7-31S139.2,14.6,131,28.1c-7-9.9-13.1-19.3-24.4-24.4C81.1-7.8,57.5,9.3,51.4,28.6c-2.7,8.5-4.4,12.7-15.6,9.9C13.8,32.9-.2,52,0,79.6"></path>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-md-12 col-lg-12 mt-100">
                        <div class="box-author">
                            <div class="row align-items-end">
                                <div class="col-12 col-md-3 col-lg-3">
                                    <div class="author-cloud text-right">
                                        <img src="{{ asset('assets/frontend/dau-xanh/assets/images/r_rqu.png')}}" alt="">
                                    </div>
                                </div>
                                <div class="col-12 col-md-9 col-lg-9">
                                    <div class="author-left authorRight">
                                        <div class="row align-items-end">
                                            <div class="col-12 col-md-8 col-lg-8">
                                                <div class="author-content author-right">
                                                    <span style="font-weight: bold;">Phạm Văn Trường</span><br>✨
                                                    Tốt nghiệp chuyên ngành Bác Sĩ Đa Khoa - Đại học Y Hà Nội.<br>✨
                                                    Chứng chỉ <span style="font-weight: bold;">IELTS 8.0</span><br>✨
                                                    Admin Fanpage và kênh Youtube <span
                                                        style="font-weight: bold;">Đậu Xanh.</span><br>
                                                    <div class="author-social">
                                                        <div class="social-item">
                                                            <a href="https://www.facebook.com/dauxanh.82">
                                                                <div class="social-icon">
                                                                    <svg xmlns="http://www.w3.org/2000/svg" width="100%"
                                                                         height="100%" preserveAspectRatio="none"
                                                                         viewBox="0 0 1536 1896.0833" class=""
                                                                         fill="rgba(255, 255, 255, 1)">
                                                                        <path d="M1248 128q119 0 203.5 84.5T1536 416v960q0 119-84.5 203.5T1248 1664h-188v-595h199l30-232h-229V689q0-56 23.5-84t91.5-28l122-1V369q-63-9-178-9-136 0-217.5 80T820 666v171H620v232h200v595H288q-119 0-203.5-84.5T0 1376V416q0-119 84.5-203.5T288 128h960z"></path>
                                                                    </svg>
                                                                </div>
                                                                <div class="social-content">
                                                                    <h3>108,2k+<br>Người theo dõi<br></h3>
                                                                </div>
                                                            </a>
                                                        </div>
                                                        <div class="social-item">
                                                            <a href="https://www.youtube.com/@dauxanh.82">
                                                                <div class="social-icon">
                                                                    <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none" viewBox="0 0 1792 1896.08" class="" fill="rgba(255, 255, 255, 1)"><use xlink:href="#shape_NgkhYfyuyy"></use></svg>
                                                                </div>
                                                                <div class="social-content">
                                                                    <h3>34,5k+<br>Người theo dõi<br></h3>
                                                                </div>
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-12 col-md-4 col-lg-4">
                                                <div class="author-thumbnail">
                                                    <img src="{{ asset('assets/frontend/dau-xanh/assets/images/truong.webp')}}" alt="">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section><!--sec-author-->

        <section class="section sec-student" id="sec-student">
            <div class="container">
                <div class="row">
                    <div class="col-12 col-md-12 col-lg-12">
                        <div class="home-title text-center notline">
                            <h3 class="font-46">Học viên nói gì về Đậu Xanh</h3>
                            <div class="home-line">
                                <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                     preserveAspectRatio="none" viewBox="0 0 422.41 34.48" class="" fill="#FDDD1E">
                                    <use xlink:href="#shape_LDdVChIGcB"></use>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-md-12 col-lg-12" style="position: relative">
                        <div class="swiper-container">
                            <div class="swiper swiper-student">
                                <div class="swiper-wrapper">
                                    <div class="swiper-slide">
                                        <div class="student">
                                            <div class="student-content">Em thật sự rất biết ơn Đậu Xanh, biết ơn vì
                                                Kênh đã
                                                tạo ra một món quà quá đỗi giá trị trên hành trình học Ielts cho một đứa
                                                nước ngập đầu mới bơi như em, tấm bằng ielts mở ra cho em hành trình
                                                mới, Em
                                                đang apply Thạc Sĩ chuyên ngành em thích tại đất nước em yêu, hi vọng
                                                mọi
                                                thứ sẽ thuận lợi
                                                <div class="student-quote">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                                         preserveAspectRatio="none" viewBox="0 0 24 24" class=""
                                                         fill="rgba(255, 156, 32, 1)">
                                                        <use xlink:href="#shape_fzqkZfSHTA"></use>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="student-author">
                                                <div class="student-thumbnail">
                                                    <img src="{{ asset('assets/frontend/dau-xanh/assets/images/giang-20250519084012-22hyy.jpg')}}" alt="">
                                                </div>
                                                <div class="student-info">
                                                    <h4>Bạn Thu Giang</h4>
                                                    <p>Nghiên cứu sinh Thạc sĩ, Đại học Nhân dân Trung Quốc</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="student">
                                            <div class="student-content">Lần đầu, em tiếp xúc với cách học này, rất thú
                                                vị
                                                và nhớ lâu hơn. Em không biết anh chị tâm huyết cỡ nào mà dàn dựng lên
                                                khóa
                                                học đỉnh vậy luôn á.
                                                <div class="student-quote">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                                         preserveAspectRatio="none" viewBox="0 0 24 24" class=""
                                                         fill="rgba(255, 156, 32, 1)">
                                                        <use xlink:href="#shape_fzqkZfSHTA"></use>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="student-author">
                                                <div class="student-thumbnail">
                                                    <img src="{{ asset('assets/frontend/dau-xanh/assets/images/kim-oanh-min-20250519091735-ek-ul.png')}}"
                                                         alt="">
                                                </div>
                                                <div class="student-info">
                                                    <h4>Bạn Kim Oanh</h4>
                                                    <p>Sinh viên Đại học Y dược Hải Phòng</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="student">
                                            <div class="student-content">Cá nhân tớ thấy rất thích học theo phương pháp
                                                này
                                                vì tớ cũng là kiểu người ghi nhớ hình ảnh tốt hơn so với kiểu học chay
                                                thuần.<br>
                                                Tớ chỉ xem video có một lần không hề đọc lại nhưng qua mấy tuần rồi vẫn
                                                nhớ.
                                                <div class="student-quote">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                                         preserveAspectRatio="none" viewBox="0 0 24 24" class=""
                                                         fill="rgba(255, 156, 32, 1)">
                                                        <use xlink:href="#shape_fzqkZfSHTA"></use>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="student-author">
                                                <div class="student-thumbnail">
                                                    <img src="{{ asset('assets/frontend/dau-xanh/assets/images/lien-min-20250519162701-uo0ij.jpg')}}" alt="">
                                                </div>
                                                <div class="student-info">
                                                    <h4>Bạn Bích Liên</h4>
                                                    <p>Bác sĩ Gây mê hồi sức - Bv Đại học Y Hà Nội</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="student">
                                            <div class="student-content">Hấp dẫn ngoài sức tưởng tượng của em luôn.<br>
                                                Như kiểu con ếch vừa được nhảy ra khỏi cái giếng của mình.
                                                <div class="student-quote">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                                         preserveAspectRatio="none" viewBox="0 0 24 24" class=""
                                                         fill="rgba(255, 156, 32, 1)">
                                                        <use xlink:href="#shape_fzqkZfSHTA"></use>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="student-author">
                                                <div class="student-thumbnail">
                                                    <img src="{{ asset('assets/frontend/dau-xanh/assets/images/nam-min-20250519163224-zuhio.jpg')}}" alt="">
                                                </div>
                                                <div class="student-info">
                                                    <h4>Bạn Hữu Nam</h4>
                                                    <p>Sinh viên Đại học FPT</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="student">
                                            <div class="student-content">Rất cảm ơn tác giả đã tạo ra một khóa học rất
                                                thú
                                                vị, hiệu quả. Học bài thứ nhất lại muốn xem tiếp bài tiếp theo.<br>
                                                Sự tỉ mỉ, đầu tư của tác giả cũng phần nào đó tạo động lực và sự khâm
                                                phục
                                                bởi sự tâm huyết của tác giả đối với khóa học.
                                                <div class="student-quote">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                                         preserveAspectRatio="none" viewBox="0 0 24 24" class=""
                                                         fill="rgba(255, 156, 32, 1)">
                                                        <use xlink:href="#shape_fzqkZfSHTA"></use>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="student-author">
                                                <div class="student-thumbnail">
                                                    <img src="{{ asset('assets/frontend/dau-xanh/assets/images/a-duc-min-20250519093322-zombj.jpg')}}" alt="">
                                                </div>
                                                <div class="student-info">
                                                    <h4>Anh Xuân Đức</h4>
                                                    <p>Viện Khoa học Thủy lợi Việt Nam</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="student">
                                            <div class="student-content">Em thực sự cảm thấy may mắn khi được biết đến
                                                khóa
                                                học. Em cảm nhận rất rõ sự tâm huyết của anh chị qua từng video nhường
                                                nào.<br>
                                                Mong rằng khóa học sẽ được nhiều người biết đến hơn vì thật sự cách
                                                giảng
                                                dạy này là một điểm sáng mà không ở khóa học nào có ạ.
                                                <div class="student-quote">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                                         preserveAspectRatio="none" viewBox="0 0 24 24" class=""
                                                         fill="rgba(255, 156, 32, 1)">
                                                        <use xlink:href="#shape_fzqkZfSHTA"></use>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="student-author">
                                                <div class="student-thumbnail">
                                                    <img src="{{ asset('assets/frontend/dau-xanh/assets/images/screenshot-2025-05-19-160103-20250519090432-xxjk-.png')}}"
                                                         alt="">
                                                </div>
                                                <div class="student-info">
                                                    <h4>Bạn Minh Tâm</h4>
                                                    <p>Sinh viên Đại học Ngoại ngữ, ĐHQG Hà Nội</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="student">
                                            <div class="student-content">Dear tổng công ty trách nhiệm hữu hạn 2 thành
                                                viên
                                                Đậu Xanh.<br>
                                                Cảm ơn anh chị đã tâm huyết xây dựng các video rất chỉn chu về nội dung
                                                hình
                                                ảnh. Học bằng hình ảnh thực sự dễ nhớ hơn rất nhiều so với cách học
                                                truyền
                                                thống.
                                                <div class="student-quote">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                                         preserveAspectRatio="none" viewBox="0 0 24 24" class=""
                                                         fill="rgba(255, 156, 32, 1)">
                                                        <use xlink:href="#shape_fzqkZfSHTA"></use>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="student-author">
                                                <div class="student-thumbnail">
                                                    <img src="{{ asset('assets/frontend/dau-xanh/assets/images/hong-min-20250519160547-kxk8c.jpg')}}" alt="">
                                                </div>
                                                <div class="student-info">
                                                    <h4>Bạn Minh Hồng</h4>
                                                    <p>Giáo viên môn Sinh học</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="student">
                                            <div class="student-content">Video thú vị với dễ nhớ lắm chị. Có lúc em xem
                                                1
                                                lèo hết 4 video luôn. Kiểu cứ xem xong lại muốn xem tiếp í ạ.
                                                <div class="student-quote">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                                         preserveAspectRatio="none" viewBox="0 0 24 24" class=""
                                                         fill="rgba(255, 156, 32, 1)">
                                                        <use xlink:href="#shape_fzqkZfSHTA"></use>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="student-author">
                                                <div class="student-thumbnail">
                                                    <img src="{{ asset('assets/frontend/dau-xanh/assets/images/hien-thuong-20250519162006-yunw8.jpg')}}"
                                                         alt="">
                                                </div>
                                                <div class="student-info">
                                                    <h4>Bạn Hiền Thương</h4>
                                                    <p>Sinh viên Y khoa - ĐH Y Dược Đà Nẵng</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="student">
                                            <div class="student-content">Anh chị hỗ trợ rất nhiệt tình. Em cứ nghĩ lúc
                                                em tư
                                                vấn mua thì mới được nhiệt tình dị, còn mua khóa học rồi thì anh chị mất
                                                tích luôn chứ ạ. Nhưng không mua rồi anh chị còn nhiệt tình hơn nữa ạ
                                                <div class="student-quote">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                                         preserveAspectRatio="none" viewBox="0 0 24 24" class=""
                                                         fill="rgba(255, 156, 32, 1)">
                                                        <use xlink:href="#shape_fzqkZfSHTA"></use>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="student-author">
                                                <div class="student-thumbnail">
                                                    <img src="{{ asset('assets/frontend/dau-xanh/assets/images/yu88sno120250519160945.jpg')}}" alt="">
                                                </div>
                                                <div class="student-info">
                                                    <h4>Bạn Nguyễn Mạnh</h4>
                                                    <p>Sinh viên Đại học</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="swiper-nav">
                                <div class="swiper-button-prev swiper-review-prev"><span></span></div>
                                <div class="swiper-button-next swiper-review-next"><span></span></div>
                            </div>
                        </div>
                        <div class="chimcumeo">
                            <svg xmlns="http://www.w3.org/2000/svg" height="100%" viewBox="0 -960 960 960" width="100%"
                                 preserveAspectRatio="none" class="" fill="rgba(72, 188, 244, 1)">
                                <path d="M295-320q-3-4 19.5-31.5T366-410q33-37 77-82 12-18 33-18t35 16q12 16 9 36.5T498-428q-55 33-98 58-38 21-70 37.5T295-320ZM430-40q-5 0-10-2t-8-7q-4-6-8-16.5T400-90q0-9 1-16.5t3-15.5q-27-5-45.5-27T340-200q0-4 3-21-74-26-119.5-83T165-451q-5 5-11 8t-14 3q-15 0-37.5-22.5T80-560q0-69 40.5-124.5T200-740q13 0 16.5 11.5T220-706q38-54 95.5-89T442-838q6-29 29-45.5t49-16.5v40q4-6 10-10 5-4 12.5-7t17.5-3q10 0 17.5 3t12.5 7q6 4 10 10-15 0-25 9t-13 22q54 14 100 46t78 77q0-11 3.5-22.5T760-740q39 0 79.5 55.5T880-560q0 75-22.5 97.5T820-440q-7 0-13.5-2.5T795-450q-14 100-68.5 159.5T582-211q3 14 13.5 22.5T620-180h23q7 0 12 4t7 10q5 16 17.5 30t23.5 23q8 7 7.5 16.5T702-82q-11 8-28 14t-44 8q-5 0-10-2t-8-7q-4-6-8-16.5t-4-24.5q0-9 1-16.5t3-15.5q-24-5-40.5-22T542-204q-15 2-30 3t-32 1q-26 0-50.5-2.5T382-210l-2 10q0 17 11.5 28.5T420-160h23q7 0 12 4t7 10q5 16 17.5 30T503-93q8 7 7.5 16.5T502-62q-11 8-28 14t-44 8Zm320-391q5-20 7.5-42t2.5-47q0-116-82-198t-198-82q-116 0-198 82t-82 198q0 24 2.5 46t7.5 42q5-20 13.5-39.5T243-508q-6-14-9.5-29.5T230-570q0-63 43.5-106.5T380-720q29 0 54.5 10.5T480-681q20-18 45.5-28.5T580-720q63 0 106.5 43.5T730-570q0 17-3.5 32.5T716-508q11 17 20 36.5t14 40.5ZM480-240q87 0 146.5-27t92.5-81q0-3 .5-6t.5-6q0-30-7-58t-21-53q-21 23-49.5 37T580-420q-8 0-16-1t-16-3q5-7 7.5-14.5T559-454q1-2 1-4v-4q5 1 10 1.5t10 .5q46 0 78-32t32-78q0-46-32-78t-78-32q-20 0-39 7.5T507-651l-27 24-27-24q-15-14-34-21.5t-39-7.5q-46 0-78 32t-32 78q0 41 25.5 70.5T359-462l-30 33q-18-7-33.5-17.5T268-471q-14 25-21 53t-7 58v10q34 54 94 82t146 28Zm100-260q-25 0-42.5-20.5T520-570q0-29 17.5-49.5T580-640q25 0 42.5 20.5T640-570q0 29-17.5 49.5T580-500Zm15-90q7 0 11-4.5t4-10.5q0-6-4.5-10.5T595-620q-7 0-11 4.5t-4 10.5q0 7 4.5 11t10.5 4Zm-215 90q-25 0-42.5-20.5T320-570q0-29 17.5-49.5T380-640q25 0 42.5 20.5T440-570q0 29-17.5 49.5T380-500Zm15-90q7 0 11-4.5t4-10.5q0-6-4.5-10.5T395-620q-7 0-11 4.5t-4 10.5q0 7 4.5 11t10.5 4Z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer id="footer">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-12 col-md-5 col-ld-5 col-xl-5 mt-footer">
                    <div class="footer-top">
                        <div class="footer-logo">
                            <a href="/">
                                <img src="{{ asset('assets/frontend/dau-xanh/assets/images/logo.png') }}" alt="Logo">
                            </a>
                            <h3 class="company">Đậu Xanh Edu</h3>
                        </div>
                        <div class="footer-address">
                            <p class="address-item">
                                <a href="mailto:<EMAIL>">
                                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                         width="100%" height="100%" viewBox="0 0 24 24" fill="rgba(255,255,255,1)">
                                        <path d="M20,4H4A2,2 0 0,0 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6A2,2 0 0,0 20,4M20,18H4V8L12,13L20,8V18M20,6L12,11L4,6V6H20V6Z"></path>
                                    </svg>
                                    Email: <EMAIL>
                                </a>
                            </p>
                            <p class="address-item">
                                <a href="/">
                                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                         width="100%" height="100%" viewBox="0 0 24 24" fill="rgba(255,255,255,1)">
                                        <path d="M16.36,14C16.44,13.34 16.5,12.68 16.5,12C16.5,11.32 16.44,10.66 16.36,10H19.74C19.9,10.64 20,11.31 20,12C20,12.69 19.9,13.36 19.74,14M14.59,19.56C15.19,18.45 15.65,17.25 15.97,16H18.92C17.96,17.65 16.43,18.93 14.59,19.56M14.34,14H9.66C9.56,13.34 9.5,12.68 9.5,12C9.5,11.32 9.56,10.65 9.66,10H14.34C14.43,10.65 14.5,11.32 14.5,12C14.5,12.68 14.43,13.34 14.34,14M12,19.96C11.17,18.76 10.5,17.43 10.09,16H13.91C13.5,17.43 12.83,18.76 12,19.96M8,8H5.08C6.03,6.34 7.57,5.06 9.4,4.44C8.8,5.55 8.35,6.75 8,8M5.08,16H8C8.35,17.25 8.8,18.45 9.4,19.56C7.57,18.93 6.03,17.65 5.08,16M4.26,14C4.1,13.36 4,12.69 4,12C4,11.31 4.1,10.64 4.26,10H7.64C7.56,10.66 7.5,11.32 7.5,12C7.5,12.68 7.56,13.34 7.64,14M12,4.03C12.83,5.23 13.5,6.57 13.91,8H10.09C10.5,6.57 11.17,5.23 12,4.03M18.92,8H15.97C15.65,6.75 15.19,5.55 14.59,4.44C16.43,5.07 17.96,6.34 18.92,8M12,2C6.47,2 2,6.5 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"></path>
                                    </svg>
                                    Website: http://dauxanh.edu.vn
                                </a>
                            </p>
                        </div>
                        <div class="footer-social">
                            <a href="https://facebook.com/dauxanh.82" target="_blank"
                               rel="nofollow">
                                <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                     preserveAspectRatio="none" viewBox="0 0 24 24" class="" fill="rgba(255, 255, 255, 1)">
                                    <path d="M19,4V7H17A1,1 0 0,0 16,8V10H19V13H16V20H13V13H11V10H13V7.5C13,5.56 14.57,4 16.5,4M20,2H4A2,2 0 0,0 2,4V20A2,2 0 0,0 4,22H20A2,2 0 0,0 22,20V4C22,2.89 21.1,2 20,2Z"></path>
                                </svg>
                            </a>
                            <a href="https://youtube.com/@dauxanh.82" target="_blank"
                               rel="nofollow">

                                <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                     preserveAspectRatio="none" viewBox="0 0 1792 1896.08" class=""
                                     fill="rgba(255, 255, 255, 1)">
                                    <use xlink:href="#shape_NgkhYfyuyy"></use>
                                </svg>
                            </a>
                        </div>
                        <div class="footer-dmca">
                            <a href="">
                                <img src="{{ asset('assets/frontend/dau-xanh/assets/images/dmca.png') }}" alt="">
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-5 col-ld-5 col-xl-5 mt-footer">
                    <div class="footer-top">
                        <div id="fb-root"></div>
                        <script async defer crossorigin="anonymous"
                                src="https://connect.facebook.net/vi_VN/sdk.js#xfbml=1&version=v22.0&appId=811114293101209"></script>
                        <div class="fb-page" data-href="https://www.facebook.com/dauxanh.82" data-tabs="timeline"
                             data-width="" data-height="" data-small-header="false" data-adapt-container-width="true"
                             data-hide-cover="false" data-show-facepile="true">
                            <blockquote cite="https://www.facebook.com/dauxanh.82" class="fb-xfbml-parse-ignore"><a
                                    href="https://www.facebook.com/dauxanh.82">Đậu Xanh</a></blockquote>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-10 col-lg-10 col-xl-10">
                    <div class="footer-copyright">© 2024 Toàn bộ bản quyền thuộc www.dauxanh.edu.vn</div>
                </div>
            </div>
        </div>
    </footer>
    @include('partials.modals.register_login')

    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <div id="button-contact-vr">
        <div id="gom-all-in-one">
            <div id="fanpage-vr" class="button-contact">
                <div class="phone-vr">
                    <div class="phone-vr-circle-fill"></div>
                    <div class="phone-vr-img-circle">
                        <a target="_blank" href="https://m.me/106866188274988">
                            <img alt="Fanpage" src="{{ asset('assets/frontend/dau-xanh/assets/images/messenger.svg') }}">
                        </a>
                    </div>
                </div>
            </div>

            <div id="zalo-vr" class="button-contact">
                <div class="phone-vr">
                    <div class="phone-vr-circle-fill"></div>
                    <div class="phone-vr-img-circle">
                        <a target="_blank" href="https://zalo.me/0962017973">
                            <img alt="Zalo" src="{{ asset('assets/frontend/dau-xanh/assets/images/zalo.svg') }}">
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div><!--button-contact-vr-->

    <svg xmlns="http://www.w3.org/2000/svg"
         style="width: 0px; height: 0px; position: absolute; overflow: hidden; display: none;">
        <symbol id="shape_LDdVChIGcB" viewBox="0 0 422.41 34.48">
            <path d="M3.49,7c7.6,0,11.49,5.67,16,12.23C24.4,26.37,30,34.48,41.25,34.48S58.09,26.37,63,19.21C67.51,12.65,71.41,7,79,7s11.5,5.67,16,12.23c4.91,7.16,10.47,15.27,21.75,15.27s16.84-8.11,21.76-15.27C143,12.65,146.93,7,154.53,7s11.5,5.67,16,12.23c4.91,7.16,10.48,15.27,21.75,15.27s16.85-8.11,21.76-15.27C218.55,12.65,222.44,7,230,7s11.5,5.67,16,12.23c4.91,7.16,10.48,15.27,21.76,15.27s16.85-8.11,21.76-15.27C294.08,12.65,298,7,305.58,7s11.5,5.67,16,12.23c4.92,7.16,10.48,15.27,21.76,15.27s16.85-8.11,21.77-15.27c4.51-6.56,8.41-12.23,16-12.23s11.5,5.67,16,12.23c4.91,7.16,10.48,15.27,21.77,15.27a3.49,3.49,0,1,0,0-7c-7.62,0-11.51-5.67-16-12.24C398,8.11,392.41,0,381.13,0s-16.86,8.11-21.77,15.26c-4.51,6.57-8.41,12.24-16,12.24s-11.5-5.67-16-12.24C322.42,8.11,316.86,0,305.58,0s-16.85,8.11-21.76,15.26c-4.51,6.57-8.41,12.24-16,12.24s-11.5-5.67-16-12.24C246.89,8.11,241.32,0,230,0S213.2,8.11,208.29,15.26c-4.5,6.57-8.39,12.24-16,12.24s-11.49-5.67-16-12.24C171.38,8.11,165.81,0,154.53,0s-16.84,8.11-21.76,15.26c-4.5,6.57-8.4,12.24-16,12.24s-11.49-5.67-16-12.24C95.86,8.11,90.29,0,79,0S62.17,8.11,57.25,15.26c-4.5,6.57-8.4,12.24-16,12.24s-11.5-5.67-16-12.24C20.33,8.11,14.77,0,3.49,0a3.49,3.49,0,0,0,0,7Z"></path>
        </symbol>
        <symbol id="shape_srMEDelFvw" viewBox="0 0 1416.5353 1896.0833">
            <path d="M1404 1385q0 117-79 196t-196 79q-135 0-235-100L117 784Q4 669 4 513q0-159 110-270t269-111q158 0 273 113l605 606q10 10 10 22 0 16-30.5 46.5T1194 950q-13 0-23-10L565 333q-79-77-181-77-106 0-179 75t-73 181q0 105 76 181l776 777q63 63 145 63 64 0 106-42t42-106q0-82-63-145L633 659q-26-24-60-24-29 0-48 19t-19 48q0 32 25 59l410 410q10 10 10 22 0 16-31 47t-47 31q-12 0-22-10L441 851q-63-61-63-149 0-82 57-139t139-57q88 0 149 63l581 581q100 98 100 235z"></path>
        </symbol>
        <symbol id="shape_DijKrreWIZ" viewBox="0 -960 960 960">
            <path d="M480-200 240-440l42-42 198 198 198-198 42 42-240 240Zm0-253L240-693l42-42 198 198 198-198 42 42-240 240Z"></path>
        </symbol>
        <symbol id="shape_fzqkZfSHTA" viewBox="0 0 24 24">
            <path d="M14,17H17L19,13V7H13V13H16M6,17H9L11,13V7H5V13H8L6,17Z"></path>
        </symbol>
        <symbol id="shape_iKgJnHoohr" viewBox="0 0 1920 1896.0833">
            <path d="M896 896q0-106-75-181t-181-75-181 75-75 181 75 181 181 75 181-75 75-181zm768 512q0-52-38-90t-90-38-90 38-38 90q0 53 37.5 90.5t90.5 37.5 90.5-37.5 37.5-90.5zm0-1024q0-52-38-90t-90-38-90 38-38 90q0 53 37.5 90.5T1536 512t90.5-37.5T1664 384zm-384 421v185q0 10-7 19.5t-16 10.5l-155 24q-11 35-32 76 34 48 90 115 7 10 7 20 0 12-7 19-23 30-82.5 89.5T999 1423q-11 0-21-7l-115-90q-37 19-77 31-11 108-23 155-7 24-30 24H547q-11 0-20-7.5t-10-17.5l-23-153q-34-10-75-31l-118 89q-7 7-20 7-11 0-21-8-144-133-144-160 0-9 7-19 10-14 41-53t47-61q-23-44-35-82l-152-24q-10-1-17-9.5T0 987V802q0-10 7-19.5T23 772l155-24q11-35 32-76-34-48-90-115-7-11-7-20 0-12 7-20 22-30 82-89t79-59q11 0 21 7l115 90q34-18 77-32 11-108 23-154 7-24 30-24h186q11 0 20 7.5t10 17.5l23 153q34 10 75 31l118-89q8-7 20-7 11 0 21 8 144 133 144 160 0 9-7 19-12 16-42 54t-45 60q23 48 34 82l152 23q10 2 17 10.5t7 19.5zm640 533v140q0 16-149 31-12 27-30 52 51 113 51 138 0 4-4 7-122 71-124 71-8 0-46-47t-52-68q-20 2-30 2t-30-2q-14 21-52 68t-46 47q-2 0-124-71-4-3-4-7 0-25 51-138-18-25-30-52-149-15-149-31v-140q0-16 149-31 13-29 30-52-51-113-51-138 0-4 4-7 4-2 35-20t59-34 30-16q8 0 46 46.5t52 67.5q20-2 30-2t30 2q51-71 92-112l6-2q4 0 124 70 4 3 4 7 0 25-51 138 17 23 30 52 149 15 149 31zm0-1024v140q0 16-149 31-12 27-30 52 51 113 51 138 0 4-4 7-122 71-124 71-8 0-46-47t-52-68q-20 2-30 2t-30-2q-14 21-52 68t-46 47q-2 0-124-71-4-3-4-7 0-25 51-138-18-25-30-52-149-15-149-31V314q0-16 149-31 13-29 30-52-51-113-51-138 0-4 4-7 4-2 35-20t59-34 30-16q8 0 46 46.5t52 67.5q20-2 30-2t30 2q51-71 92-112l6-2q4 0 124 70 4 3 4 7 0 25-51 138 17 23 30 52 149 15 149 31z"></path>
        </symbol>
        <symbol id="shape_bvGuLrDOKl" viewBox="0 0 1792 1896.0833">
            <path d="M1280 896q0-37-30-54L738 522q-31-20-65-2-33 18-33 56v640q0 38 33 56 16 8 31 8 20 0 34-10l512-320q30-17 30-54zm512 0q0 96-1 150t-8.5 136.5T1760 1330q-16 73-69 123t-124 58q-222 25-671 25t-671-25q-71-8-124.5-58T31 1330q-14-65-21.5-147.5T1 1046 0 896t1-150 8.5-136.5T32 462q16-73 69-123t124-58q222-25 671-25t671 25q71 8 124.5 58t69.5 123q14 65 21.5 147.5T1791 746t1 150z"></path>
        </symbol>
        <symbol id="shape_dprfKgOrjE" viewBox="0 0 1536 1896.0833">
            <path d="M1248 128q119 0 203.5 84.5T1536 416v960q0 119-84.5 203.5T1248 1664h-188v-595h199l30-232h-229V689q0-56 23.5-84t91.5-28l122-1V369q-63-9-178-9-136 0-217.5 80T820 666v171H620v232h200v595H288q-119 0-203.5-84.5T0 1376V416q0-119 84.5-203.5T288 128h960z"></path>
        </symbol>
        <symbol id="shape_AncYUuInWN" viewBox="0 0 1416.5353 1896.0833">
            <path d="M1404 1385q0 117-79 196t-196 79q-135 0-235-100L117 784Q4 669 4 513q0-159 110-270t269-111q158 0 273 113l605 606q10 10 10 22 0 16-30.5 46.5T1194 950q-13 0-23-10L565 333q-79-77-181-77-106 0-179 75t-73 181q0 105 76 181l776 777q63 63 145 63 64 0 106-42t42-106q0-82-63-145L633 659q-26-24-60-24-29 0-48 19t-19 48q0 32 25 59l410 410q10 10 10 22 0 16-31 47t-47 31q-12 0-22-10L441 851q-63-61-63-149 0-82 57-139t139-57q88 0 149 63l581 581q100 98 100 235z"></path>
        </symbol>
        <symbol id="shape_fIYeneYUPl" viewBox="0 -960 960 960">
            <path d="M480-200 240-440l42-42 198 198 198-198 42 42-240 240Zm0-253L240-693l42-42 198 198 198-198 42 42-240 240Z"></path>
        </symbol>
        <symbol id="shape_NgkhYfyuyy" viewBox="0 0 1792 1896.0833">
            <path d="M1280 896q0-37-30-54L738 522q-31-20-65-2-33 18-33 56v640q0 38 33 56 16 8 31 8 20 0 34-10l512-320q30-17 30-54zm512 0q0 96-1 150t-8.5 136.5T1760 1330q-16 73-69 123t-124 58q-222 25-671 25t-671-25q-71-8-124.5-58T31 1330q-14-65-21.5-147.5T1 1046 0 896t1-150 8.5-136.5T32 462q16-73 69-123t124-58q222-25 671-25t671 25q71 8 124.5 58t69.5 123q14 65 21.5 147.5T1791 746t1 150z"></path>
        </symbol>
    </svg>
@stop
