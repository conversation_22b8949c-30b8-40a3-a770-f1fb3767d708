<!DOCTYPE html>
<html lang="vi">

<head>
    <title><PERSON><PERSON><PERSON> online | {{ $course_details->title }}</title>
    <!-- all the meta tags -->
    <meta charset="utf-8"/>
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta content="" name="description"/>
    <meta content="" name="author"/>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <!-- all the css files -->
    <link rel="shortcut icon" href="{{ asset(get_frontend_settings('favicon')) }}"/>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="{{ asset('assets/frontend/default/css/bootstrap.min.css') }}">
    <!-- Fontawesome CSS -->
    <link rel="stylesheet" type="text/css"
          href="{{ asset('assets/global/course_player/vendors/fontawesome/fontawesome.css') }}"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css"/>
    <!-- Player CSS -->
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/global/plyr/plyr.css') }}"/>
    <!-- Main CSS -->
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/global/course_player/css/style.css') }}"/>
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/global/course_player/css/custom.css') }}"/>
    <!-- Tour Guide -->
    <link rel="stylesheet" href="{{ asset('assets/frontend/default/css/plugins/web-tour.css') }}">
    <!-- seeding -->
    <link rel="stylesheet" href="{{ asset('assets/frontend/default/css/plugins/noti-seeding.css') }}">

    <!-- FlatIcons Css -->
    <link rel="stylesheet" href="{{ asset('assets/global/icons/uicons-bold-rounded/css/uicons-bold-rounded.css') }}"/>
    <link rel="stylesheet"
          href="{{ asset('assets/global/icons/uicons-bold-straight/css/uicons-bold-straight.css') }}"/>
    <link rel="stylesheet"
          href="{{ asset('assets/global/icons/uicons-regular-rounded/css/uicons-regular-rounded.css') }}"/>
    <link rel="stylesheet"
          href="{{ asset('assets/global/icons/uicons-solid-rounded/css/uicons-solid-rounded.css') }}"/>
    <link rel="stylesheet"
          href="{{ asset('assets/global/icons/uicons-solid-rounded/css/uicons-solid-rounded.css') }}"/>

    <!-- Summernote Css -->
    <link rel="stylesheet" href="{{ asset('assets/global/summernote/summernote.min.css') }}">
    <!-- global Css -->
    <link rel="stylesheet" href="{{ asset('assets/global/global.css') }}"/>
    <!-- Yaireo Tagify -->
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/global/tagify-master/dist/tagify.css') }}"
          rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="{{ asset('assets/frontend/default/css/player-config.css') }}?v={{ time() }}"/>
    <link rel="stylesheet" href="{{ asset('assets/frontend/config/player.css') }}?v={{ time() }}"/>


</head>
@php
    if (auth()->user()->role == 'admin' || !empty($enroll_status) && $enroll_status == 'valid') {
        $enroll_status = true;
    } else {
        $enroll_status = false;
    }
@endphp

<body id="player_content">

<!-- Start Course Playing Header -->
<header class="playing-header-section">
    @include('course_player.header')
</header>
{{-- <button id="open-login-modal" type="button" data-bs-toggle="modal" data-bs-target="#modal-regis">Open Login--}}
{{-- Modal</button>--}}
<!-- End Course Playing Header -->
<main class="main playing-course">
    <div class="container elearning-course">
        <div class="main-content">
            <div class="">
                <div class="course-video-area">
                    <!-- Video -->
                    <div class="course-video-wrap">
                        <div class="position-relative custom-system-video">
                            <div class="elearning-premium">

                                @if(!$enroll_status && $lesson_details->paid_lesson)
                                    <div class="video-censorred premium-video-container">
                                        <img
                                            src="{{ asset('assets/frontend/course-shopee/assets/icons/graduation-cap.svg') }}"
                                            alt="" width="40" height="40" class="premium-icon">
                                        <div class="premium-title">
                                            Video này chỉ dành <br>cho các học viên đã trả phí
                                        </div>
                                        <p class="premium-description">
                                            Còn nhiều video Free khác rất chất lượng, bạn hãy xem
                                            các video Free khác nhé!
                                        </p>
                                        <div class="premium-buttons">
                                            <button class="btn-upgrade" type="button" data-bs-toggle="modal"
                                                    data-bs-target="#modal-regis">
                                                <img
                                                    src="{{ asset('assets/frontend/course-shopee/assets/icons/crown-icon.svg') }}"
                                                    alt="" width="20" height="20">
                                                Nâng cấp ngay
                                            </button>
                                            @if($next_free_lesson_id)
                                                <a href="{{ route('course.player', ['slug' => $course_details->slug, 'id' => $next_free_lesson_id]) }}"
                                                   class="btn-free">
                                                    Xem video Free tiếp theo
                                                    <img
                                                        src="{{ asset('assets/frontend/course-shopee/assets/icons/chevron-right.svg') }}"
                                                        alt="" width="20" height="20">
                                                </a>
                                            @endif
                                        </div>
                                    </div>
{{--                                @elseif(in_array($lesson_details->id, get_locked_lesson_ids($course_details->id, auth()->user()->id)) && $course_details->enable_drip_content && (strpos($lesson_details->lesson_src,'mediadelivery.net') !== false) ||!empty($lesson_details->video_type) )--}}
                                @elseif(in_array($lesson_details->id, get_locked_lesson_ids($course_details->id, auth()->user()->id)) && $course_details->enable_drip_content )
                                    @php

                                        $drip_content_settings = json_decode($course_details->drip_content_settings);
                                    @endphp
                                    <div class="permission-denied-container">
                                        <div class="permission-denied-content">
                                            <div class="permission-denied-icon">
                                                <i class="bi bi-lock-fill"></i>
                                            </div>
                                            <h3 class="permission-denied-title">
                                                Nội dung bị khóa
                                            </h3>
                                            <p class="permission-denied-description">
                                                Khóa học này sử dụng tính năng học tuần tự. Bạn cần hoàn thành các bài học trước đó để mở khóa nội dung này.
                                            </p>
                                            <div class="permission-denied-progress">
                                                <div class="progress-info">
                                                    <i class="bi bi-info-circle"></i>
                                                    <span>Hãy quay lại và hoàn thành các bài học trước đó</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <style>
                                        .permission-denied-container {
                                            display: flex;
                                            align-items: center;
                                            justify-content: center;
                                            min-height: 400px;
                                            padding: 2rem;
                                            margin: 2rem 0;
                                        }

                                        .permission-denied-content {
                                            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
                                            border: 1px solid #e9ecef;
                                            border-radius: 16px;
                                            padding: 3rem 2rem;
                                            text-align: center;
                                            max-width: 500px;
                                            width: 100%;
                                            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
                                            position: relative;
                                            overflow: hidden;
                                        }

                                        .permission-denied-content::before {
                                            content: '';
                                            position: absolute;
                                            top: 0;
                                            left: 0;
                                            right: 0;
                                            height: 4px;
                                            background: linear-gradient(90deg, #754FFE 0%, #c664ff 100%);
                                        }

                                        .permission-denied-icon {
                                            width: 80px;
                                            height: 80px;
                                            background: linear-gradient(135deg, #754FFE 0%, #c664ff 100%);
                                            border-radius: 50%;
                                            display: flex;
                                            align-items: center;
                                            justify-content: center;
                                            margin: 0 auto 1.5rem;
                                            box-shadow: 0 8px 20px rgba(117, 79, 254, 0.3);
                                        }

                                        .permission-denied-icon i {
                                            font-size: 2rem;
                                            color: white;
                                        }

                                        .permission-denied-title {
                                            font-size: 1.5rem;
                                            font-weight: 600;
                                            color: #1e293b;
                                            margin-bottom: 1rem;
                                            line-height: 1.3;
                                        }

                                        .permission-denied-description {
                                            font-size: 1rem;
                                            color: #64748b;
                                            line-height: 1.6;
                                            margin-bottom: 2rem;
                                            max-width: 400px;
                                            margin-left: auto;
                                            margin-right: auto;
                                        }

                                        .permission-denied-progress {
                                            background: #f1f5f9;
                                            border-radius: 12px;
                                            padding: 1rem;
                                            border-left: 4px solid #754FFE;
                                        }

                                        .progress-info {
                                            display: flex;
                                            align-items: center;
                                            justify-content: center;
                                            gap: 0.5rem;
                                            font-size: 0.9rem;
                                            color: #475569;
                                            font-weight: 500;
                                        }

                                        .progress-info i {
                                            color: #754FFE;
                                            font-size: 1rem;
                                        }

                                        @media (max-width: 768px) {
                                            .permission-denied-container {
                                                padding: 1rem;
                                                min-height: 300px;
                                            }

                                            .permission-denied-content {
                                                padding: 2rem 1.5rem;
                                            }

                                            .permission-denied-icon {
                                                width: 60px;
                                                height: 60px;
                                            }

                                            .permission-denied-icon i {
                                                font-size: 1.5rem;
                                            }

                                            .permission-denied-title {
                                                font-size: 1.25rem;
                                            }

                                            .permission-denied-description {
                                                font-size: 0.9rem;
                                            }
                                        }
                                    </style>
                                @else
                                    @include('course_player.player_page')
                                @endif

                            </div>
                        </div>
                    </div>
                </div>

                <div class="course-video-info">
                    <div class="d-flex justify-content-between align-items-start">
                        <p class="course-video-info-title fw-bold fs-4 flex-1">

                            @if(!$enroll_status)
                                @if($lesson_details->hide_title == 1)
                                    Nâng cấp để xem bài học
                                @else
                                    {{ $lesson_details->title }}
                                @endif
                            @else
                                {{ $lesson_details->title }}
                            @endif
                        </p>
                        @if(!$enroll_status && !$lesson_details->paid_lesson)
                            <button class="btn-upgrade upgrade-btn" type="button" data-bs-toggle="modal" data-bs-target="#modal-regis">
                                <img
                                    src="{{ asset('assets/frontend/course-shopee/assets/icons/crown-icon.svg') }}"
                                    alt="" width="16" height="16" style="transform: translateY(-2px);"> Nâng cấp
                            </button>
                        @endif
                    </div>

                    <div class="course-video-info-meta">
                        <div class="course-badge-view">
                            @if($lesson_details->paid_lesson)
                                <div class="user-badge badge premium-trial-badge">
                                    <img src="{{ asset('assets/frontend/course-shopee/assets/icons/crown-icon.svg') }}"
                                         width="12.941px" height="12.941px" alt="Pro">
                                    <span>PRO</span>
                                </div>
                            @elseif($lesson_details->trial_lesson)
                                <div class="user-badge badge pro-trial-badge">
                                    <img src="{{ asset('assets/frontend/course-shopee/assets/icons/crown-icon.svg') }}"
                                         width="12.941px" height="12.941px" alt="Pro">
                                    <span>{{get_phrase('PRO TRIAL')}}</span>
                                </div>
                            @else
                                <div class="user-badge badge free-badge">
                                    <img src="{{ asset('assets/frontend/course-shopee/assets/icons/coin.svg') }}"
                                         width="11" height="11" alt="Free">
                                    <span>{{get_phrase('FREE')}}</span>
                                </div>
                            @endif

                            @if(isset($lesson_details->is_important) && $lesson_details->is_important)
                                <div class="user-badge badge important-badge">
                                    <img src="{{ asset('assets/frontend/course-shopee/assets/icons/important.svg') }}"
                                         width="11" height="11" alt="Important">
                                    <span>{{get_phrase('IMPORTANT')}}</span>
                                </div>
                            @endif
                        </div>

                        <div class="divider divider-movie"></div>
                        <div class="views c-views-count">
                            <img src="{{ asset('assets/frontend/course-shopee/assets/icons/movie-clip.svg') }}"
                                 width="16" height="16" alt="Views">
                            <span>{{get_phrase('Views')}}:
                                    <span>{{ $lesson_details->formatted_views ?? 0 }}</span>
                                </span>
                        </div>
                        <div class="divider"></div>

                        @if(lesson_durations($lesson_details->id) != '00:00:00')
                            <div>
                                    <span class="total-time">{{get_phrase('Duration')}}:
                                        <span
                                            class="total-time-value">{{ lesson_durations($lesson_details->id) }}</span>
                                    </span>
                            </div>
                        @endif
                    </div>
                </div>

                <script>
                    // Disable right-click on video
                    document.getElementById("player").oncontextmenu = function () {
                        return false; // Prevent right-click menu
                    };
                </script>
                <!-- Tab -->
                <!-- Tab -->
                <div class="course-video-navtab">
                    @include('course_player.tab_bar')
                </div>

            </div>
        </div>
        <div class="elearning-course-playlist">
            @include('course_player.side_bar')

        </div>
    </div>

    {{--web tour--}}
    <div>
        <div id="tour-overlay"></div>
        <div id="tour-popover" class="flex flex-col">
            <div class="flex items-center mb-3">
        <span class="text-yellow-500 text-2xl mr-2"
        ><i class="fas fa-lightbulb"></i
            ></span>
                <h3 id="tour-title" class="text-lg font-semibold text-gray-800">
                    Tiêu đề Hướng dẫn
                </h3>
            </div>
            <p id="tour-content" class="text-sm text-gray-700 mb-4">
                Nội dung hướng dẫn sẽ được hiển thị ở đây.
            </p>
            <div class="flex justify-between items-center mt-auto">
                <span id="tour-step-counter" class="text-xs text-gray-500">1/5</span>
                <div class="guide-btn">
                    <button
                        id="tour-prev"
                        class="text-sm text-gray-600 hover:text-gray-800 py-1 px-3 mr-2 rounded-md hover:bg-gray-200 transition duration-150"
                    >
                        Trước
                    </button>
                    <button
                        id="tour-next"
                        class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-1 px-4 rounded-md shadow transition duration-150"
                    >
                        Tiếp
                    </button>
                    <button
                        id="tour-done"
                        class="bg-green-500 hover:bg-green-600 text-white font-semibold py-1 px-4 rounded-md shadow transition duration-150"
                        style="display: none"
                    >
                        Hoàn thành
                    </button>
                </div>
            </div>
            <button
                id="tour-skip"
                class="absolute top-2 right-3 text-gray-400 hover:text-gray-600 text-xl"
            >
                &times;
            </button>
        </div>
    </div>
    <div class="noti-seeding" id="noti-seeding"></div>
</main>


<!-- Main Jquery -->
<script src="{{ asset('assets/frontend/default/js/jquery-3.7.1.min.js') }}"></script>

<!-- Bootstrap bundle with popper -->
<script src="{{ asset('assets/frontend/default/js/bootstrap.bundle.min.js') }}"></script>

<!-- Summernote Css -->
<script src="{{ asset('assets/global/summernote/summernote.min.js') }}"></script>

<!-- Fontawesome JS -->
<script src="{{ asset('assets/global/course_player/vendors/fontawesome/fontawesome.all.min.js') }}"></script>

<!-- Player JS -->
<script src="{{ asset('assets/global/plyr/plyr.js') }}"></script>

<!-- Yaireo Tagify -->
<script src="{{ asset('assets/global/tagify-master/dist/tagify.min.js') }}"></script>

<!-- Jquery form -->
<script type="text/javascript" src="{{ asset('assets/global/jquery-form/jquery.form.min.js') }}"></script>

<!-- Tourguide -->
<script type="text/javascript" src="{{ asset('assets/frontend/default/js/plugins/web-tour.js') }}"></script>

<!-- seeding -->
<script type="text/javascript" src="{{ asset('assets/frontend/default/js/plugins/noti-seeding.js') }}"></script>


<script src="{{ asset('assets/frontend/course-shopee/assets/js/source.js') }}"></script>
<script src="{{ asset('assets/frontend/course-shopee/assets/js/script.js') }}"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js"></script>
<script src="{{ asset('assets/frontend/default/js/vietqr.js') }}"></script>

<script>
    // --- Initialize the Tour ---
    $(document).ready(function () {
        // Configuration for the tour steps
        const myTourSteps = [
            {
                element: ".course-list-container",
                placement: "left",
                title: "Danh sách bài học",
                content:
                    "Đây là danh sách bài học của trang web. Nó cho bạn biết bạn đang ở đâu.",
            },
            {
                element: "body button.plyr__control.plyr__control--overlaid",
                placement: "bottom",
                title: "Video bài học",
                content:
                    "Video bài học sẽ được hiển thị ở đây. Hãy bấm play để xem video.",
            },
        ];

        function checkElementsExist() {
            for (const step of myTourSteps) {
                if ($(step.element).length === 0) {
                    return false;
                }
            }
            return true;
        }

        function initTour() {
            if (checkElementsExist()) {
                // Create a new tour instance
                const tour = new WebTourGuide(myTourSteps, {
                    cookieName: "mySpecificTourCompleted", // Optional: Custom cookie name
                    // highlightClass: 'custom-highlight-class' // Optional: Custom highlight class
                });

                tour.start();
            } else {
                setTimeout(initTour, 100);
            }
        }

        initTour();
    });
</script>

<script>
    $(document).ready(function () {
        // Check if the user is on a mobile device
        function isMobileDevice() {
            return (window.innerWidth <= 768 ||
                /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent));
        }

        if (isMobileDevice()) {
            return;
        }

        @if(addon_check('my.fake_notifications'))
        const seeding = new FakeSeeding({
            minInterval: 10000, // 10 seconds minimum
            maxInterval: 16000, // 16 seconds maximum
            showDuration: 5000, // Show for 5 seconds
        });
        seeding.start();

        // seeding.stop();
        const player = document.querySelector('#player');

        if (player) {
            let isPlaying = false;

            // Track play event
            player.addEventListener('play', function () {
                isPlaying = true;
                console.log('Video state changed: PLAYING');
                seeding.stop();
            });

            // Track pause event
            player.addEventListener('pause', function () {
                isPlaying = false;
                console.log('Video state changed: PAUSED');
                seeding.start();
            });
            // Function to check current state
            window.checkVideoState = function () {

                return isPlaying ? 'PLAYING' : 'PAUSED/STOPPED';
            };

            console.log('Video player tracking initialized');
        } else {
            console.log('No video player found on this page');
        }
        @endif
    });
</script>

<style>
    #qrcode img {
        width: 100%;
    }

    #cancel_coupon,
    .login-link {
        cursor: pointer;
    }
</style>
<script src="https://www.google.com/recaptcha/api.js" async defer></script>
@include('partials.modals.checkout_payment')
<script>
    $(document).ready(function () {
        // Khai báo biến ở đầu hàm
        let countdownInterval;

        function appendTimerBox(days, hours, minutes, seconds) {
            const countdownElement = $(".countdown-timer");
            countdownElement.html(
                `<div class='timer-box time-day'>
                <span class='timer-box-number'>${days}</span>
                <span class='timer-box-label'>Ngày</span>
                </div><div class='timer-box time-hour'>
                <span class='timer-box-number'>${hours}</span>
                <span class='timer-box-label'>Giờ</span>
                </div><div class='timer-box time-minute'>
                <span class='timer-box-number'>${minutes}</span>
                <span class='timer-box-label'>Phút</span>
                </div><div class='timer-box time-second'>
                <span class='timer-box-number'>${seconds}</span>
                <span class='timer-box-label'>Giây</span>
                </div>`
            );
        }

        // Update countdown every second
        updateCountdown();
        countdownInterval = setInterval(updateCountdown, 1000); // Update every minute

        function updateCountdown() {
            var countdownElement = $(".countdown-timer");
            var targetDate = new Date(countdownElement.data("countdown"));
            var now = new Date();

            // Calculate remaining time in milliseconds
            var difference = targetDate - now;

            if (difference <= 0) {
                // If countdown has ended
                appendTimerBox("00", "00", "00", "00");
                $("#countdown-presell").hide();
                clearInterval(countdownInterval);
                return;
            }
            $("#countdown-presell").show();

            // Calculate days, hours and minutes
            var days = Math.floor(difference / (1000 * 60 * 60 * 24));
            var hours = Math.floor(
                (difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
            );
            var minutes = Math.floor(
                (difference % (1000 * 60 * 60)) / (1000 * 60)
            );
            var seconds = Math.floor((difference % (1000 * 60)) / 1000);

            // Format with leading zeros
            days = days < 10 ? "0" + days : days;
            hours = hours < 10 ? "0" + hours : hours;
            minutes = minutes < 10 ? "0" + minutes : minutes;
            seconds = seconds < 10 ? "0" + seconds : seconds;

            // Update countdown display
            appendTimerBox(days, hours, minutes, seconds);
        }

        // Tier item activation based on target dates
        function updateTierStatus() {
            var now = new Date();
            var activeFound = false; // Initialize the variable here

            // Check each tier item in reverse order to prioritize the latest active tier
            $(".tier-item").each(function () {
                var targetDateStr = $(this).data("targetdate");
                if (targetDateStr) {
                    var targetDate = new Date(targetDateStr);

                    // If current date is after or equal to target date
                    if (now >= targetDate) {
                        $(this).addClass("active");
                        activeFound = true; // Set to true when we find an active tier
                    } else {
                        $(this).removeClass("active");
                    }
                }
            });

            // If no tier is active (all future dates), activate the first one
            if (!activeFound) {
                $(".tier-item:first").addClass("active");
            }
        }

        // Run on page load
        updateTierStatus();

        // Check tier status daily (can adjust frequency as needed)
        setInterval(updateTierStatus, 86400000); // Daily check
    });
</script>

<!-- toster file -->
@include('frontend.default.toaster')

<!-- Custom Script -->
<script src="{{ asset('assets/global/course_player/js/script.js') }}"></script>

@include('admin.common_scripts')
@include('course_player.init')
@stack('js')

<style>
    .course-video-navtab .nav-pills .nav-link.active,
    .course-video-navtab .nav-pills .show > .nav-link {
        color: #eb2805;
    }

    .course-video-navtab .nav-pills .nav-link.active path,
    .course-video-navtab .nav-pills .show > .nav-link path {
        fill: #eb2805;
    }

    .course-video-navtab .nav-pills .nav-link::after {
        background: #eb2805;
    }

    /* Smooth text shimmer effect */
    .video-censorred .premium-title {
        color: #ffd700;
        background-image: linear-gradient(-45deg,
        #ffd700 0%,
        #ff9f43 25%,
        #ff6b6b 50%,
        #4f9fff 75%,
        #ffd700 100%);
        background-size: 400%;
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        text-fill-color: transparent;
        animation: shine 8s linear infinite;
        font-weight: bold;
    }

    @keyframes shine {
        from {
            background-position: 0%;
        }

        to {
            background-position: 400%;
        }
    }

    /* Focus mode styles */
    .focus-mode-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        z-index: 9998;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    body.focus-mode {
        overflow: hidden;
    }

    body.focus-mode .focus-mode-overlay {
        opacity: 1;
        visibility: visible;
    }

    .course-video-wrap {
        transition: all 0.3s ease;
        position: relative;
        /*z-index: 1;*/
    }

    body.focus-mode .course-video-wrap {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 90%;
        max-width: 1600px;
        min-width: 320px;
        z-index: 9999;
        margin: 0;
        box-sizing: border-box;
        height: 85vh;
        max-height: 90vh;
        aspect-ratio: 16/9;
        border-radius: 8px;
        overflow: hidden;
    }

    /* Tablet */
    @media (max-width: 1024px) {
        body.focus-mode .course-video-wrap {
            width: 95%;
            height: 75vh;
        }
    }

    /* Mobile landscape */
    @media (max-width: 896px) and (orientation: landscape) {
        body.focus-mode .course-video-wrap {
            width: 96%;
            height: 98vh;
            border-radius: 0;
        }
    }

    /* Mobile portrait */
    @media (max-width: 768px) and (orientation: portrait) {
        body.focus-mode .course-video-wrap {
            width: 100%;
            height: 40vh;
            border-radius: 0;
            top: 50%;
            transform: translate(-50%, -50%);
        }
    }

    /* Small mobile */
    @media (max-width: 480px) {
        body.focus-mode .course-video-wrap {
            width: 100%;
            height: 35vh;
            border-radius: 0;
            top: 50%;
            transform: translate(-50%, -50%);
        }
    }

    .close-focus-btn {
        position: fixed;
        top: 15px;
        right: 15px;
        color: #fff;
        font-size: 20px;
        cursor: pointer;
        z-index: 10000;
        background: rgba(255, 255, 255, 0.15);
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.2);
        font-weight: 300;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .close-focus-btn:hover {
        background: rgba(255, 255, 255, 0.25);
        border-color: rgba(255, 255, 255, 0.3);
        transform: scale(1.05);
    }

    body.focus-mode .close-focus-btn {
        opacity: 1;
        visibility: visible;
    }

    /* Purchase course notification styles */
    .purchase-notification {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: 1px solid #dee2e6;
        border-radius: 12px;
        padding: 2rem;
        margin: 2rem 0;
    }

    .purchase-notification h4 {
        color: #495057;
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .purchase-notification p {
        color: #6c757d;
        font-size: 0.95rem;
        line-height: 1.6;
        margin-bottom: 1.5rem;
    }

    .purchase-notification .btn {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        padding: 0.75rem 1.5rem;
        font-weight: 500;
        border-radius: 8px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .purchase-notification .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
    }

    .purchase-notification .btn img {
        margin: 0;
        transform: none;
    }

    .purchase-notification img {
        opacity: 0.7;
        margin-bottom: 1rem;
    }
</style>

<!-- Add overlay and close button -->
<div class="focus-mode-overlay"></div>
<div class="close-focus-btn">×</div>

</body>

</html>
