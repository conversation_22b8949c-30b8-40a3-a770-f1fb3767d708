@push('js')
    <script type="text/javascript" src="//assets.mediadelivery.net/playerjs/player-0.1.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        "use strict";
        // Device detection
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
        const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
        const isIOSSafari = isIOS && isSafari;

        // Global variables
        const popupConfigs = @json($lesson_details->popup_configs ?? []);
        const lesson_id = '{{ $lesson_details['id'] }}';
        const course_id = '{{ $course_details['id'] }}';
        const currentProgress = '{{ lesson_progress($lesson_details['id']) }}';
        let previousSavedDuration = 0;
        let currentDuration = 0;

        // Popup tracking
        window.shownPopups = {};
        window.popupShown = false;

        // Player related variables
        let player;
        let player_js;
        let watched_duration = JSON.parse(@json(get_watched_duration($lesson_details['id'], auth()->user()->id)));
        let previous_duration = watched_duration && watched_duration.current_duration > 0
            ? watched_duration.current_duration
            : 0;

        // Heatmap tracking variables
        let currentHeatmapTimestamp = 0;
        let heatmapData = {};
        let heatmapChart = null;

        /**
         * Focus mode toggle functionality
         * @param {boolean} enable - Whether to enable focus mode
         * @param {boolean} checkIOSSafari - Whether to check if device is iOS Safari
         */
        const toggleFocusMode = (enable, checkIOSSafari = false) => {
            console.log(123)
            if (!checkIOSSafari || (checkIOSSafari && isIOS)) {
                $('body').toggleClass('focus-mode', enable);
            }
        };

        /**
         * Updates watch history periodically
         */
        function setupWatchHistoryTracking() {
            if (typeof player !== 'object' || player === null) return;

            setInterval(function () {

                currentDuration = player.currentTime;
                let totalDuration = player.totalDuration || 0;

                if (lesson_id && course_id && previousSavedDuration != currentDuration) {
                    previousSavedDuration = currentDuration;

                    $.ajax({
                        type: 'POST',
                        url: "{{ route('update_watch_history') }}",
                        data: {
                            lesson_id: lesson_id,
                            course_id: course_id,
                            current_duration: currentDuration,
                            total_duration: totalDuration,
                        },
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        error: function (xhr) {
                            if (xhr.status === 451) {
                                try {
                                    window.location.href = "{{route('violation')}}";
                                } catch (e) {
                                    console.error("Error parsing response:", e);
                                }
                            }
                        }
                    });
                }
            }, 10000);
        }

        /**
         * Shows advertisement popup
         * @param {string|number} popup_id - Optional specific popup ID
         */
        function showAdPopup(popup_id) {
            // Pause video when showing popup
            if (player_js && document.getElementById("bunny-player")) {
                player_js.pause();
            } else if (typeof player === 'object' && player !== null) {
                player.pause();
            }

            // Get popup from database
            $.ajax({
                url: "{{ route('get.random.popup') }}",
                type: "GET",
                data: {
                    popup_id: popup_id || {{ $lesson_details['popup_id'] ?? 'null' }}
                },
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function (response) {
                    if (response.popup) {
                        // Remove any existing popups first
                        $('#ad-popup-overlay').remove();

                        // Create and show popup
                        const uniqueId = 'ad-popup-' + Date.now();
                        const popupHtml = `
                        <div id="ad-popup-overlay" class="ad-popup-overlay" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.7); z-index: 999; display: flex; justify-content: center; align-items: center;">
                            <div class="ad-popup-content" style="position: relative; max-width: 80%; max-height: 70%; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);">
                            <style>
                                @media (min-width: 768px) {
                                    .ad-popup-content {
                                        max-width: 60% !important;
                                    }
                                }

                                .ad-close-btn {
                                    position: absolute;
                                    top: 10px;
                                    right: 10px;
                                    background: #ff0000;
                                    color: white;
                                    border: none;
                                    border-radius: 50%;
                                    width: 30px;
                                    height: 30px;
                                    font-size: 18px;
                                    line-height: 30px;
                                    cursor: pointer;
                                    z-index: 1000;
                                    text-align: center;
                                    padding: 0;
                                }

                                .ad-close-btn:hover {
                                    background: #cc0000;
                                }
                            </style>
                                <a href="${response.popup.url}" target="_blank">
                                    <img src="${response.popup.image_link}" alt="Advertisement" style="width: 100%; height: auto; display: block;">
                                </a>
                                <button type="button" id="close-ad-popup" class="ad-close-btn" onclick="closePopupDirectly()">✖</button>
                            </div>
                        </div>`;

                        // Append popup to video player area instead of body
                        if ($('.course-video-area').length > 0) {
                            $('.course-video-area').css('position', 'relative').append(popupHtml);
                        } else if ($('.plyr__video-wrapper').length > 0) {
                            $('.plyr__video-wrapper').css('position', 'relative').append(popupHtml);
                        } else {
                            // Fallback to appending to player element
                            $('#player').parent().css('position', 'relative').append(popupHtml);
                        }

                        // No jQuery event handling needed as we use direct onclick handler
                        console.log('Popup created with direct handler');
                    }
                },
                error: function (xhr, status, error) {
                    console.error("Error fetching popup:", error);

                    // Resume video on error
                    if (player_js && document.getElementById("bunny-player")) {
                        player_js.play();
                    } else if (typeof player === 'object' && player !== null) {
                        player.play();
                    }
                }
            });
        }

        /**
         * Set up next video countdown and overlay
         */
        function setupNextVideoCountdown() {
            const overlay = document.getElementById('nextVideoOverlay');
            const countdownElement = document.getElementById('countdown');
            const cancelNextVideoButton = document.getElementById('cancelNextVideo');
            const replayVideoButton = document.getElementById('replayVideo');
            let countdownInterval;

            // Function to start countdown
            function startCountdown() {
                let countdown = 5; // 5 second countdown
                countdownElement.textContent = countdown;

                // Update countdown text
                const countdownTextElement = document.getElementById('countdownText');
                if (countdownTextElement) {
                    countdownTextElement.textContent = countdown + ' giây';
                }

                overlay.style.visibility = 'visible';

                // Reset and restart circular progress animation
                const circleProgress = document.querySelector('.circular-progress');
                circleProgress.style.transition = 'none';
                circleProgress.style.strokeDashoffset = 220;

                setTimeout(() => {
                    circleProgress.style.transition = 'stroke-dashoffset 5s linear';
                    circleProgress.style.strokeDashoffset = 0;
                }, 10);

                countdownInterval = setInterval(() => {
                    countdown -= 1;
                    countdownElement.textContent = countdown;

                    // Update countdown text
                    if (countdownTextElement) {
                        countdownTextElement.textContent = countdown + ' giây';
                    }

                    if (countdown <= 0) {
                        clearInterval(countdownInterval);
                        overlay.style.visibility = 'hidden';

                        const next_lesson_id = '{{ next_lesson($course_details['id'], $lesson_details['id']) }}';
                        if (next_lesson_id) {
                            const url = '{{ url("play-course") }}' + '/' +
                                '{{ Str::slug($course_details['title']) }}' + '-' +
                                course_id + '/' + next_lesson_id;
                            window.location.href = url;
                        }
                    }
                }, 1000);
            }

            // Setup event listener for video end in regular player
            if (typeof player === 'object' && player !== null && player.addEventListener) {
                player.addEventListener('ended', () => {
                    const next_lesson_id = '{{ next_lesson($course_details['id'], $lesson_details['id']) }}';
                    if (next_lesson_id) {
                        startCountdown();
                    }
                });
            }

            // Cancel next video on click
            cancelNextVideoButton.addEventListener('click', () => {
                clearInterval(countdownInterval);
                overlay.style.visibility = 'hidden';
            });

            // Replay current video on click
            replayVideoButton.addEventListener('click', () => {
                clearInterval(countdownInterval);
                overlay.style.visibility = 'hidden';

                if (document.getElementById("bunny-player")) {
                    const bunnyPlayerInstance = new playerjs.Player(document.getElementById("bunny-player"));
                    bunnyPlayerInstance.setCurrentTime(0);
                    bunnyPlayerInstance.play();
                } else if (typeof player === 'object' && player !== null) {
                    player.currentTime = 0;
                    player.play();
                }
            });

            // Expose startCountdown function globally so bunny player can use it
            window.startCountdown = startCountdown;
        }

        // Initialize video player based on type
        function initializePlayer() {
            const bunny_player = document.getElementById("bunny-player");

            if (bunny_player) {
                initializeBunnyPlayer(bunny_player);
            } else {
                initializeRegularPlayer();
            }

            // Setup watch history tracking after player is initialized
            setupWatchHistoryTracking();

            // Set previous time if available
            setPreviousWatchTime();
        }

        /**
         * Initialize Bunny player
         */
        function initializeBunnyPlayer(bunny_player) {
            player_js = new playerjs.Player(bunny_player);
            player = {
                currentTime: 200,
                playing: true
            };

            let updateDurationSetOnce = false;

            player_js.on('timeupdate', (data) => {
                if (!updateDurationSetOnce) {
                    player.totalDuration = data.duration;
                    updateDurationSetOnce = true;
                }

                updateHeatmap(data.seconds);
                player.currentTime = Math.floor(data.seconds);

                @if($lesson_details['popup_configs']??[])
                // Check for each configured popup
                @foreach($lesson_details['popup_configs']??[] as $index => $config)
                if (Math.floor(data.seconds) == {{ $config['time'] ?? 0 }} && !window.shownPopups['popup_{{ $index }}_{{ $config['popup_id'] ?? 0 }}']) {
                    window.shownPopups['popup_{{ $index }}_{{ $config['popup_id'] ?? 0 }}'] = true;
                    showAdPopup('{{ $config['popup_id'] ?? 0 }}');
                }
                @endforeach
                @elseif($lesson_details['popup_id'] && $lesson_details['popup_time'] > 0)
                // Legacy support
                if (Math.floor(data.seconds) == {{ $lesson_details['popup_time'] }} && !window.popupShown) {
                    window.popupShown = true;
                    showAdPopup();
                }
                @endif
            });

            player_js.on('enterfullscreen', () => {
                document.body.classList.add('plyr-fullscreen-active');
                window.dispatchEvent(new Event('resize'));
            });

            player_js.on('exitfullscreen', () => {
                document.body.classList.remove('plyr-fullscreen-active');
                window.dispatchEvent(new Event('resize'));
            });

            player_js.on('ready', () => {
                let timeSetOnce = false;

                player_js.on('play', () => {
                    if (!timeSetOnce) {
                        player_js.setCurrentTime(Math.floor(watched_duration.current_duration));
                        timeSetOnce = true;
                    }
                });

                player_js.on('ended', () => {
                    next_course();

                });

                // Get duration for heatmap
                player_js.getDuration((duration) => {
                    player.totalDuration = duration;
                });
            });
        }

        function showPaymentModal() {
            const modal = document.getElementById('modal-regis');

            // If using plain Bootstrap
            if (modal) {
                const bsModal = new bootstrap.Modal(modal);
                bsModal.show();
            }
        }
        function next_course() {
            @if( $enroll_status)
                const next_lesson_id = '{{ next_lesson($course_details['id'], $lesson_details['id']) }}';
                if (next_lesson_id) {
                    startCountdown();
                }
            @else
                 showPaymentModal();
            @endif

        }

        /**
         * Initialize regular Plyr player
         */
        function initializeRegularPlayer() {
            player = new Plyr('#player', {
                youtube: {
                    // Options for YouTube player
                    controls: 1, // Show YouTube controls
                    modestBranding: false, // Show YouTube logo
                    showinfo: 1, // Show video title and uploader on play
                    rel: 0, // Show related videos at the end
                    iv_load_policy: 3, // Do not show video annotations
                    cc_load_policy: 1, // Show captions by default
                    autoplay: false, // Do not autoplay
                    loop: false, // Do not loop the video
                    mute: false, // Do not mute the video
                    start: 0, // Start at this time (in seconds)
                    end: null // End at this time (in seconds)
                }
            });

            player.on('timeupdate', () => {
                updateHeatmap(player.currentTime);

                @if($lesson_details['popup_configs']??[])
                // Check for each configured popup
                @foreach($lesson_details['popup_configs']??[] as $index => $config)
                if (Math.floor(player.currentTime) == {{ $config['time'] ?? 0 }} && !window.shownPopups['popup_{{ $index }}_{{ $config['popup_id'] ?? 0 }}']) {
                    window.shownPopups['popup_{{ $index }}_{{ $config['popup_id'] ?? 0 }}'] = true;
                    showAdPopup('{{ $config['popup_id'] ?? 0 }}');
                }
                @endforeach
                @elseif($lesson_details['popup_id'] && $lesson_details['popup_time'] > 0)
                // Legacy support
                if (Math.floor(player.currentTime) == {{ $lesson_details['popup_time'] }} && !window.popupShown) {
                    window.popupShown = true;
                    showAdPopup();
                }
                @endif
            });

            player.on('enterfullscreen', () => {
                toggleFocusMode(true, true);
            });

            player.on('exitfullscreen', () => {
                toggleFocusMode(false, true);
            });

            player.on('ready', () => {
                player.totalDuration = player.duration;
            });
            player.on('ended', event => {
                next_course();
            });
        }

        /**
         * Set previous watch time
         */
        function setPreviousWatchTime() {
            if (!player) return;

            const previousTimeSetter = setInterval(function () {
                if (player.playing == false && player.currentTime != previous_duration) {
                    player.currentTime = previous_duration;
                } else {
                    clearInterval(previousTimeSetter);
                }
            }, 200);
        }

        /**
         * Update video heatmap data
         */
        function updateHeatmap(currentTime) {
            @if(addon_check('my.video_heatmap'))
            const currentSecond = Math.floor(currentTime);
            // Only update every 10 seconds to reduce server load
            if (currentSecond % 10 !== 0) return;

            // Avoid duplicate updates
            if (currentHeatmapTimestamp === currentSecond) return;

            currentHeatmapTimestamp = currentSecond;

            $.ajax({
                type: 'POST',
                url: "{{ route('update_heatmap') }}",
                data: {
                    lesson_id: lesson_id,
                    course_id: course_id,
                    timestamp: currentHeatmapTimestamp
                },
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function (response) {
                    if (response.success && response.data) {
                        heatmapData = response.data;
                        renderHeatmap(heatmapData);
                    }
                },
                error: function (xhr, status, error) {
                    console.error("Error updating heatmap:", error);
                }
            });
            @endif
        }

        // Admin-only heatmap functionality
        @if(auth()->user()->role == 'admin' && addon_check('my.video_heatmap'))
        /**
         * Render video heatmap visualization
         */
        function renderHeatmap(data) {
            const heatmapContainer = document.getElementById('video-heatmap');
            if (!heatmapContainer) return;

            heatmapContainer.classList.remove('d-none');
            heatmapContainer.innerHTML = '<canvas id="myChart"></canvas>';

            // Get video duration
            const videoDuration = player && player.totalDuration ? player.totalDuration : 0;
            if (!videoDuration) {
                // Retry rendering after a delay when player might be ready
                setTimeout(() => renderHeatmap(data), 2000);
                return;
            }

            // Prepare data for Chart.js
            const labels = [];
            const viewData = [];

            // Handle empty data
            if (!data || Object.keys(data).length === 0) {
                // Create sample empty data points
                for (let i = 0; i <= Math.min(videoDuration, 50); i += 2) {
                    labels.push(`${Math.floor(i / 60)}:${String(i % 60).padStart(2, '0')}`);
                    viewData.push(0);
                }
            } else {
                // Sample points evenly for a smoother curve
                const maxDataPoints = 50;
                const interval = Math.ceil(videoDuration / maxDataPoints);

                // Pre-process to ensure no data is lost
                const processedData = {};
                for (let i = 0; i <= videoDuration; i++) {
                    const index = Math.floor(i / interval) * interval;
                    processedData[index] = Math.max(processedData[index] || 0, data[i] || 0);
                }

                // Convert to arrays for Chart.js
                Object.keys(processedData).sort((a, b) => a - b).forEach(time => {
                    const timeInt = parseInt(time);
                    const minutes = Math.floor(timeInt / 60);
                    const seconds = timeInt % 60;
                    labels.push(`${minutes}:${String(seconds).padStart(2, '0')}`);
                    viewData.push(processedData[time]);
                });
            }

            // Calculate max value for proper scaling
            const maxValue = Math.max(1, ...viewData);

            // Initialize Chart.js
            const ctx = document.getElementById('myChart').getContext('2d');

            // Destroy previous chart if exists
            if (heatmapChart) {
                heatmapChart.destroy();
            }

            heatmapChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'View Intensity',
                        data: viewData,
                        backgroundColor: 'rgba(0, 123, 255, 0.2)',
                        borderColor: '#00aaff',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.3,
                        pointRadius: 0,
                        pointHoverRadius: 5,
                        pointHoverBackgroundColor: '#00aaff',
                        pointHoverBorderColor: 'white',
                        pointHoverBorderWidth: 2,
                        hoverBackgroundColor: 'rgba(0, 123, 255, 0.3)',
                        hoverBorderColor: '#0080ff',
                        hoverBorderWidth: 2,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: {duration: 500},
                    layout: {
                        padding: {
                            top: 15,
                            bottom: 5,
                            left: 5,
                            right: 5
                        }
                    },
                    plugins: {
                        legend: {display: false},
                        tooltip: {
                            enabled: true,
                            mode: 'index',
                            intersect: false,
                            displayColors: false,
                            backgroundColor: 'rgba(0, 0, 0, 0.7)',
                            titleFont: {size: 12},
                            bodyFont: {size: 12},
                            padding: 10,
                            cornerRadius: 4,
                            callbacks: {
                                title: function (tooltipItems) {
                                    if (!tooltipItems || tooltipItems.length === 0) return '';
                                    return `Time: ${tooltipItems[0].label}`;
                                },
                                label: function (context) {
                                    return `Views: ${context.raw.toFixed(0)}`;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            display: false,
                            grid: {display: false}
                        },
                        y: {
                            display: false,
                            beginAtZero: true,
                            suggestedMax: maxValue * 1.2, // Add 20% padding at top
                            grid: {display: false}
                        }
                    },
                    onHover: (event, chartElements) => {
                        const chartContainer = document.getElementById('myChart');
                        if (chartElements && chartElements.length > 0) {
                            chartContainer.style.cursor = 'pointer';
                        } else {
                            chartContainer.style.cursor = 'default';
                        }
                    },
                }
            });
        }

        /**
         * Load initial heatmap data
         */
        function loadInitialHeatmap() {
            $.ajax({
                type: 'GET',
                url: `{{ asset('storage/heatmaps/course_'.$course_details['id'].'_lesson_'.$lesson_details['id'].'.json') }}`,
                success: function (data) {
                    heatmapData = data;
                    renderHeatmap(data);

                    // Ensure heatmap is visible
                    const heatmapContainer = document.getElementById('video-heatmap');
                    if (heatmapContainer) {
                        heatmapContainer.classList.remove('d-none');
                    }
                },
                error: function (xhr, status, error) {
                    console.error("Error loading heatmap data:", error);
                    // Create empty heatmap if file doesn't exist
                    renderHeatmap({});

                    // Ensure heatmap is visible even if AJAX fails
                    const heatmapContainer = document.getElementById('video-heatmap');
                    if (heatmapContainer) {
                        heatmapContainer.classList.remove('d-none');
                    }
                }
            });
        }

        // Load heatmap on page load for admin users
        loadInitialHeatmap();
        @endif

        // Function to close popup directly with inline handler
        function closePopupDirectly() {
            // Remove popup
            document.getElementById('ad-popup-overlay')?.remove();

            // Resume video after a small delay
            setTimeout(function () {
                try {
                    if (player_js && document.getElementById("bunny-player")) {
                        player_js.play();
                    } else if (typeof player === 'object' && player !== null) {
                        player.play();
                    }
                } catch (err) {
                    console.error('Error resuming video:', err);
                }
            }, 100);

            // Prevent event bubbling
            return false;
        }

        // Make function available globally
        window.closePopupDirectly = closePopupDirectly;

        // Initialize player and event listeners on DOMContentLoaded
        $(document).ready(function () {
            // Initialize player
            initializePlayer();

            // Setup next video overlay and countdown
            setupNextVideoCountdown();

            // Focus mode event listeners
            $('#fullpage-screen').on('click', () => toggleFocusMode(true));
            $('.close-focus-btn, .focus-mode-overlay').on('click', () => toggleFocusMode(false));

            // Handle ESC key to exit focus mode
            $(document).on('keydown', function (e) {
                if (e.key === 'Escape' && $('body').hasClass('focus-mode')) {
                    toggleFocusMode(false);
                }
            });
        });
    </script>
@endpush

<style type="text/css">
    .plyr__progress video {
        width: 180px !important;
        height: auto !important;
        position: absolute !important;
        bottom: 30px !important;
        z-index: 1 !important;
        border-radius: 10px !important;
        border: 2px solid #fff !important;
        display: none;
        background-color: #000;
    }

    .plyr__progress video:hover {
        display: none !important;
    }

    video:not(.plyr:fullscreen video) {
        width: 100%;
        max-height: auto !important;
        max-height: 567px !important;
        border-radius: 5px;
    }

    /* Next Video Overlay Styling */
    .overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.85);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        visibility: hidden;
        backdrop-filter: blur(8px);
    }

    .next-video-container {
        background: rgba(255, 255, 255, 0.98);
        border-radius: 20px;
        padding: 32px 28px;
        text-align: center;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        max-width: 380px;
        width: 90%;
        position: relative;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .next-video-header {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
        margin-bottom: 24px;
    }

    .next-video-icon {
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, #754ffe, #6610f2);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        box-shadow: 0 8px 20px rgba(117, 79, 254, 0.3);
    }

    .next-video-title {
        font-size: 20px;
        font-weight: 600;
        color: #1e293b;
        margin: 0;
        letter-spacing: -0.02em;
    }

    .countdown-section {
        margin-bottom: 28px;
    }

    .circular-progress-container {
        position: relative;
        width: 80px;
        height: 80px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 auto 16px;
    }

    .outer-circle {
        stroke: rgba(117, 79, 254, 0.1);
        stroke-width: 3;
        fill: none;
    }

    .circular-progress {
        stroke-dasharray: 220;
        stroke-dashoffset: 220;
        stroke: #754ffe;
        stroke-width: 3;
        stroke-linecap: round;
        fill: none;
        transition: stroke-dashoffset 5s linear;
    }

    .progress-ring {
        transform: rotate(-90deg);
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }

    .countdown-number {
        position: absolute;
        font-size: 24px;
        font-weight: 700;
        color: #754ffe;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .countdown-text {
        font-size: 16px;
        color: #64748b;
        margin: 0;
        font-weight: 500;
    }

    .countdown-highlight {
        color: #754ffe;
        font-weight: 600;
    }

    .action-buttons {
        display: flex;
        gap: 12px;
        justify-content: center;
    }

    .btn-secondary, .btn-primary {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        border-radius: 10px;
        font-size: 14px;
        font-weight: 600;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        min-width: 120px;
        justify-content: center;
    }

    .btn-secondary {
        background: #f1f5f9;
        color: #64748b;
        border: 1px solid #e2e8f0;
    }

    .btn-secondary:hover {
        background: #e2e8f0;
        color: #475569;
        transform: translateY(-1px);
    }

    .btn-primary {
        background: linear-gradient(135deg, #754ffe, #6610f2);
        color: white;
        box-shadow: 0 4px 12px rgba(117, 79, 254, 0.3);
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #6610f2, #5209d0);
        transform: translateY(-1px);
        box-shadow: 0 6px 16px rgba(117, 79, 254, 0.4);
    }

    .btn-secondary:active, .btn-primary:active {
        transform: translateY(0);
    }

    /* Responsive Design */
    @media (max-width: 480px) {
        .next-video-container {
            padding: 24px 20px;
            max-width: 320px;
        }

        .next-video-title {
            font-size: 18px;
        }

        .action-buttons {
            flex-direction: column;
        }

        .btn-secondary, .btn-primary {
            width: 100%;
        }
    }

    /* Video Heatmap Styles */
    .video-heatmap-container {
        height: 120px;
        width: 100%;
        background-color: white;
        margin-top: 10px;
        margin-bottom: 10px;
        border-radius: 8px;
        overflow: hidden;
        position: relative;
        padding: 0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    #myChart {
        width: 100% !important;
        height: 100% !important;
    }
</style>

<div class="overlay" id="nextVideoOverlay">
    <div class="next-video-container">
        <div class="next-video-header">
            <div class="next-video-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M8 5v14l11-7z" fill="currentColor"/>
                </svg>
            </div>
            <h4 class="next-video-title">Video tiếp theo</h4>
        </div>

        <div class="countdown-section">
            <div class="circular-progress-container">
                <svg class="progress-ring" width="80" height="80">
                    <circle class="outer-circle" cx="40" cy="40" r="35"/>
                    <circle class="circular-progress" cx="40" cy="40" r="35"/>
                </svg>
                <div class="countdown-number" id="countdown">5</div>
            </div>
            <p class="countdown-text">Tự động phát sau <span class="countdown-highlight" id="countdownText">5 giây</span></p>
        </div>

        <div class="action-buttons">
            <button class="btn-secondary" id="cancelNextVideo">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
                Hủy bỏ
            </button>
            <button class="btn-primary" id="replayVideo">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M1 4v6h6M23 20v-6h-6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                Xem lại
            </button>
        </div>
    </div>
</div>


