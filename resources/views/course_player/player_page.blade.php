<style>
    .focus-mode .course-video-wrap {
        height: 100vh !important;
    }

    .focus-mode iframe.embed-responsive-item {
        height: 90vh !important;
    }

    /* Custom PDF viewer styles */
    .course-video-area-container .embed-responsive-item {
        border: none;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .focus-mode .course-video-area-container .embed-responsive-item {
        height: 100vh !important;
        border-radius: 0;
    }

    /* Content updating notification styles */
    .content-updating-notification {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 16px;
        padding: 60px 40px;
        text-align: center;
        color: white;
        box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
        position: relative;
        overflow: hidden;
        min-height: 400px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .content-updating-notification::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: shimmer 3s ease-in-out infinite;
    }

    .content-updating-notification .icon-container {
        width: 120px;
        height: 120px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 30px;
        backdrop-filter: blur(10px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        position: relative;
        z-index: 2;
    }

    .content-updating-notification .icon-container i {
        font-size: 48px;
        color: white;
        animation: pulse 2s ease-in-out infinite;
    }

    .content-updating-notification h3 {
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 16px;
        position: relative;
        z-index: 2;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .content-updating-notification p {
        font-size: 16px;
        opacity: 0.9;
        margin-bottom: 30px;
        line-height: 1.6;
        position: relative;
        z-index: 2;
        max-width: 400px;
    }

    .content-updating-notification .refresh-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 12px 30px;
        border-radius: 50px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        position: relative;
        z-index: 2;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .content-updating-notification .refresh-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.2);
    }

    @keyframes shimmer {
        0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(0deg); }
        50% { transform: translateX(0%) translateY(0%) rotate(180deg); }
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.1); opacity: 0.8; }
    }

    .loading-dots {
        display: inline-flex;
        gap: 4px;
        margin-left: 8px;
    }

    .loading-dots span {
        width: 6px;
        height: 6px;
        background: white;
        border-radius: 50%;
        animation: loading 1.4s ease-in-out infinite both;
    }

    .loading-dots span:nth-child(1) { animation-delay: -0.32s; }
    .loading-dots span:nth-child(2) { animation-delay: -0.16s; }

    @keyframes loading {
        0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
        40% { transform: scale(1); opacity: 1; }
    }
</style>

@if (isset($lesson_details->lesson_type))
    @if ($lesson_details->lesson_type == 'text')
        <div class="course-video-area-container border-primary">
            <div class="text_show">
                {!! removeScripts($lesson_details->attachment) !!}
            </div>
        </div>
    @elseif ($lesson_details->lesson_type == 'video-url')

        @if(empty($lesson_details->lesson_src))
            <div class="course-video-area-container border-primary border">
                <div class="content-updating-notification">
                    <div class="icon-container">
                        <i class="fas fa-tools"></i>
                    </div>
                    <h3>{{$lesson_details->title}}</h3>
                    <p>Nội dung bài học này đang được cập nhật. Vui lòng quay lại sau để có trải nghiệm học tập tốt nhất.</p>
                    <a href="javascript:void(0)" onclick="location.reload()" class="refresh-btn">
                        <i class="fas fa-sync-alt"></i>
                        Tải lại trang
                        <div class="loading-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </a>
                </div>
            </div>
        @else
            <div class="course-video-area-container border-primary border">
                <!-- Video -->
                <div class="course-video-wrap">
                    <div id="player" data-tour="step: 2; title: Xem video; content: Xem video để học">
                        @if(str_contains($lesson_details->lesson_src, 'mediadelivery.net'))
                            <iframe class="embed-responsive-item" width="100%" id="bunny-player"
                                    src="{{ $lesson_details->lesson_src }}"
                                    allowfullscreen>
                            </iframe>
                            @include('course_player.watermark')
                        @else
                            <iframe
                                src="{{ $lesson_details->lesson_src }}?origin=https://plyr.io&amp;iv_load_policy=3&amp;modestbranding=1&amp;playsinline=1&amp;showinfo=0&amp;rel=0&amp;enablejsapi=1"
                                allowfullscreen
                                allowtransparency
                            ></iframe>
                        @endif

                    </div>
                    @if(auth()->user()->role == 'admin' && addon_check('my.video_heatmap'))
                        <div id="video-heatmap" class="video-heatmap-container"></div>
                    @endif

                    @include('course_player.player_config')
                </div>
            </div>
        @endif
    @elseif($lesson_details->lesson_type == 'system-video')
        @if(empty($lesson_details->lesson_src))
            <div class="course-video-area-container border-primary border">
                <div class="content-updating-notification">
                    <div class="icon-container">
                        <i class="fas fa-tools"></i>
                    </div>
                    <h3>Khóa học đang cập nhật</h3>
                    <p>Nội dung bài học này đang được cập nhật. Vui lòng quay lại sau để có trải nghiệm học tập tốt nhất.</p>
                    <a href="javascript:void(0)" onclick="location.reload()" class="refresh-btn">
                        <i class="fas fa-sync-alt"></i>
                        Tải lại trang
                        <div class="loading-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </a>
                </div>
            </div>
        @else
            @php
                $watermark_type = get_player_settings('watermark_type');
                $lesson_video = $lesson_details->lesson_src;
                if ($watermark_type == 'ffmpeg') {
                    $origin = dirname($lesson_details->lesson_src);
                    $dir = $origin . '/watermark';
                    $file = str_replace($origin, '', $lesson_details->lesson_src);
                    $lesson_video = "{$dir}{$file}";
                }
            @endphp
            <div class="course-video-area-container border-primary border">
                <!-- Video -->
                <div class="course-video-wrap">
                    <div class=" bd-r-10 mb-16 position-relative bg-light custom-system-video">
                        <video id="player" playsinline controls oncontextmenu="return false;">
                            <source
                                src="{{ route('course.get_file', ['course_id' => $lesson_details->course_id, 'lesson_id' => $lesson_details->id]) }}"
                                type="video/mp4">
                        </video>
                        @include('course_player.player_config')
                        @include('course_player.watermark')
                    </div>

                </div>
            </div>
        @endif
    @elseif($lesson_details->lesson_type == 'image')
        @php
            // $img = asset('uploads/lesson_file/attachment/' . $lesson_details->attachment);
            $img = route('course.get_file', ['course_id' => $lesson_details->course_id, 'lesson_id' => $lesson_details->id])
        @endphp
        <img width="100%" class="max-w-auto" height="auto" src="{{ $img }}"/>
    @elseif($lesson_details->lesson_type == 'vimeo-url' && $lesson_details->video_type == 'vimeo')
        @if(empty($lesson_details->lesson_src))
            <div class="course-video-area-container border-primary border">
                <div class="content-updating-notification">
                    <div class="icon-container">
                        <i class="fas fa-tools"></i>
                    </div>
                    <h3>Khóa học đang cập nhật</h3>
                    <p>Nội dung bài học này đang được cập nhật. Vui lòng quay lại sau để có trải nghiệm học tập tốt nhất.</p>
                    <a href="javascript:void(0)" onclick="location.reload()" class="refresh-btn">
                        <i class="fas fa-sync-alt"></i>
                        Tải lại trang
                        <div class="loading-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </a>
                </div>
            </div>
        @else
            @php
                $video_url = $lesson_details->lesson_src;
                $video_id = explode('https://vimeo.com/', $video_url);
                $video_id = str_replace('https://vimeo.com/', '', $video_url);
            @endphp

            <div class="course-video-area-container border-primary border">
                <!-- Video -->
                <div class="course-video-wrap">
                    <div id="player">
                        <iframe height="500"
                                src="https://player.vimeo.com/video/{{ $video_id }}?loop=false&amp;byline=false&amp;portrait=false&amp;title=false&amp;speed=true&amp;transparent=0&amp;gesture=media"
                                allowfullscreen allowtransparency allow="autoplay"></iframe>
                        @include('course_player.player_config')
                        @include('course_player.watermark')
                    </div>

                </div>
            </div>
        @endif
    @elseif($lesson_details->lesson_type == 'google_drive')
        @if(empty($lesson_details->lesson_src))
            <div class="course-video-area-container border-primary border">
                <div class="content-updating-notification">
                    <div class="icon-container">
                        <i class="fas fa-tools"></i>
                    </div>
                    <h3>Khóa học đang cập nhật</h3>
                    <p>Nội dung bài học này đang được cập nhật. Vui lòng quay lại sau để có trải nghiệm học tập tốt nhất.</p>
                    <a href="javascript:void(0)" onclick="location.reload()" class="refresh-btn">
                        <i class="fas fa-sync-alt"></i>
                        Tải lại trang
                        <div class="loading-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </a>
                </div>
            </div>
        @else
            @php
                $video_url = $lesson_details->lesson_src;
                $url_array_1 = explode('/', $video_url . '/');
                $url_array_2 = explode('=', $video_url);
                $video_id = null;
                if ($url_array_1[4] == 'd'):
                    $video_id = $url_array_1[5];
                else:
                    $video_id = $url_array_2[1];
                endif;
            @endphp
            <div class="course-video-area-container border-primary border">
                <!-- Video -->
                <div class="course-video-wrap">
                    <video width="100%" height="680" id="player" playsinline controls>
                        <source class=""
                                src="https://www.googleapis.com/drive/v3/files/{{ $video_id }}?alt=media&key={{ get_settings('youtube_api_key') }}"
                                type="video/mp4">
                    </video>
                    @include('course_player.player_config')

                </div>
            </div>
        @endif
    @elseif($lesson_details->lesson_type == 'html5')
        @if(empty($lesson_details->lesson_src))
            <div class="course-video-area-container border-primary border">
                <div class="content-updating-notification">
                    <div class="icon-container">
                        <i class="fas fa-tools"></i>
                    </div>
                    <h3>Khóa học đang cập nhật</h3>
                    <p>Nội dung bài học này đang được cập nhật. Vui lòng quay lại sau để có trải nghiệm học tập tốt nhất.</p>
                    <a href="javascript:void(0)" onclick="location.reload()" class="refresh-btn">
                        <i class="fas fa-sync-alt"></i>
                        Tải lại trang
                        <div class="loading-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </a>
                </div>
            </div>
        @else
            <div class="course-video-area-container border-primary border">
                <!-- Video -->
                <div class="course-video-wrap">
                    <video width="100%" height="680" id="player" playsinline controls>
                        <source class="remove_video_src" src="{{ $lesson_details->lesson_src }}" type="video/mp4">
                    </video>
                    @include('course_player.player_config')
                    @include('course_player.watermark')

                </div>
            </div>
        @endif
    @elseif($lesson_details->lesson_type == 'document_type')
        @php
            $src = route('course.get_file', ['course_id' => $lesson_details->course_id, 'lesson_id' => $lesson_details->id])
        @endphp
        @if ($lesson_details->attachment_type == 'pdf')
            <iframe class="embed-responsive-item" width="100%" height="95vh"
                    src="{{ asset('assets/pdf-viewer/pdf-viewer.html') }}?file={{ urlencode($src) }}"
                    allowfullscreen>
            </iframe>

{{--            Old default PDF viewer--}}
{{--            <iframe class="embed-responsive-item" width="100%" src="{{ $src }}#navpanes=0&toolbar=0&view=FitH" allowfullscreen></iframe>--}}

{{--            <iframe class="embed-responsive-item" width="100%" height="95vh"--}}
{{--                    src="{{ route('pdf_canvas', ['course_id' => $lesson_details->course_id, 'lesson_id' => $lesson_details->id]) }}"--}}
{{--                    allowfullscreen></iframe>--}}

        @elseif($lesson_details->attachment_type == 'doc' || $lesson_details->attachment_type == 'ppt')
            <iframe class="embed-responsive-item" width='100%'
                    src="https://view.officeapps.live.com/op/embed.aspx?src={{ $src }}" frameborder='0'></iframe>
        @elseif($lesson_details->attachment_type == 'txt')
            <iframe class="embed-responsive-item" width='100%' src="{{ $src }}" frameborder='0'></iframe>
        @endif
{{--        @include('course_player.player_config')--}}
    @elseif($lesson_details->lesson_type == 'quiz')
        <div class="course-video-area-container border-primary pb-5">
            @include('course_player.quiz.index')
        </div>
    @else
        @if(empty($lesson_details->lesson_src))
            <div class="course-video-area-container border-primary border">
                <div class="content-updating-notification">
                    <div class="icon-container">
                        <i class="fas fa-tools"></i>
                    </div>
                    <h3>Khóa học đang cập nhật</h3>
                    <p>Nội dung bài học này đang được cập nhật. Vui lòng quay lại sau để có trải nghiệm học tập tốt nhất.</p>
                    <a href="javascript:void(0)" onclick="location.reload()" class="refresh-btn">
                        <i class="fas fa-sync-alt"></i>
                        Tải lại trang
                        <div class="loading-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </a>
                </div>
            </div>
        @else
            <div class="iframe-custom" style="position: relative">
                <iframe class="embed-responsive-item" width="100%"
                        @if(str_contains($lesson_details->lesson_src, 'mediadelivery.net'))
                        id="bunny-player"
                        @else
                        id="plyr-player"
                        @endif
                        src="{{ $lesson_details->lesson_src }}"
                        allowfullscreen>
                </iframe>
                @include('course_player.player_config')
                @include('course_player.watermark')
                @if(auth()->user()->role == 'admin' && addon_check('my.video_heatmap'))
                    <div id="video-heatmap" class="video-heatmap-container"></div>
                @endif
            </div>
        @endif

    @endif
@endif

<script>

</script>
