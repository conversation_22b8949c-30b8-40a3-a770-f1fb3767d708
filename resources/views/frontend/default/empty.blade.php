<div class="col-12 py-5">
    <div class="empty d-flex align-items-center justify-content-center flex-column gap-4">
        <span class="">
            <svg xmlns="http://www.w3.org/2000/svg" width="168" height="168" viewBox="0 0 168 168" fill="none">
                <path
                    d="M69 43C69 45.9667 69.8797 48.8668 71.528 51.3336C73.1762 53.8003 75.5189 55.7229 78.2598 56.8582C81.0006 57.9935 84.0166 58.2906 86.9264 57.7118C89.8361 57.133 92.5088 55.7044 94.6066 53.6066C96.7044 51.5088 98.133 48.8361 98.7118 45.9264C99.2906 43.0166 98.9935 40.0006 97.8582 37.2598C96.7229 34.5189 94.8003 32.1762 92.3336 30.528C89.8668 28.8797 86.9667 28 84 28C80.0231 28.0044 76.2104 29.5862 73.3983 32.3983C70.5862 35.2104 69.0044 39.0231 69 43ZM95 43C95 45.1756 94.3549 47.3023 93.1462 49.1113C91.9375 50.9202 90.2195 52.3301 88.2095 53.1627C86.1995 53.9952 83.9878 54.2131 81.854 53.7886C79.7202 53.3642 77.7602 52.3166 76.2218 50.7782C74.6834 49.2398 73.6358 47.2798 73.2114 45.146C72.7869 43.0122 73.0048 40.8005 73.8373 38.7905C74.6699 36.7805 76.0798 35.0625 77.8887 33.8538C79.6977 32.6451 81.8244 32 84 32C86.9164 32.0033 89.7123 33.1633 91.7745 35.2255C93.8367 37.2877 94.9967 40.0836 95 43Z"
                    fill="url(#paint0_linear_6_85)" />
                <path
                    d="M82 38V42C82 42.5304 82.2107 43.0391 82.5858 43.4142C82.9609 43.7893 83.4696 44 84 44C84.5304 44 85.0392 43.7893 85.4142 43.4142C85.7893 43.0391 86 42.5304 86 42V38C86 37.4696 85.7893 36.9609 85.4142 36.5858C85.0392 36.2107 84.5304 36 84 36C83.4696 36 82.9609 36.2107 82.5858 36.5858C82.2107 36.9609 82 37.4696 82 38ZM82.5899 46.5898C82.4932 46.6779 82.4091 46.7787 82.3399 46.8896C82.267 46.9973 82.2034 47.111 82.1499 47.2295C82.0982 47.3512 82.0613 47.4788 82.04 47.6094C82.0118 47.7376 81.9984 47.8687 82 48C82.0037 48.5293 82.2156 49.0359 82.5898 49.4102C82.9641 49.7844 83.4707 49.9963 84 50C84.395 49.9998 84.781 49.8827 85.1096 49.6634C85.4381 49.4442 85.6944 49.1327 85.8463 48.768C85.9981 48.4034 86.0386 48.002 85.9627 47.6144C85.8869 47.2268 85.698 46.8703 85.4199 46.5898C85.0373 46.2286 84.5311 46.0273 84.0049 46.0273C83.4787 46.0273 82.9724 46.2286 82.5899 46.5898Z"
                    fill="url(#paint1_linear_6_85)" />
                <path
                    d="M89.0577 22C89.6511 22 90.2311 21.8241 90.7244 21.4944C91.2178 21.1648 91.6023 20.6962 91.8294 20.1481C92.0564 19.5999 92.1159 18.9967 92.0001 18.4147C91.8843 17.8328 91.5986 17.2982 91.1791 16.8787C90.7595 16.4591 90.225 16.1734 89.643 16.0576C89.0611 15.9419 88.4579 16.0013 87.9097 16.2284C87.3615 16.4554 86.893 16.8399 86.5633 17.3333C86.2337 17.8266 86.0577 18.4067 86.0577 19C86.0586 19.7954 86.375 20.5579 86.9374 21.1203C87.4998 21.6827 88.2624 21.9991 89.0577 22ZM89.0577 17.5C89.3544 17.5 89.6444 17.588 89.8911 17.7528C90.1378 17.9176 90.33 18.1519 90.4436 18.426C90.5571 18.7001 90.5868 19.0017 90.5289 19.2926C90.471 19.5836 90.3282 19.8509 90.1184 20.0607C89.9086 20.2704 89.6414 20.4133 89.3504 20.4712C89.0594 20.5291 88.7578 20.4993 88.4837 20.3858C88.2096 20.2723 87.9754 20.08 87.8105 19.8334C87.6457 19.5867 87.5577 19.2967 87.5577 19C87.5581 18.6023 87.7163 18.221 87.9975 17.9398C88.2787 17.6586 88.66 17.5004 89.0577 17.5ZM118 9.06119C117.604 9.06119 117.218 9.17849 116.889 9.39825C116.56 9.61801 116.304 9.93037 116.152 10.2958C116.001 10.6613 115.961 11.0634 116.038 11.4514C116.116 11.8393 116.306 12.1957 116.586 12.4754C116.865 12.7551 117.222 12.9456 117.61 13.0228C117.998 13.0999 118.4 13.0603 118.765 12.9089C119.131 12.7576 119.443 12.5012 119.663 12.1723C119.883 11.8434 120 11.4568 120 11.0612C119.999 10.5309 119.788 10.0226 119.414 9.64765C119.039 9.2727 118.53 9.0618 118 9.06119ZM118 12.0612C117.802 12.0612 117.609 12.0025 117.444 11.8927C117.28 11.7828 117.152 11.6266 117.076 11.4439C117 11.2611 116.981 11.0601 117.019 10.8661C117.058 10.6721 117.153 10.4939 117.293 10.3541C117.433 10.2142 117.611 10.119 117.805 10.0804C117.999 10.0418 118.2 10.0616 118.383 10.1373C118.565 10.213 118.722 10.3412 118.831 10.5056C118.941 10.6701 119 10.8634 119 11.0612C119 11.3263 118.894 11.5805 118.707 11.768C118.519 11.9555 118.265 12.0609 118 12.0612ZM61.5534 4C61.1578 4 60.7711 4.1173 60.4422 4.33706C60.1133 4.55682 59.857 4.86918 59.7056 5.23463C59.5542 5.60009 59.5146 6.00222 59.5918 6.39018C59.669 6.77814 59.8595 7.13451 60.1392 7.41421C60.4189 7.69392 60.7752 7.8844 61.1632 7.96157C61.5512 8.03874 61.9533 7.99913 62.3187 7.84776C62.6842 7.69638 62.9966 7.44004 63.2163 7.11114C63.4361 6.78224 63.5534 6.39556 63.5534 6C63.5528 5.46975 63.3419 4.9614 62.9669 4.58646C62.592 4.21151 62.0836 4.00061 61.5534 4ZM61.5534 7C61.3556 7 61.1623 6.94135 60.9978 6.83147C60.8334 6.72159 60.7052 6.56541 60.6295 6.38268C60.5538 6.19996 60.534 5.99889 60.5726 5.80491C60.6112 5.61093 60.7064 5.43275 60.8463 5.29289C60.9861 5.15304 61.1643 5.0578 61.3583 5.01921C61.5523 4.98063 61.7533 5.00043 61.9361 5.07612C62.1188 5.15181 62.275 5.27998 62.3849 5.44443C62.4947 5.60888 62.5534 5.80222 62.5534 6C62.5531 6.26514 62.4477 6.51934 62.2602 6.70681C62.0727 6.89429 61.8185 6.99974 61.5534 7ZM134.058 30C133.662 30 133.276 30.1173 132.947 30.3371C132.618 30.5568 132.361 30.8692 132.21 31.2346C132.059 31.6001 132.019 32.0022 132.096 32.3902C132.173 32.7781 132.364 33.1345 132.644 33.4142C132.923 33.6939 133.28 33.8844 133.668 33.9616C134.056 34.0387 134.458 33.9991 134.823 33.8478C135.189 33.6964 135.501 33.44 135.721 33.1111C135.94 32.7822 136.058 32.3956 136.058 32C136.057 31.4698 135.846 30.9614 135.471 30.5865C135.096 30.2115 134.588 30.0006 134.058 30ZM134.058 33C133.86 33 133.667 32.9414 133.502 32.8315C133.338 32.7216 133.21 32.5654 133.134 32.3827C133.058 32.2 133.038 31.9989 133.077 31.8049C133.116 31.6109 133.211 31.4327 133.351 31.2929C133.49 31.153 133.669 31.0578 133.863 31.0192C134.057 30.9806 134.258 31.0004 134.44 31.0761C134.623 31.1518 134.779 31.28 134.889 31.4444C134.999 31.6089 135.058 31.8022 135.058 32C135.057 32.2651 134.952 32.5193 134.765 32.7068C134.577 32.8943 134.323 32.9997 134.058 33ZM17.0577 59.9466C17.0577 59.551 16.9404 59.1643 16.7207 58.8354C16.5009 58.5065 16.1886 58.2502 15.8231 58.0988C15.4577 57.9474 15.0555 57.9078 14.6676 57.985C14.2796 58.0622 13.9232 58.2527 13.6435 58.5324C13.3638 58.8121 13.1733 59.1684 13.0962 59.5564C13.019 59.9444 13.0586 60.3465 13.21 60.7119C13.3614 61.0774 13.6177 61.3898 13.9466 61.6095C14.2755 61.8293 14.6622 61.9466 15.0577 61.9466C15.588 61.946 16.0963 61.7351 16.4713 61.3601C16.8462 60.9852 17.0571 60.4768 17.0577 59.9466ZM14.0577 59.9466C14.0577 59.7488 14.1164 59.5555 14.2263 59.391C14.3362 59.2266 14.4923 59.0984 14.6751 59.0227C14.8578 58.947 15.0588 58.9272 15.2528 58.9658C15.4468 59.0044 15.625 59.0996 15.7648 59.2395C15.9047 59.3793 15.9999 59.5575 16.0385 59.7515C16.0771 59.9455 16.0573 60.1465 15.9816 60.3293C15.9059 60.512 15.7778 60.6682 15.6133 60.778C15.4489 60.8879 15.2555 60.9466 15.0577 60.9466C14.7926 60.9463 14.5384 60.8409 14.3509 60.6534C14.1634 60.4659 14.058 60.2117 14.0577 59.9466Z"
                    fill="#2D4356" />
                <path d="M45.641 29.958L47.128 28.002L46.189 27.47L45.235 29.66H45.203L44.233 27.486L43.278 28.034L44.749 29.943V29.974L42.448 29.676V30.74L44.764 30.443V30.474L43.278 32.383L44.169 32.947L45.187 30.74H45.218L46.157 32.931L47.143 32.368L45.641 30.49V30.459L48.003 30.74V29.676L45.641 29.989V29.958Z" fill="url(#paint2_linear_6_85)" />
                <path d="M8.83398 18.013L7.97798 19.112L8.49198 19.437L9.07798 18.166H9.09498L9.63598 19.428L10.204 19.103L9.33898 18.022V18.004L10.699 18.166V17.554L9.33898 17.734V17.716L10.195 16.59L9.65498 16.283L9.10498 17.544H9.08698L8.52798 16.292L7.97798 16.607L8.82598 17.707V17.725L7.50098 17.554V18.166L8.83398 17.995V18.013Z" fill="url(#paint3_linear_6_85)" />
                <path d="M159.058 50.66V49.617L156.741 49.923V49.893L158.199 47.975L157.278 47.453L156.342 49.601H156.311L155.36 47.468L154.423 48.005L155.866 49.878V49.908L153.609 49.617V50.66L155.881 50.369V50.399L154.423 52.271L155.298 52.824L156.296 50.66H156.326L157.247 52.809L158.214 52.256L156.741 50.414V50.384L159.058 50.66Z" fill="url(#paint4_linear_6_85)" />
                <path d="M158.501 8.53198L159.759 6.87798L158.965 6.42798L158.157 8.28098H158.13L157.31 6.44098L156.501 6.90398L157.746 8.51898V8.54598L155.8 8.29398V9.19398L157.759 8.94298V8.96898L156.501 10.584L157.256 11.061L158.117 9.19398H158.143L158.938 11.048L159.772 10.571L158.501 8.98198V8.95598L160.499 9.19398V8.29398L158.501 8.55898V8.53198Z" fill="url(#paint5_linear_6_85)" />
                <path d="M2 150C3.10457 150 4 149.105 4 148C4 146.895 3.10457 146 2 146C0.89543 146 0 146.895 0 148C0 149.105 0.89543 150 2 150Z" fill="#2D4356" />
                <path d="M11 146H8C7.46957 146 6.96086 146.211 6.58579 146.586C6.21071 146.961 6 147.47 6 148C6 148.53 6.21071 149.039 6.58579 149.414C6.96086 149.789 7.46957 150 8 150H11C11.5304 150 12.0391 149.789 12.4142 149.414C12.7893 149.039 13 148.53 13 148C13 147.47 12.7893 146.961 12.4142 146.586C12.0391 146.211 11.5304 146 11 146ZM160 146H157C156.47 146 155.961 146.211 155.586 146.586C155.211 146.961 155 147.47 155 148C155 148.53 155.211 149.039 155.586 149.414C155.961 149.789 156.47 150 157 150H160C160.53 150 161.039 149.789 161.414 149.414C161.789 149.039 162 148.53 162 148C162 147.47 161.789 146.961 161.414 146.586C161.039 146.211 160.53 146 160 146Z" fill="#2D4356" />
                <path d="M166 150C167.105 150 168 149.105 168 148C168 146.895 167.105 146 166 146C164.895 146 164 146.895 164 148C164 149.105 164.895 150 166 150Z" fill="#2D4356" />
                <path
                    d="M118.154 154H109.846C109.343 154.04 108.874 154.268 108.532 154.639C108.19 155.01 108 155.496 108 156C108 156.504 108.19 156.99 108.532 157.361C108.874 157.732 109.343 157.96 109.846 158H118.154C118.657 157.96 119.126 157.732 119.468 157.361C119.81 156.99 120 156.504 120 156C120 155.496 119.81 155.01 119.468 154.639C119.126 154.268 118.657 154.04 118.154 154ZM58.1539 154H49.8462C49.3434 154.04 48.8742 154.268 48.5321 154.639C48.19 155.01 48 155.496 48 156C48 156.504 48.19 156.99 48.5321 157.361C48.8742 157.732 49.3434 157.96 49.8462 158H58.1539C58.6567 157.96 59.1258 157.732 59.468 157.361C59.8101 156.99 60.0001 156.504 60.0001 156C60.0001 155.496 59.8101 155.01 59.468 154.639C59.1258 154.268 58.6567 154.04 58.1539 154ZM104 154H64C63.4696 154 62.9609 154.211 62.5858 154.586C62.2107 154.961 62 155.47 62 156C62 156.53 62.2107 157.039 62.5858 157.414C62.9609 157.789 63.4696 158 64 158H79.94V160H72C71.4696 160 70.9609 160.211 70.5858 160.586C70.2107 160.961 70 161.47 70 162C70 162.53 70.2107 163.039 70.5858 163.414C70.9609 163.789 71.4696 164 72 164H97C97.5305 164 98.0392 163.789 98.4142 163.414C98.7893 163.039 99 162.53 99 162C99 161.47 98.7893 160.961 98.4142 160.586C98.0392 160.211 97.5305 160 97 160H88.06V158H104C104.53 158 105.039 157.789 105.414 157.414C105.789 157.039 106 156.53 106 156C106 155.47 105.789 154.961 105.414 154.586C105.039 154.211 104.53 154 104 154Z"
                    fill="url(#paint6_linear_6_85)" />
                <path d="M134 139H34V143H134V139Z" fill="url(#paint7_linear_6_85)" />
                <path
                    d="M150.721 146H138V126H142C143.591 125.998 145.116 125.366 146.241 124.241C147.366 123.116 147.998 121.591 148 120V114C148 113.956 147.99 113.916 147.987 113.873C147.984 113.733 147.96 113.595 147.918 113.462C147.9 113.401 147.884 113.34 147.861 113.279C147.832 113.21 147.8 113.143 147.764 113.077C147.745 113.039 147.732 113 147.71 112.963L136.743 94.8877C136.206 94.0083 135.452 93.2813 134.554 92.7758C133.656 92.2703 132.644 92.0032 131.613 92H117V77.3622L122.648 75.4287C122.925 75.3336 123.178 75.1788 123.389 74.975C123.6 74.7713 123.764 74.5237 123.868 74.2498C123.973 73.9759 124.016 73.6823 123.994 73.3899C123.973 73.0975 123.888 72.8134 123.744 72.5576C123.744 72.5576 117.278 61.0299 117.273 61.0215C116.871 60.4206 116.281 59.9705 115.595 59.7422L99.0263 54.5839C98.1107 55.774 97.0583 56.8524 95.8907 57.7966L108.167 61.6184L84 69.207L59.833 61.6184L72.1093 57.7966C70.9417 56.8524 69.8893 55.774 68.9737 54.5839L52.4053 59.7422C51.7198 59.9713 51.1296 60.4212 50.7271 61.0215L44.2559 72.5576C44.1124 72.8134 44.027 73.0975 44.0055 73.3899C43.9841 73.6823 44.0272 73.9759 44.1318 74.2498C44.2363 74.5237 44.3999 74.7713 44.6107 74.975C44.8216 75.1788 45.0747 75.3336 45.352 75.4287L51 77.3622V92H36.3867C35.3562 92.0032 34.3437 92.2703 33.4457 92.7758C32.5477 93.2813 31.7941 94.0084 31.2568 94.8877L20.29 112.963C20.2677 113 20.2553 113.039 20.2357 113.077C20.1997 113.143 20.1675 113.21 20.1392 113.279C20.1157 113.34 20.0996 113.401 20.0824 113.462C20.0396 113.595 20.0162 113.733 20.0128 113.873C20.0101 113.916 20 113.956 20 114V120C20.0017 121.591 20.6344 123.116 21.7593 124.241C22.8841 125.366 24.4092 125.998 26 126H30V146H17.2787C16.9942 145.963 16.705 145.987 16.4305 146.07C16.1559 146.154 15.9024 146.295 15.6867 146.484C15.4711 146.673 15.2983 146.907 15.18 147.168C15.0616 147.429 15.0004 147.713 15.0004 148C15.0004 148.287 15.0616 148.571 15.18 148.832C15.2983 149.093 15.4711 149.327 15.6867 149.516C15.9024 149.705 16.1559 149.846 16.4305 149.93C16.705 150.013 16.9942 150.037 17.2787 150H150.721C151.006 150.037 151.295 150.013 151.57 149.93C151.844 149.846 152.098 149.705 152.313 149.516C152.529 149.327 152.702 149.093 152.82 148.832C152.938 148.571 153 148.287 153 148C153 147.713 152.938 147.429 152.82 147.168C152.702 146.907 152.529 146.673 152.313 146.484C152.098 146.295 151.844 146.154 151.57 146.07C151.295 145.987 151.006 145.963 150.721 146ZM114.294 63.8871L119.082 72.4209L92.3364 81.5772L87.0143 72.4538L114.294 63.8871ZM89.707 85.0078C89.94 85.4068 90.3039 85.7128 90.7369 85.8741C91.1698 86.0354 91.6453 86.0419 92.0825 85.8926L113 78.7316V92.4326L84 101.897L55 92.4326V78.7316L75.9175 85.8926C76.3548 86.0412 76.8299 86.0343 77.2627 85.8731C77.6956 85.7119 78.0594 85.4063 78.293 85.0078L84 75.2246L89.707 85.0078ZM48.9185 72.4209L53.7057 63.8871L80.9857 72.4538L75.6636 81.5772L48.9185 72.4209ZM34.6768 96.9619C34.856 96.669 35.1072 96.4268 35.4066 96.2584C35.7059 96.0901 36.0433 96.0011 36.3867 96H53.0378L83.3794 105.901C83.7827 106.033 84.2173 106.033 84.6206 105.901L114.962 96H131.613C131.957 96.0011 132.294 96.09 132.593 96.2584C132.893 96.4268 133.144 96.669 133.323 96.9619L142.447 112H25.5527L34.6768 96.9619ZM26 122C25.4698 121.999 24.9614 121.788 24.5865 121.414C24.2115 121.039 24.0006 120.53 24 120V116H144V120C143.999 120.53 143.788 121.039 143.414 121.414C143.039 121.788 142.53 121.999 142 122H26ZM134 146H34V126H134V146Z"
                    fill="#2D4356" />
                <defs>
                    <linearGradient id="paint0_linear_6_85" x1="84" y1="28" x2="84" y2="58" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#490BCE" />
                        <stop offset="1" stop-color="#CA0BCE" />
                    </linearGradient>
                    <linearGradient id="paint1_linear_6_85" x1="83.9999" y1="36" x2="83.9999" y2="50" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#490BCE" />
                        <stop offset="1" stop-color="#CA0BCE" />
                    </linearGradient>
                    <linearGradient id="paint2_linear_6_85" x1="45.2255" y1="27.47" x2="45.2255" y2="32.947" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#490BCE" />
                        <stop offset="1" stop-color="#CA0BCE" />
                    </linearGradient>
                    <linearGradient id="paint3_linear_6_85" x1="9.09998" y1="16.283" x2="9.09998" y2="19.437" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#490BCE" />
                        <stop offset="1" stop-color="#CA0BCE" />
                    </linearGradient>
                    <linearGradient id="paint4_linear_6_85" x1="156.334" y1="47.453" x2="156.334" y2="52.824" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#490BCE" />
                        <stop offset="1" stop-color="#CA0BCE" />
                    </linearGradient>
                    <linearGradient id="paint5_linear_6_85" x1="158.15" y1="6.42798" x2="158.15" y2="11.061" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#490BCE" />
                        <stop offset="1" stop-color="#CA0BCE" />
                    </linearGradient>
                    <linearGradient id="paint6_linear_6_85" x1="84" y1="154" x2="84" y2="164" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#490BCE" />
                        <stop offset="1" stop-color="#CA0BCE" />
                    </linearGradient>
                    <linearGradient id="paint7_linear_6_85" x1="84" y1="139" x2="84" y2="143" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#490BCE" />
                        <stop offset="1" stop-color="#CA0BCE" />
                    </linearGradient>
                </defs>
            </svg>
        </span>
        <h4 class="g-title">{{ get_phrase('No data found !') }}</h4>

        @if(Route::currentRouteName() == 'courses')
            <p class="w-50 text-center">{{ get_phrase("Please attempt utilizing the suitable keywords in your search query to obtain more precise results.") }}</p>
            <a href="{{route('courses')}}" class="eBtn gradient d-flex mt-3"><i class="fi-rr-arrow-alt-left me-2"></i> {{get_phrase('Back')}}</a>
        @elseif(Route::currentRouteName() == 'tutor_schedule' || Route::currentRouteName() == 'tutor.getSchedulesForDate')
            <p class="w-50 text-center">{{ get_phrase("Please try using the other dates.") }}</p>
        @else
            <p class="w-50 text-center">{{ get_phrase("Please try using the appropriate keywords.") }}</p>
            <a href="{{url()->previous()}}" class="eBtn gradient d-flex mt-3"><i class="fi-rr-arrow-alt-left me-2"></i> {{get_phrase('Back')}}</a>
        @endif
    </div>
</div>
