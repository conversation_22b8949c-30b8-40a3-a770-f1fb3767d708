@extends('layouts.default')
@push('title', '<PERSON><PERSON><PERSON><PERSON> trình Affiliate')
@push('meta')@endpush
@push('css')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* Modern Header Section */
        .affiliate-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .affiliate-header::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 200px;
            height: 200px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(50%, -50%);
        }

        .affiliate-header h1 {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 15px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .affiliate-header p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 0;
            font-weight: 400;
        }

        /* Modern Tab System */
        .modern-tabs {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 20px;
            padding: 6px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            margin-bottom: 0;
            display: flex;
            gap: 4px;
            border: 1px solid rgba(0, 0, 0, 0.04);
        }

        .tab-button {
            background: transparent;
            border: none;
            padding: 16px 28px;
            font-weight: 700;
            color: #64748b;
            border-radius: 16px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            flex: 1;
            text-align: center;
            font-size: 13px;
            position: relative;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .tab-button.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
            transform: translateY(-2px);
        }

        .tab-button:hover:not(.active) {
            background: rgba(255, 255, 255, 0.8);
            color: #475569;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* Modern Cards */
        .modern-card {
            background: white;
            border-radius: 24px;
            padding: 32px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.04);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            margin-bottom: 28px;
            position: relative;
            overflow: hidden;
        }

        .modern-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 24px 24px 0 0;
        }

        .modern-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
        }

        .copy-link-aff-coupon {
            cursor: pointer;
            color: #667eea;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .copy-link-aff-coupon:hover {
            color: #764ba2;
            transform: scale(1.05);
        }

        /* Balance Cards */
        .balance-card {
            position: relative;
            overflow: hidden;
            min-height: 320px;
            display: flex;
            flex-direction: column;
        }

        .balance-card.earnings {
            background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 50%, #fb923c 100%);
            color: #9a3412;
        }

        .balance-card.earnings::before {
            background: linear-gradient(135deg, #fb923c 0%, #ea580c 100%);
        }

        .balance-card.available {
            background: linear-gradient(135deg, #ecfdf5 0%, #a7f3d0 50%, #34d399 100%);
            color: #065f46;
        }

        .balance-card.available::before {
            background: linear-gradient(135deg, #34d399 0%, #059669 100%);
        }

        .balance-card-header {
            flex-shrink: 0;
            margin-bottom: 20px;
        }

        .balance-card-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .balance-card-footer {
            flex-shrink: 0;
            margin-top: auto;
            padding-top: 20px;
        }

        .balance-title {
            color: inherit;
            font-size: 14px;
            margin-bottom: 0;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            opacity: 0.8;
        }

        .balance-amount {
            color: inherit;
            font-size: 2.8rem;
            font-weight: 900;
            margin: 20px 0;
            display: flex;
            align-items: center;
            line-height: 1;
        }

        .balance-amount .currency-icon {
            margin-right: 12px;
            font-size: 2.2rem;
            opacity: 0.9;
        }

        .balance-info-box {
            background: rgba(255,255,255,0.15);
            border-radius: 16px;
            padding: 20px;
            backdrop-filter: blur(10px);
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            min-height: 80px;
        }

        /* Ensure equal spacing */
        .balance-card .balance-card-content {
            min-height: 180px;
        }

        .balance-card .balance-card-footer {
            margin-top: auto;
        }

        /* Modern Buttons */
        .modern-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 14px 28px;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .modern-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
            color: white;
        }

        .modern-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            box-shadow: none;
            transform: none;
        }

        .modern-btn.secondary {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
            box-shadow: 0 4px 15px rgba(107, 114, 128, 0.4);
        }

        .modern-btn.danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
        }

        /* Modern Table Styling */
        .modern-table-container {
            background: white;
            border-radius: 24px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            margin-top: 32px;
            border: 1px solid rgba(0, 0, 0, 0.04);
        }

        .modern-table {
            width: 100%;
            margin-bottom: 0;
            border-collapse: separate;
            border-spacing: 0;
            font-size: 14px;
        }

        .modern-table thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 800;
            padding: 24px 20px;
            border: none;
            text-transform: uppercase;
            font-size: 11px;
            letter-spacing: 1.2px;
            position: relative;
            text-align: left;
        }

        .modern-table thead th:first-child {
            border-radius: 24px 0 0 0;
        }

        .modern-table thead th:last-child {
            border-radius: 0 24px 0 0;
        }

        .modern-table tbody tr {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-bottom: 1px solid #f1f5f9;
        }

        .modern-table tbody tr:hover {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            transform: scale(1.01);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }

        .modern-table tbody td {
            padding: 20px;
            vertical-align: middle;
            border: none;
            color: #374151;
            font-weight: 500;
        }

        .modern-table tbody tr:last-child td:first-child {
            border-radius: 0 0 0 24px;
        }

        .modern-table tbody tr:last-child td:last-child {
            border-radius: 0 0 24px 0;
        }

        /* Modern Badge styling */
        .modern-badge {
            padding: 8px 16px;
            font-weight: 600;
            font-size: 11px;
            border-radius: 25px;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .modern-badge:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .modern-badge.success {
            background: linear-gradient(135deg, #d1fae5 0%, #10b981 100%);
            color: #065f46;
        }

        .modern-badge.warning {
            background: linear-gradient(135deg, #fef3c7 0%, #f59e0b 100%);
            color: #92400e;
        }

        .modern-badge.danger {
            background: linear-gradient(135deg, #fee2e2 0%, #ef4444 100%);
            color: #991b1b;
        }

        .modern-badge.info {
            background: linear-gradient(135deg, #dbeafe 0%, #3b82f6 100%);
            color: #1e40af;
        }

        /* Modern Pagination */
        .modern-pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            margin-top: 30px;
            padding: 20px 0;
        }

        .modern-pagination .page-item .page-link {
            border: none;
            background: white;
            color: #6b7280;
            padding: 12px 16px;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .modern-pagination .page-item.active .page-link {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .modern-pagination .page-item .page-link:hover:not(.active) {
            background: #f3f4f6;
            color: #374151;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 80px 20px;
            color: #9ca3af;
            position: relative;
        }

        .empty-state::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(102, 126, 234, 0.05) 0%, transparent 70%);
            border-radius: 50%;
            z-index: 0;
        }

        .empty-state > * {
            position: relative;
            z-index: 1;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 24px;
            opacity: 0.6;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .empty-state h3 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 12px;
            color: #374151;
        }

        .empty-state p {
            font-size: 1rem;
            margin-bottom: 0;
            color: #6b7280;
            max-width: 400px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.6;
        }

        /* Form Styling */
        .modern-form-group {
            margin-bottom: 25px;
        }

        .modern-form-label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .modern-form-control {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 14px 16px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            background: #f9fafb;
        }

        .modern-form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: white;
            outline: none;
        }

        .modern-input-group {
            display: flex;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .modern-input-group .form-control {
            border: none;
            border-radius: 0;
            background: white;
            flex: 1;
        }

        .modern-input-group .btn {
            border: none;
            border-radius: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 14px 20px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .modern-input-group .btn:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        }

        /* Modern Metrics Cards */
        .modern-metrics-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            transition: all 0.4s ease;
            border: 1px solid rgba(0, 0, 0, 0.05);
            box-shadow: 0 4px 25px rgba(0, 0, 0, 0.08);
            position: relative;
            overflow: hidden;
        }

        .modern-metrics-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--card-color-1), var(--card-color-2));
        }

        .modern-metrics-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .modern-metrics-card.clicks {
            --card-color-1: #667eea;
            --card-color-2: #764ba2;
        }

        .modern-metrics-card.purchases {
            --card-color-1: #f093fb;
            --card-color-2: #f5576c;
        }

        .modern-metrics-card.conversion {
            --card-color-1: #4facfe;
            --card-color-2: #00f2fe;
        }

        .modern-metrics-icon {
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            border-radius: 50%;
            font-size: 32px;
            color: white;
            background: linear-gradient(135deg, var(--card-color-1), var(--card-color-2));
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .modern-metrics-value {
            font-size: 3rem;
            font-weight: 800;
            margin: 15px 0 10px;
            background: linear-gradient(135deg, var(--card-color-1), var(--card-color-2));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .modern-metrics-label {
            color: #6b7280;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Modern Alert */
        .modern-alert {
            background: linear-gradient(135deg, #fef3c7 0%, #f59e0b 100%);
            border: none;
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
            color: #92400e;
        }

        .modern-alert::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 150px;
            height: 150px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transform: translate(50%, -50%);
        }

        .modern-alert-title {
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .modern-alert-title i {
            font-size: 1.5rem;
        }

        .modern-alert-content {
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .modern-alert-footer {
            font-size: 13px;
            font-style: italic;
            opacity: 0.8;
        }

        /* Modern Info Box */
        .modern-info-box {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: none;
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            display: flex;
            align-items: flex-start;
            gap: 15px;
            position: relative;
            overflow: hidden;
        }

        .modern-info-box::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(135deg, #0ea5e9, #0284c7);
        }

        .modern-info-icon {
            color: #0284c7;
            font-size: 20px;
            margin-top: 2px;
        }

        .modern-info-content {
            flex: 1;
        }

        .modern-info-title {
            font-weight: 700;
            color: #0c4a6e;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .modern-info-text {
            color: #075985;
            margin-bottom: 0;
            line-height: 1.5;
            font-size: 14px;
        }

        /* Modern Section Headers */
        .modern-section-header {
            margin-bottom: 20px;
        }

        .modern-section-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: #111827;
            margin-bottom: 8px;
        }

        .modern-section-subtitle {
            color: #6b7280;
            font-size: 14px;
            margin-bottom: 0;
            font-weight: 500;
        }

        /* Modern Select and Input */
        .modern-select {
            border: 2px solid #e5e7eb;
            border-radius: 16px;
            padding: 18px 20px;
            font-size: 14px;
            font-weight: 500;
            background: #f9fafb;
            transition: all 0.3s ease;
            width: 100%;
            height: 60px;
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 16px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 50px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
        }

        .modern-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 2px 8px rgba(0, 0, 0, 0.1);
            background: white;
            outline: none;
        }

        .modern-input-group {
            display: flex;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            height: 60px;
        }

        .modern-input-group .form-control {
            border: 2px solid #e5e7eb;
            border-right: none;
            border-radius: 16px 0 0 16px;
            background: #f9fafb;
            flex: 1;
            padding: 18px 20px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            height: 60px;
        }

        .modern-input-group .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: white;
            outline: none;
            z-index: 2;
            position: relative;
        }

        .modern-input-group .btn {
            border: 2px solid #e5e7eb;
            border-left: none;
            border-radius: 0 16px 16px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 18px 24px;
            font-weight: 600;
            transition: all 0.3s ease;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 60px;
        }

        .modern-input-group .btn:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            transform: scale(1.05);
        }

        /* Input Section Styling */
        .input-section {
            display: flex;
            flex-direction: column;
            height: 100%;
            min-height: 140px;
        }

        .input-section-header {
            flex-shrink: 0;
            margin-bottom: 20px;
        }

        .input-section-content {
            flex: 1;
            display: flex;
            align-items: flex-end;
        }

        /* Ensure both sections have same structure */
        .affiliate-input-row {
            display: flex;
            align-items: stretch;
        }

        .affiliate-input-row .col-lg-6 {
            display: flex;
            flex-direction: column;
        }

        /* Perfect alignment for both inputs */
        .modern-section-title {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 8px;
            color: #374151;
            line-height: 1.3;
        }

        .modern-section-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 0;
            line-height: 1.4;
        }

        /* Ensure headers have same height */
        .input-section-header {
            min-height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .modern-metrics-card {
                padding: 25px;
            }

            .modern-metrics-value {
                font-size: 2.5rem;
            }
        }

        @media (max-width: 992px) {
            .modern-tabs {
                flex-direction: column;
                gap: 6px;
            }

            .tab-button {
                padding: 14px 20px;
                font-size: 12px;
            }
        }

        @media (max-width: 768px) {
            .affiliate-header {
                padding: 30px 20px;
                text-align: center;
            }

            .affiliate-header h1 {
                font-size: 2rem;
            }

            .modern-card {
                padding: 24px;
                margin-bottom: 20px;
            }

            .modern-metrics-card {
                padding: 24px;
                margin-bottom: 20px;
            }

            .modern-metrics-value {
                font-size: 2rem;
            }

            .modern-metrics-icon {
                width: 60px;
                height: 60px;
                font-size: 24px;
            }

            .balance-card {
                min-height: 280px;
            }

            .balance-amount {
                font-size: 2.2rem;
            }

            .balance-card-header,
            .balance-card-content,
            .balance-card-footer {
                flex-shrink: 0;
            }

            .modern-table-container {
                margin-top: 20px;
            }

            /* Input Section Mobile */
            .modern-select,
            .modern-input-group .form-control,
            .modern-input-group .btn {
                height: 50px;
                padding: 14px 16px;
                font-size: 13px;
            }

            .modern-select {
                padding-right: 40px;
                background-size: 14px;
                background-position: right 12px center;
            }

            .modern-input-group .btn {
                min-width: 50px;
                padding: 14px 16px;
            }

            .affiliate-input-row {
                align-items: stretch !important;
            }

            .input-section {
                min-height: 120px;
            }

            .input-section-header {
                min-height: 60px;
                margin-bottom: 16px;
            }
        }

        @media (max-width: 576px) {
            .affiliate-header {
                padding: 25px 15px;
            }

            .affiliate-header h1 {
                font-size: 1.75rem;
            }

            .modern-card {
                padding: 20px;
                margin-bottom: 20px;
                border-radius: 16px;
            }

            .modern-table thead th,
            .modern-table tbody td {
                padding: 16px 12px;
                font-size: 12px;
            }

            .modern-btn {
                padding: 12px 20px;
                font-size: 13px;
            }

            .balance-amount {
                font-size: 1.8rem;
            }

            .balance-card {
                min-height: 240px;
            }

            .balance-amount {
                font-size: 1.8rem;
            }

            .balance-amount .currency-icon {
                font-size: 1.5rem;
            }

            .tab-button {
                padding: 12px 16px;
                font-size: 11px;
            }

            .modern-tabs {
                padding: 4px;
                border-radius: 16px;
            }

            /* Small Mobile Input Adjustments */
            .modern-select,
            .modern-input-group .form-control,
            .modern-input-group .btn {
                height: 48px;
                padding: 12px 14px;
                font-size: 12px;
                border-radius: 12px;
            }

            .modern-input-group {
                border-radius: 12px;
                height: 48px;
            }

            .modern-input-group .form-control {
                border-radius: 12px 0 0 12px;
            }

            .modern-input-group .btn {
                border-radius: 0 12px 12px 0;
                min-width: 48px;
                padding: 12px;
            }

            .modern-select {
                padding-right: 36px;
                background-size: 12px;
                background-position: right 10px center;
            }

            .input-section {
                min-height: 100px;
            }

            .input-section-header {
                margin-bottom: 12px;
                min-height: 50px;
            }

            .modern-section-title {
                font-size: 16px;
                margin-bottom: 6px;
            }

            .modern-section-subtitle {
                font-size: 12px;
                line-height: 1.3;
            }
        }

        /* Additional Improvements */
        .modern-card .row {
            margin: 0;
        }

        .modern-card .row > * {
            padding-left: 0;
            padding-right: 0;
        }

        /* Equal Height Cards */
        .row.mb-5 {
            display: flex;
            flex-wrap: wrap;
        }

        .row.mb-5 > [class*="col-"] {
            display: flex;
            flex-direction: column;
        }

        .row.mb-5 .balance-card {
            flex: 1;
            height: 100%;
        }

        .modern-table-container {
            position: relative;
        }

        .modern-table-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
            pointer-events: none;
            border-radius: 24px;
        }

        /* Animation Classes */
        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        .slide-up {
            animation: slideUp 0.6s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Loading States */
        .loading {
            position: relative;
            overflow: hidden;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% {
                left: -100%;
            }
            100% {
                left: 100%;
            }
        }

        /* Custom Scrollbar */
        .modern-table-container::-webkit-scrollbar {
            height: 8px;
        }

        .modern-table-container::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }

        .modern-table-container::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 4px;
        }

        .modern-table-container::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a67d8, #6b46c1);
        }
    </style>
@endpush

@section('content')
    <!------------ My profile area start  ------------>
    <section class="course-content">
        <div class="profile-banner-area"></div>
        <div class="container profile-banner-area-container">
            <div class="row">
                @include('frontend.default.student.left_sidebar')
                <div class="col-lg-9">
                    <!-- Modern Header -->
                    <div class="affiliate-header fade-in">
                        <h1>🚀 Chương trình Affiliate</h1>
                        <p>Kiếm tiền từ việc giới thiệu khóa học chất lượng cao đến bạn bè và cộng đồng của bạn</p>
                    </div>

                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if(get_settings('affiliate_notification') == 1)
                        <!-- Modern Alert -->
                        <div class="modern-alert slide-up">
                            <div class="modern-alert-title">
                                <i class="fas fa-gift"></i>
                                Ưu đãi độc quyền có sẵn!
                            </div>
                            <div class="modern-alert-content">
                                <p>Liên hệ với quản trị viên để nhận mã giảm giá độc quyền cho người được giới thiệu của bạn.
                                   Mã đặc biệt này sẽ tăng tỷ lệ chuyển đổi khi người dùng được giới thiệu mua khóa học.</p>
                            </div>
                            <div class="modern-alert-footer">
                                <span>⚠️</span> Lưu ý: Các mã độc quyền này chỉ hoạt động khi người mua sử dụng liên kết giới thiệu của bạn.
                            </div>
                        </div>
                    @endif

                    <!-- Affiliate Link Section -->
                    <div class="modern-card slide-up">
                        <div class="row g-4 affiliate-input-row">
                            <div class="col-lg-6">
                                <div class="input-section">
                                    <div class="input-section-header">
                                        <h3 class="modern-section-title">📚 Chọn khóa học</h3>
                                        <p class="modern-section-subtitle">Chọn khóa học cụ thể để tạo liên kết giới thiệu chuyên biệt</p>
                                    </div>
                                    <div class="input-section-content w-100">
                                        <select name="course_id" id="choose_course" class="modern-select w-100">
                                            <option value="">Chọn một khóa học...</option>
                                            @foreach($courses as $course)
                                                <option value="{{ $course->id }}"
                                                        data-link_affiliate="{{ route("course.details", ["slug"=>$course->slug,"ref"=> bin2hex("KH-".auth()->id())]) }}">{{ $course->title }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="input-section">
                                    <div class="input-section-header">
                                        <h3 class="modern-section-title">🔗 Liên kết Affiliate của bạn</h3>
                                        <p class="modern-section-subtitle">Chia sẻ liên kết này để kiếm hoa hồng từ việc mua khóa học</p>
                                    </div>
                                    <div class="input-section-content w-100">
                                        <div class="modern-input-group w-100">
                                            <input type="text" class="form-control" id="affiliateLink" readonly
                                                   value="{{ route('home') }}?ref={{ bin2hex("KH-".auth()->id()) }}">
                                            <button class="btn" type="button" id="copyButton">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="modern-info-box">
                            <div class="modern-info-icon">
                                <i class="fas fa-bell"></i>
                            </div>
                            <div class="modern-info-content">
                                <p class="modern-info-title">Thông báo hoa hồng</p>
                                <p class="modern-info-text">
                                    Khi người được giới thiệu hoàn tất giao dịch mua, thông báo hoa hồng sẽ được gửi đến email của bạn.
                                    Vui lòng kiểm tra thư mục spam và đánh dấu email của chúng tôi là "Không phải spam" để đảm bảo nhận được thông báo.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Modern Metrics Row -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="modern-metrics-card clicks slide-up">
                                <div class="modern-metrics-icon">
                                    <i class="fas fa-mouse-pointer"></i>
                                </div>
                                <div class="modern-metrics-value">{{ number_format((int)($total_clicks ?? 0)) }}</div>
                                <div class="modern-metrics-label">Tổng lượt click</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="modern-metrics-card purchases slide-up">
                                <div class="modern-metrics-icon">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <div class="modern-metrics-value">{{ number_format((int)($total_purchases ?? 0)) }}</div>
                                <div class="modern-metrics-label">Tổng giao dịch</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="modern-metrics-card conversion slide-up">
                                <div class="modern-metrics-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="modern-metrics-value">{{ $conversion_rate }}%</div>
                                <div class="modern-metrics-label">Tỷ lệ chuyển đổi</div>
                            </div>
                        </div>
                    </div>

                    <!-- Balance Cards -->
                    <div class="row mb-5">
                        <div class="col-lg-6 mb-4">
                            <div class="modern-card balance-card earnings slide-up">
                                <!-- Header -->
                                <div class="balance-card-header">
                                    <div class="d-flex align-items-center">
                                        <div style="width: 50px; height: 50px; background: rgba(255,255,255,0.2); border-radius: 15px; display: flex; align-items: center; justify-content: center; margin-right: 15px;">
                                            <i class="fas fa-chart-line" style="font-size: 24px; color: inherit;"></i>
                                        </div>
                                        <h3 class="balance-title">Tổng thu nhập affiliate</h3>
                                    </div>
                                </div>

                                <!-- Content -->
                                <div class="balance-card-content">
                                    <h2 class="balance-amount">

                                      {{ number_format(filter_var($affiliate_earning_amount, FILTER_SANITIZE_NUMBER_INT) ?? 0)}}  <span class="currency-icon">₫</span>
                                    </h2>

                                    <div class="balance-info-box">
                                        <div class="d-flex align-items-start">
                                            <i class="fas fa-info-circle me-3 mt-1" style="color: inherit; opacity: 0.8;"></i>
                                            <div>
                                                <h6 style="color: inherit; font-weight: 700; margin-bottom: 8px;">Chính sách giải ngân</h6>
                                                <p style="color: inherit; opacity: 0.9; margin: 0; font-size: 13px; line-height: 1.5;">
                                                    Hoa hồng sẽ được chuyển vào số dư khả dụng sau {{ $affiliate_payout_days }} ngày.
                                                    Thời gian này để xử lý hoàn tiền hoặc tranh chấp.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6 mb-4">
                            <div class="modern-card balance-card available slide-up">
                                <!-- Header -->
                                <div class="balance-card-header">
                                    <div class="d-flex align-items-center">
                                        <div style="width: 50px; height: 50px; background: rgba(255,255,255,0.2); border-radius: 15px; display: flex; align-items: center; justify-content: center; margin-right: 15px;">
                                            <i class="fas fa-wallet" style="font-size: 24px; color: inherit;"></i>
                                        </div>
                                        <h3 class="balance-title">Số dư khả dụng để rút</h3>
                                    </div>
                                </div>

                                <!-- Content -->
                                <div class="balance-card-content">
                                    <h2 class="balance-amount">
                                      {{ number_format(($affiliate_balance_confirmed_amount ?? 0)) }}  <span class="currency-icon">₫</span>
                                    </h2>

                                    <div class="balance-info-box">
                                        <p style="color: inherit; margin: 0; font-size: 13px; opacity: 0.9; text-align: center;">
                                            <i class="fas fa-info-circle me-2"></i>Số tiền rút tối thiểu: {{ currency($affiliate_payout_min_amount) }}
                                        </p>
                                    </div>
                                </div>

                                <!-- Footer -->
                                <div class="balance-card-footer">
                                    <button class="modern-btn w-100" data-bs-toggle="modal"
                                            data-bs-target="#withdrawalModal"
                                            style="background: rgba(255,255,255,0.2); color: inherit; border: 2px solid rgba(255,255,255,0.3); backdrop-filter: blur(10px); font-weight: 700;"
                                            {{ ($affiliate_balance_confirmed_amount <= 0 || $affiliate_balance_confirmed_amount < $affiliate_payout_min_amount) ? 'disabled' : '' }}>
                                        <i class="fas fa-paper-plane me-2"></i>Yêu cầu rút tiền
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Modern Tabs Section -->
                    <div class="modern-card slide-up">
                        <!-- Tab Header -->
                        <div class="d-flex flex-column flex-lg-row justify-content-between align-items-start align-items-lg-center mb-4 gap-3">
                            <div class="modern-tabs flex-grow-1">
                                <button class="tab-button active" id="earning-tab">
                                    <i class="fas fa-chart-line me-2"></i>Lịch sử thu nhập
                                </button>
                                <button class="tab-button" id="withdrawal-tab">
                                    <i class="fas fa-money-bill-wave me-2"></i>Lịch sử rút tiền
                                </button>
                                <button class="tab-button" id="coupon-tab">
                                    <i class="fas fa-ticket-alt me-2"></i>Mã giảm giá
                                </button>
                            </div>
                            <div class="flex-shrink-0">
                                <a href="{{ route('my.profile') }}#bank-info" class="modern-btn secondary">
                                    <i class="fas fa-university me-2"></i>Cập nhật ngân hàng
                                </a>
                            </div>
                        </div>

                        <div class="tab-content">
                            <div id="earning-content" class="active-tab">
                                <div class="modern-table-container">
                                    <div class="table-responsive">
                                        <table class="modern-table">
                                            <thead>
                                            <tr>
                                                <th>#</th>
                                                <th>Ngày</th>
                                                <th>Khóa học</th>
                                                <th>Số tiền gốc</th>
                                                <th>Hoa hồng</th>
                                                <th>Trạng thái</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            @forelse($affiliate_histories as $i=>$affiliate_history)
                                                <tr>
                                                    <td><strong>{{ ++$i }}</strong></td>
                                                    <td>{{ $affiliate_history->created_at->format('H:i - d/m/Y') }}</td>
                                                    <td>
                                                        <div>
                                                            <strong>{{ @$affiliate_history->course->title }}</strong>
                                                            <p class="mb-0 text-muted small">
                                                                <i class="fas fa-user me-1"></i>Mua bởi: {{ @$affiliate_history->user->name }}
                                                            </p>
                                                        </div>
                                                    </td>
                                                    <td><strong>{{ currency(@$affiliate_history->total_amount) }}</strong></td>
                                                    <td>
                                                        <strong class="text-success">{{ currency($affiliate_history->affiliate_amount) }}</strong>
                                                    </td>
                                                    <td>
                                                        <span class="modern-badge {{ $affiliate_history->is_approve_affiliate == 1 ? 'success' : 'warning' }}">
                                                            <i class="fas fa-{{ $affiliate_history->is_approve_affiliate == 1 ? 'check-circle' : 'clock' }} me-1"></i>
                                                            {{ $affiliate_history->is_approve_affiliate == 1 ? 'Đã duyệt' : 'Chờ duyệt' }}
                                                        </span>
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="6">
                                                        <div class="empty-state">
                                                            <i class="fas fa-chart-line"></i>
                                                            <h3>Chưa có lịch sử thu nhập</h3>
                                                            <p>Bắt đầu chia sẻ liên kết affiliate để kiếm hoa hồng đầu tiên!</p>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                @if($affiliate_histories->hasPages())
                                    <div class="modern-pagination">
                                        {{ $affiliate_histories->links() }}
                                    </div>
                                @endif
                            </div>

                            <div id="withdrawal-content" class="d-none">
                                <div class="modern-table-container">
                                    <div class="table-responsive">
                                        <table class="modern-table">
                                            <thead>
                                            <tr>
                                                <th>#</th>
                                                <th>Ngày yêu cầu</th>
                                                <th>Số tiền</th>
                                                <th>Thông tin ngân hàng</th>
                                                <th>Ngày xử lý</th>
                                                <th>Trạng thái</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            @forelse($withdrawals as $k=>$withdrawal)
                                                <tr>
                                                    <td><strong>{{ ++$k }}</strong></td>
                                                    <td>
                                                        @if(is_int($withdrawal->request_date))
                                                            {{ date('H:i - d/m/Y', $withdrawal->request_date) }}
                                                        @elseif(is_string($withdrawal->request_date))
                                                            {{ $withdrawal->request_date }}
                                                        @elseif($withdrawal->request_date instanceof \DateTime || $withdrawal->request_date instanceof \Carbon\Carbon)
                                                            {{ $withdrawal->request_date->format('H:i - d/m/Y') }}
                                                        @else
                                                            <span class="text-muted">N/A</span>
                                                        @endif
                                                    </td>
                                                    <td><strong class="text-primary">{{ currency($withdrawal->amount) }}</strong></td>
                                                    <td>
                                                        @if(!empty($withdrawal->note))
                                                            <div class="small">{!! nl2br(e($withdrawal->note)) !!}</div>
                                                        @else
                                                            <span class="text-muted">Chưa có thông tin</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        @if($withdrawal->processed_date)
                                                            @if(is_int($withdrawal->processed_date))
                                                                {{ date('H:i - d/m/Y', $withdrawal->processed_date) }}
                                                            @elseif(is_string($withdrawal->processed_date))
                                                                {{ $withdrawal->processed_date }}
                                                            @else
                                                                {{ \Carbon\Carbon::parse($withdrawal->processed_date)->format('H:i - d/m/Y') }}
                                                            @endif
                                                        @else
                                                            <span class="text-muted">Chưa xử lý</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        @if($withdrawal->status == 1)
                                                            <span class="modern-badge success">
                                                                <i class="fas fa-check-circle"></i>Hoàn thành
                                                            </span>
                                                        @elseif($withdrawal->status == 2)
                                                            <span class="modern-badge danger">
                                                                <i class="fas fa-times-circle"></i>Đã hủy
                                                            </span>
                                                        @else
                                                            <div class="d-flex align-items-center gap-2">
                                                                <span class="modern-badge warning">
                                                                    <i class="fas fa-clock"></i>Chờ xử lý
                                                                </span>
                                                                <button type="button"
                                                                        class="modern-btn danger btn-sm cancel-withdrawal-btn"
                                                                        data-id="{{ $withdrawal->id }}"
                                                                        data-amount="{{ currency($withdrawal->amount) }}">
                                                                    <i class="fas fa-times"></i>Hủy
                                                                </button>
                                                            </div>
                                                        @endif
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="6">
                                                        <div class="empty-state">
                                                            <i class="fas fa-money-bill-wave"></i>
                                                            <h3>Chưa có yêu cầu rút tiền</h3>
                                                            <p>Các yêu cầu rút tiền của bạn sẽ hiển thị ở đây</p>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                @if($withdrawals->hasPages())
                                    <div class="modern-pagination">
                                        {{ $withdrawals->links() }}
                                    </div>
                                @endif
                            </div>

                            <div id="coupon-content" class="d-none">
                                <div class="modern-table-container">
                                    <div class="table-responsive">
                                        <table class="modern-table">
                                            <thead>
                                            <tr>
                                                <th>#</th>
                                                <th>Khóa học</th>
                                                <th>Mã giảm giá</th>
                                                <th>Giảm giá (%)</th>
                                                <th>Ngày hết hạn</th>
                                                <th>Số lượng</th>
                                                <th>Chia sẻ liên kết</th>
                                                <th>Trạng thái</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            @forelse($coupons as $c=>$coupon)
                                                <tr>
                                                    <td><strong>{{ ++$c }}</strong></td>
                                                    <td>
                                                        @if($coupon->course_id)
                                                            <div>
                                                                <strong>Khóa học cụ thể:</strong>
                                                                <a href="{{ route('course.details', $coupon->course->slug) }}"
                                                                   class="text-primary">{{ $coupon->course->title }}</a>
                                                            </div>
                                                        @else
                                                            <div>
                                                                <strong>Áp dụng cho:</strong>
                                                                <a href="{{ route('home') }}" class="text-success">Tất cả khóa học</a>
                                                            </div>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <code class="bg-light px-2 py-1 rounded">{{ $coupon->code }}</code>
                                                    </td>
                                                    <td>
                                                        <span class="modern-badge info">{{ $coupon->discount }}%</span>
                                                    </td>
                                                    <td>
                                                        @if(is_string($coupon->expiry))
                                                            {{ date('d/m/Y', $coupon->expiry) }}
                                                        @elseif($coupon->expiry instanceof \DateTime || $coupon->expiry instanceof \Carbon\Carbon)
                                                            {{ $coupon->expiry->format('d/m/Y') }}
                                                        @else
                                                            <span class="text-muted">Không xác định</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <span class="modern-badge {{ $coupon->quantity === null ? 'success' : 'info' }}">
                                                            {{ $coupon->quantity === null ? "Không giới hạn" : number_format((int)$coupon->quantity) }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        @if($coupon->course_id)
                                                            <button class="modern-btn copy-link-aff-coupon"
                                                                    data-url="{{ route("course.details", ["slug"=>@$coupon->course->slug,"ref" => bin2hex("KH-".auth()->id()),"coupon"=>$coupon->code]) }}">
                                                                <i class="fas fa-copy"></i>Sao chép liên kết
                                                            </button>
                                                        @else
                                                            <button class="modern-btn copy-link-aff-coupon"
                                                                    data-url="{{ route('home') }}?ref={{ bin2hex("KH-".auth()->id()) }}&coupon={{ $coupon->code }}">
                                                                <i class="fas fa-copy"></i>Sao chép liên kết
                                                            </button>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        @if($coupon->status == 1)
                                                            <span class="modern-badge success">
                                                                <i class="fas fa-globe"></i>Công khai
                                                            </span>
                                                        @elseif($coupon->status == 2)
                                                            <span class="modern-badge warning">
                                                                <i class="fas fa-lock"></i>Riêng tư
                                                            </span>
                                                        @else
                                                            <span class="modern-badge danger">
                                                                <i class="fas fa-ban"></i>Vô hiệu hóa
                                                            </span>
                                                        @endif
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="8">
                                                        <div class="empty-state">
                                                            <i class="fas fa-ticket-alt"></i>
                                                            <h3>Chưa có mã giảm giá</h3>
                                                            <p>Liên hệ quản trị viên để nhận mã giảm giá độc quyền</p>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                @if($coupons->hasPages())
                                    <div class="modern-pagination">
                                        {{ $coupons->links() }}
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Modern Withdrawal Modal -->
    <div class="modal fade" id="withdrawalModal" tabindex="-1" aria-labelledby="withdrawalModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content" style="border-radius: 20px; border: none; box-shadow: 0 10px 40px rgba(0,0,0,0.1);">
                <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 20px 20px 0 0; border: none;">
                    <h5 class="modal-title" id="withdrawalModalLabel">
                        <i class="fas fa-wallet me-2"></i>Yêu cầu rút tiền
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                @if(!auth()->user()->bank_name || !auth()->user()->bank_account_number || !auth()->user()->bank_account_name)
                    <div class="modal-body" style="padding: 40px;">
                        <div class="text-center mb-4">
                            <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #fbbf24, #f59e0b); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px;">
                                <i class="fas fa-exclamation-triangle text-white" style="font-size: 2rem;"></i>
                            </div>
                            <h4 style="color: #374151; font-weight: 700;">Thiếu thông tin ngân hàng</h4>
                        </div>
                        <div class="modern-alert" style="margin-bottom: 30px;">
                            <p style="margin: 0; font-size: 16px;">Vui lòng cấu hình thông tin ngân hàng trước khi yêu cầu rút tiền.</p>
                        </div>
                        <div class="text-center">
                            <a href="{{ route('my.profile') }}#bank-info" class="modern-btn" style="padding: 15px 30px; font-size: 16px;">
                                <i class="fas fa-university"></i>Cập nhật thông tin ngân hàng
                            </a>
                        </div>
                    </div>
                    <div class="modal-footer" style="border: none; padding: 20px 40px;">
                        <button type="button" class="modern-btn secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times"></i>Đóng
                        </button>
                    </div>
                @else
                    <form action="{{ route('request.withdrawal') }}" method="post">
                        @csrf
                        <div class="modal-body" style="padding: 40px;">
                            <div class="modern-form-group">
                                <label for="amount" class="modern-form-label">
                                    <i class="fas fa-money-bill-wave me-2"></i>Số tiền rút
                                </label>
                                <div class="modern-input-group">
                                    <input type="text" class="modern-form-control" id="amount" name="amount"
                                           min="{{ $affiliate_payout_min_amount }}"
                                           max="{{ $affiliate_balance_confirmed_amount }}" step="1000" required
                                           placeholder="Nhập số tiền muốn rút"
                                           autocomplete="off">
                                    <span class="input-group-text" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; font-weight: 600;">VNĐ</span>
                                </div>
                                @if(session('error_withdraw'))
                                    <div class="mt-3 alert alert-danger" style="border-radius: 12px; border: none;">
                                        {{ session('error_withdraw') }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                    </div>
                                @endif
                                <div class="d-flex justify-content-between align-items-center mt-3">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Tối thiểu: {{ currency($affiliate_payout_min_amount) }} - Tối đa: {{ currency($affiliate_balance_confirmed_amount) }}
                                    </small>
                                    <button type="button" id="withdrawAllBtn" class="modern-btn secondary" style="padding: 8px 16px; font-size: 12px;">
                                        <i class="fas fa-coins me-1"></i>Rút toàn bộ
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer" style="border: none; padding: 20px 40px;">
                            <button type="button" class="modern-btn secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times"></i>Hủy
                            </button>
                            <button type="submit" class="modern-btn">
                                <i class="fas fa-paper-plane"></i>Gửi yêu cầu rút tiền
                            </button>
                        </div>
                    </form>
                @endif
            </div>
        </div>
    </div>

    <!-- Modern Cancel Withdrawal Modal -->
    <div class="modal fade" id="cancelWithdrawalModal" tabindex="-1" aria-labelledby="cancelWithdrawalModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content" style="border-radius: 20px; border: none; box-shadow: 0 10px 40px rgba(0,0,0,0.1);">
                <div class="modal-header" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white; border-radius: 20px 20px 0 0; border: none;">
                    <h5 class="modal-title" id="cancelWithdrawalModalLabel">
                        <i class="fas fa-exclamation-triangle me-2"></i>Hủy yêu cầu rút tiền
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="" method="post" id="cancelWithdrawalForm">
                    @csrf
                    <div class="modal-body" style="padding: 40px;">
                        <div class="text-center mb-4">
                            <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #fbbf24, #f59e0b); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px;">
                                <i class="fas fa-exclamation-triangle text-white" style="font-size: 2rem;"></i>
                            </div>
                            <h4 style="color: #374151; font-weight: 700;">Xác nhận hủy yêu cầu</h4>
                        </div>
                        <div class="text-center mb-4">
                            <p class="mb-3" style="font-size: 16px; color: #6b7280;">Bạn có chắc chắn muốn hủy yêu cầu rút tiền này không?</p>
                            <div class="modern-info-box" style="text-align: left;">
                                <div class="modern-info-icon">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div class="modern-info-content">
                                    <p class="modern-info-title">Số tiền: <span id="cancelAmount" class="fw-bold"></span></p>
                                    <p class="modern-info-text">Số tiền này sẽ được hoàn trả vào số dư khả dụng của bạn.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer" style="border: none; padding: 20px 40px;">
                        <button type="button" class="modern-btn secondary" data-bs-dismiss="modal">
                            <i class="fas fa-shield-alt"></i>Không, giữ yêu cầu
                        </button>
                        <button type="submit" class="modern-btn danger">
                            <i class="fas fa-times"></i>Có, hủy yêu cầu
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection
@push('js')

    <script>
        $(document).ready(function () {
            $('#choose_course').change(function () {
                var course_id = $(this).val();
                var link_affiliate = "{{ route('home') }}?ref={{ bin2hex("KH-".auth()->id()) }}";
                if (course_id) {
                    link_affiliate = $(this).find('option:selected').data('link_affiliate');
                }
                $('#affiliateLink').val(link_affiliate);

            });
        });
    </script>
    <script>
        $(document).ready(function () {
            // Tab switching
            $('#earning-tab').click(function () {
                $(this).addClass('active');
                $('#withdrawal-tab').removeClass('active');
                $('#coupon-tab').removeClass('active');
                $('#earning-content').removeClass('d-none');
                $('#withdrawal-content').addClass('d-none');
                $('#coupon-content').addClass('d-none');
            });

            $('#withdrawal-tab').click(function () {
                $(this).addClass('active');
                $('#earning-tab').removeClass('active');
                $('#coupon-tab').removeClass('active');
                $('#withdrawal-content').removeClass('d-none');

                $('#earning-content').addClass('d-none');
                $('#coupon-content').addClass('d-none');
            });

            $('#coupon-tab').click(function () {
                $(this).addClass('active');
                $('#coupon-content').removeClass('d-none');
                $('#coupon-content').addClass('active');

                $('#withdrawal-tab').removeClass('active');
                $('#withdrawal-content').addClass('d-none');

                $('#earning-tab').removeClass('active');
                $('#earning-content').addClass('d-none');
            });

            // Affiliate link copy functionality
            const affiliateLink = document.getElementById('affiliateLink');
            const copyButton = document.getElementById('copyButton');


            function copyToClipboard() {
                // Sử dụng Clipboard API hiện đại thay vì document.execCommand
                navigator.clipboard.writeText(affiliateLink.value)
                    .then(() => {
                        // Show success feedback
                        const originalText = copyButton.innerHTML;
                        copyButton.innerHTML = '<i class="fas fa-check"></i> copied';
                        setTimeout(() => {
                            copyButton.innerHTML = originalText;
                        }, 2000);
                    })
                    .catch(err => {
                        console.error('Could not copy text: ', err);
                        // Fallback method for older browsers
                        try {
                            affiliateLink.select();
                            document.execCommand('copy');
                        } catch (err) {
                            console.error('Fallback copy method failed: ', err);
                            alert('Không thể sao chép liên kết. Vui lòng chọn và sao chép liên kết thủ công.');
                        }
                    });
            }

            if (copyButton) {
                copyButton.addEventListener('click', copyToClipboard);
            }

            // Copy affiliate coupon link functionality
            $('.copy-link-aff-coupon').on('click', function () {
                const url = $(this).data('url');
                navigator.clipboard.writeText(url)
                    .then(() => {
                        // Show success feedback
                        const originalText = $(this).html();
                        $(this).html('<i class="fas fa-check"></i> Copy thành công!');
                        setTimeout(() => {
                            $(this).html(originalText);
                        }, 2000);
                    })
                    .catch(err => {
                        console.error('Could not copy text: ', err);
                        // Fallback method for older browsers
                        try {
                            const tempInput = document.createElement('input');
                            tempInput.value = url;
                            document.body.appendChild(tempInput);
                            tempInput.select();
                            document.execCommand('copy');
                            document.body.removeChild(tempInput);

                            // Show success feedback
                            const originalText = $(this).html();
                            $(this).html('<i class="fas fa-check"></i> Copy thành công!');
                            setTimeout(() => {
                                $(this).html(originalText);
                            }, 2000);
                        } catch (err) {
                            console.error('Fallback copy method failed: ', err);
                            alert('Không thể sao chép liên kết. Vui lòng chọn và sao chép liên kết thủ công.');
                        }
                    });
            });

            // Animation for modern metrics cards
            function animateMetrics() {
                $('.modern-metrics-card').each(function () {
                    $(this).addClass('animate__animated animate__fadeInUp');

                    // Add a small delay between each card animation
                    let delay = $(this).index() * 100;
                    $(this).css('animation-delay', delay + 'ms');
                });
            }

            // Call the animation when page loads
            animateMetrics();

            // Add hover effects for modern metrics cards
            $('.modern-metrics-card').hover(
                function () {
                    $(this).addClass('animate__pulse');
                },
                function () {
                    $(this).removeClass('animate__pulse');
                }
            );

            // Add loading animation to cards
            $('.modern-card, .modern-metrics-card').each(function(index) {
                $(this).css('animation-delay', (index * 0.1) + 's');
                $(this).addClass('slide-up');
            });

            // Xử lý sự kiện khi nhấn nút hủy yêu cầu rút tiền
            $('.cancel-withdrawal-btn').on('click', function () {
                const withdrawalId = $(this).data('id');
                const amount = $(this).data('amount');

                // Cập nhật thông tin trong modal
                $('#cancelAmount').text(amount);

                // Cập nhật action của form
                const formAction = "{{ route('cancel.withdrawal', ':id') }}".replace(':id', withdrawalId);
                $('#cancelWithdrawalForm').attr('action', formAction);

                // Hiển thị modal xác nhận
                $('#cancelWithdrawalModal').modal('show');
            });

            // Format tiền tệ cho input số tiền rút
            const amountInput = document.getElementById('amount');
            const maxAmount = parseFloat('{{ $affiliate_balance_confirmed_amount }}');

            // Định dạng số với dấu phẩy ngăn cách hàng nghìn (không có phần thập phân)
            function formatNumber(number) {
                // Chuyển đổi thành số nguyên và định dạng
                return Math.floor(Number(number)).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
            }

            // Chuyển đổi từ chuỗi được định dạng sang số
            function parseFormattedNumber(formattedNumber) {
                return parseInt(formattedNumber.replace(/,/g, '')) || 0;
            }

            // Rút toàn bộ số dư
            $('#withdrawAllBtn').click(function (e) {
                e.preventDefault();
                amountInput.value = formatNumber(maxAmount);
            });

            if (amountInput) {
                let cursorPosition = 0;
                let prevValue = '';

                // Format số tiền trong input
                function formatAmountInput() {
                    // Lưu vị trí con trỏ
                    cursorPosition = amountInput.selectionStart;

                    // Lưu độ dài trước khi định dạng
                    const oldLength = amountInput.value.length;

                    // Loại bỏ các ký tự không phải số
                    let value = amountInput.value.replace(/[^\d]/g, '');

                    // Định dạng số tiền với dấu phẩy
                    let formattedValue = value;
                    if (value !== '') {
                        formattedValue = formatNumber(value);
                    }

                    // Chỉ cập nhật nếu giá trị thay đổi để tránh vấn đề với việc di chuyển con trỏ
                    if (prevValue !== formattedValue) {
                        amountInput.value = formattedValue;
                        prevValue = formattedValue;

                        // Điều chỉnh vị trí con trỏ
                        const newLength = amountInput.value.length;
                        const lengthDiff = newLength - oldLength;
                        amountInput.setSelectionRange(cursorPosition + lengthDiff, cursorPosition + lengthDiff);
                    }
                }

                // Xử lý sự kiện input
                amountInput.addEventListener('input', function (e) {
                    formatAmountInput();
                });

                // Xử lý sự kiện keydown để giữ vị trí con trỏ
                amountInput.addEventListener('keydown', function (e) {
                    cursorPosition = amountInput.selectionStart;
                    prevValue = amountInput.value;
                });

                // Format khi blur
                amountInput.addEventListener('blur', function (e) {
                    if (e.target.value.trim() !== '') {
                        try {
                            const numValue = parseFormattedNumber(e.target.value);
                            if (numValue > 0) {
                                e.target.value = formatNumber(numValue);
                            } else {
                                e.target.value = '';
                            }
                        } catch (error) {
                            console.error('Error formatting number:', error);
                            e.target.value = '';
                        }
                    }
                });

                // Kiểm tra giá trị tối đa khi submit
                document.querySelector('form').addEventListener('submit', function (e) {
                    const enteredAmount = parseFormattedNumber(amountInput.value);

                    if (enteredAmount <= 0) {
                        e.preventDefault();
                        alert('Vui lòng nhập số tiền hợp lệ');
                        amountInput.focus();
                        return;
                    }

                    if (enteredAmount > maxAmount) {
                        e.preventDefault();
                        let formattedMax = formatNumber(maxAmount);
                        amountInput.value = formattedMax;
                        amountInput.focus();
                    }
                });

                // Khi mở modal, reset form
                $('#withdrawalModal').on('shown.bs.modal', function () {
                    amountInput.value = '';
                    setTimeout(function () {
                        amountInput.focus();
                    }, 500);
                });
            }
            @if(session('error_withdraw'))
            $('#withdrawalModal').modal('show');
            @endif
        });
    </script>
@endpush
