@extends('layouts.default')
@push('title', get_phrase('My profile'))
@push('meta')@endpush
@push('css')
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
<link href="{{ asset('assets/frontend/default/css/profile-enhancement.css') }}" rel="stylesheet" />
<style>
    /* Enhanced Profile Page Styling */
    .profile-card {
        background: #fff;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        padding: 40px;
        margin-bottom: 30px;
        border: 1px solid rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
    }

    .profile-card:hover {
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
    }

    .section-title {
        font-size: 24px;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 30px;
        padding-bottom: 15px;
        border-bottom: 3px solid #f57d30;
        position: relative;
    }

    .section-title::after {
        content: '';
        position: absolute;
        bottom: -3px;
        left: 0;
        width: 60px;
        height: 3px;
        background: linear-gradient(90deg, #f57d30, #f68034);
        border-radius: 2px;
    }

    .form-group {
        margin-bottom: 25px;
        position: relative;
    }

    .form-label {
        font-weight: 600;
        color: #34495e;
        margin-bottom: 8px;
        font-size: 14px;
        display: block;
    }

    .form-control {
        height: 50px;
        border: 2px solid #e8ecef;
        border-radius: 12px;
        padding: 12px 20px;
        font-size: 15px;
        transition: all 0.3s ease;
        background-color: #fafbfc;
    }

    .form-control:focus {
        border-color: #f57d30;
        box-shadow: 0 0 0 0.2rem rgba(245, 125, 48, 0.15);
        background-color: #fff;
        outline: none;
    }



    .bank-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 16px;
        padding: 30px;
        margin-top: 40px;
        border: 1px solid #dee2e6;
    }

    .bank-section .section-title {
        color: #495057;
        border-bottom-color: #6c757d;
    }

    .bank-section .section-title::after {
        background: linear-gradient(90deg, #6c757d, #495057);
    }

    .submit-btn {
        background: linear-gradient(135deg, #f57d30 0%, #f68034 100%);
        border: none;
        border-radius: 12px;
        padding: 15px 40px;
        font-size: 16px;
        font-weight: 600;
        color: white;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(245, 125, 48, 0.3);
        margin-top: 20px;
    }

    .submit-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(245, 125, 48, 0.4);
        background: linear-gradient(135deg, #e66d1f 0%, #e56f23 100%);
    }

    .submit-btn:active {
        transform: translateY(0);
    }

    /* Custom styling for Select2 */
    .select2-container--default {
        width: 100% !important;
    }

    .select2-container--default .select2-selection--single {
        height: 50px;
        border-radius: 12px;
        border: 2px solid #e8ecef;
        padding: 0;
        font-size: 15px;
        line-height: 1.5;
        background-color: #fafbfc;
        transition: all 0.3s ease;
    }

    .select2-container--default .select2-selection--single:focus-within {
        border-color: #f57d30;
        box-shadow: 0 0 0 0.2rem rgba(245, 125, 48, 0.15);
        background-color: #fff;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        color: #495057;
        line-height: 46px;
        padding-left: 20px;
        font-size: 15px;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 46px;
        right: 15px;
    }

    .select2-dropdown {
        border: 2px solid #e8ecef;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        z-index: 9999 !important;
    }

    .select2-search--dropdown .select2-search__field {
        padding: 12px;
        border: 2px solid #e8ecef;
        border-radius: 8px;
        font-size: 15px;
    }

    .select2-container--default .select2-results__option--highlighted[aria-selected] {
        background-color: #f57d30;
    }

    .select2-container--default .select2-results__option {
        padding: 12px 20px;
        font-size: 15px;
    }

    .select2-results__option {
        padding: 12px 20px;
        font-size: 15px;
    }

    /* Fix for bank item display */
    .bank-item {
        display: block;
        width: 100%;
    }

    .bank-code {
        color: #f57d30;
        font-weight: 600;
    }

    .bank-name {
        color: #495057;
    }

    /* Additional Select2 fixes */
    .select2-container--open .select2-dropdown {
        border-top: none;
        border-top-left-radius: 0;
        border-top-right-radius: 0;
    }

    .select2-container--open .select2-selection {
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
    }

    .password-hint {
        font-size: 12px;
        color: #6c757d;
        margin-top: 5px;
        font-style: italic;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .profile-card {
            padding: 25px;
            margin-bottom: 20px;
        }

        .section-title {
            font-size: 20px;
            margin-bottom: 20px;
        }

        .bank-section {
            padding: 20px;
            margin-top: 30px;
        }


    }
</style>
@endpush
@section('content')
    <!------------ My profile area start  ------------>
    <section class="course-content">
        <div class="profile-banner-area"></div>
        <div class="container profile-banner-area-container">
            <div class="row">
                @include('frontend.default.student.left_sidebar')
                <div class="col-lg-9">
                    <div class="profile-card">
                        <h4 class="section-title">
                            <i class="fas fa-user-edit me-2"></i>
                            Thông tin cá nhân
                        </h4>
                        <form action="{{ route('update.profile', $user_details->id) }}" method="POST">@csrf
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label for="name" class="form-label">
                                            <i class="fas fa-user me-1"></i>
                                            Họ và tên
                                        </label>
                                        <input type="text" class="form-control" name="name"
                                            value="{{ $user_details->name }}" id="name" placeholder="Nhập họ và tên của bạn">
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label for="email" class="form-label">
                                            <i class="fas fa-envelope me-1"></i>
                                            Địa chỉ email
                                        </label>
                                        <input type="email" class="form-control" name="email"
                                            value="{{ $user_details->email }}" id="email" placeholder="<EMAIL>">
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label for="phone" class="form-label">
                                            <i class="fas fa-phone me-1"></i>
                                            Số điện thoại
                                        </label>
                                        <input type="tel" class="form-control" name="phone"
                                            value="{{ $user_details->phone }}" id="phone" placeholder="0123456789">
                                    </div>
                                </div>

                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label for="old_password" class="form-label">
                                            <i class="fas fa-lock me-1"></i>
                                            Mật khẩu cũ
                                        </label>
                                        <input type="text" class="form-control" name="old_password" id="old_password" placeholder="Nhập mật khẩu cũ">
                                        <div class="password-hint">Để trống nếu không muốn đổi mật khẩu</div>
                                    </div>
                                </div>

                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label for="new_password" class="form-label">
                                            <i class="fas fa-key me-1"></i>
                                            Mật khẩu mới
                                        </label>
                                        <input type="text" class="form-control" name="new_password" id="new_password" placeholder="Nhập mật khẩu mới">
                                        <div class="password-hint">Để trống nếu không muốn đổi mật khẩu</div>
                                    </div>
                                </div>

                            </div>
                            <div class="bank-section">
                                <h4 class="section-title">
                                    <i class="fas fa-university me-2"></i>
                                    Thông Tin Ngân Hàng
                                </h4>
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="form-group">
                                            <label for="bank_name" class="form-label">
                                                <i class="fas fa-building me-1"></i>
                                                Tên ngân hàng
                                            </label>
                                            <select class="form-control" name="bank_name" id="bank_select">
                                                <option value="">Chọn Ngân hàng</option>
                                            </select>
                                            <input type="hidden" name="bank_code" id="bank_code" value="">
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="bank_account_number" class="form-label">
                                                <i class="fas fa-credit-card me-1"></i>
                                                Số tài khoản ngân hàng
                                            </label>
                                            <input type="text" class="form-control" name="bank_account_number"
                                                value="{{ $user_details->bank_account_number }}" id="bank_account_number" placeholder="Nhập số tài khoản">
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="bank_account_name" class="form-label">
                                                <i class="fas fa-user-tag me-1"></i>
                                                Tên Tài Khoản Ngân Hàng
                                            </label>
                                            <input type="text" class="form-control" name="bank_account_name"
                                                value="{{ $user_details->bank_account_name }}" id="bank_account_name" placeholder="Tên chủ tài khoản">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="submit-btn">
                                    <i class="fas fa-save me-2"></i>
                                    Lưu thay đổi
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!------------ My profile area end  ------------>
@endsection
@push('js')
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
    $(document).ready(function() {
        // Bank data from PHP helper
        var bankData = @json(getBankData());

        // Initialize Select2
        $('#bank_select').select2({
            placeholder: "Chọn Ngân hàng",
            allowClear: true,
            data: bankData.map(function(bank) {
                var fullBankInfo = bank.code + ' - ' + bank.name;
                return {
                    id: fullBankInfo,
                    text: fullBankInfo,
                    code: bank.code,
                    name: bank.name
                };
            }),
            dropdownParent: $('body'),
            width: '100%'
        });



        // Set initial value if there is one
        var currentBankName = "{{ $user_details->bank_name ?? '' }}";
        var currentBankCode = "{{ $user_details->bank_code ?? '' }}";

        if (currentBankName && currentBankName.trim() !== '') {
            // Try to find the bank in the data
            var foundBank = bankData.find(function(bank) {
                return bank.name === currentBankName ||
                       bank.code === currentBankCode ||
                       (currentBankName.includes(bank.code) && bank.code !== '');
            });

            if (foundBank) {
                var fullBankInfo = foundBank.code + ' - ' + foundBank.name;
                $('#bank_select').val(fullBankInfo).trigger('change');
                $('#bank_code').val(foundBank.code);
            } else {
                // If not found, set the current value as is
                $('#bank_select').val(currentBankName).trigger('change');
                $('#bank_code').val(currentBankCode);
            }
        }

        // When a bank is selected, update the hidden field
        $('#bank_select').on('select2:select', function(e) {
            var data = e.params.data;
            var selectedText = data.text || data.id;

            // Try to extract bank code from the selected text
            var parts = selectedText.split(' - ');
            if (parts.length >= 2) {
                $('#bank_code').val(parts[0]);
            } else {
                // Find the bank in our data
                var selectedBank = bankData.find(function(bank) {
                    return (bank.code + ' - ' + bank.name) === selectedText;
                });
                if (selectedBank) {
                    $('#bank_code').val(selectedBank.code);
                }
            }
        });



        // Form submission with loading state
        $('form').on('submit', function() {
            var submitBtn = $('.submit-btn');
            var originalText = submitBtn.html();

            submitBtn.prop('disabled', true);
            submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>Đang lưu...');

            // Re-enable button after 3 seconds (in case of error)
            setTimeout(function() {
                submitBtn.prop('disabled', false);
                submitBtn.html(originalText);
            }, 3000);
        });

        // Add smooth scroll animation for form sections
        $('.profile-card').css('opacity', '0').animate({
            opacity: 1
        }, 600);

        $('.bank-section').css('opacity', '0').delay(200).animate({
            opacity: 1
        }, 600);
    });
</script>
@endpush
