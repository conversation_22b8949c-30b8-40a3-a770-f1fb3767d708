<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    {{ config(['app.name' => get_settings('system_title')]) }}
    <title>@stack('title') | {{ config('app.name') }}</title>

    <!-- all the meta tags -->
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta content="" name="description" />
    <meta content="" name="author" />
    <link rel="shortcut icon" href="{{ asset(get_frontend_settings('favicon')) }}" />
    <meta content="{{ csrf_token() }}" name="csrf_token" />
    @stack('meta')
    <!-- End meta -->

    <link rel="stylesheet" type="text/css" href="{{ asset('assets/backend/vendors/bootstrap/bootstrap.min.css') }}" />

    {{-- FlatIcons --}}
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/global/icons/uicons-solid-rounded/css/uicons-solid-rounded.css') }}" />
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/global/icons/uicons-bold-rounded/css/uicons-bold-rounded.css') }}" />
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/global/icons/uicons-bold-straight/css/uicons-bold-straight.css') }}" />
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/global/icons/uicons-regular-rounded/css/uicons-regular-rounded.css') }}" />
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/global/icons/uicons-thin-rounded/css/uicons-thin-rounded.css') }}" />

    {{-- Font awesome icons --}}
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/global/icon-picker/fontawesome-iconpicker.min.css') }}" />
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/global/icon-picker/icons/fontawesome-all.min.css') }}" />

    {{-- Summernote --}}
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/global/summernote/summernote-lite.min.css') }}" rel="stylesheet">

    {{-- Yaireo Tagify --}}
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/global/tagify-master/dist/tagify.css') }}" rel="stylesheet" type="text/css" />

    {{-- Select2 --}}
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/global/select2/select2.min.css') }}" rel="stylesheet" type="text/css" />

    {{-- Date range picker --}}
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/backend/vendors/daterangepicker/daterangepicker.css') }}" rel="stylesheet" type="text/css" />


    {{-- Custom css --}}
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/backend/css/style.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/backend/css/responsive.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/backend/css/custom.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/backend/css/dashboard-modern.css') }}">

    @stack('css')

    <script type="text/javascript" src="{{ asset('assets/backend/js/jquery-3.7.1.min.js') }}"></script>
</head>

<body>
    <main>
        <!-- Sidebar Navigation -->
        <div class="ol-sidebar">
            @include('admin.navigation')
        </div>

        <div class="ol-sidebar-content">
            @include('admin.header')
            <div class="ol-body-content">
                <div class="container-fluid">
                    @yield('content')
                </div>
            </div>
        </div>
    </main>


    @include('admin.modal')

    <script src="{{ asset('assets/backend/vendors/bootstrap/bootstrap.bundle.min.js') }}"></script>
    {{-- Summernote --}}
    <script src="{{ asset('assets/global/summernote/summernote-lite.min.js') }}"></script>

    {{-- Icon --}}
    <script src="{{ asset('assets/global/icon-picker/fontawesome-iconpicker.min.js') }}"></script>

    {{-- Jquery form --}}
    <script type="text/javascript" src="{{ asset('assets/global/jquery-form/jquery.form.min.js') }}"></script>

    {{-- Jquery UI --}}
    <script type="text/javascript" src="{{ asset('assets/global/jquery-ui-1.13.2/jquery-ui.min.js') }}"></script>

    {{-- Yaireo Tagify --}}
    <script src="{{ asset('assets/global/tagify-master/dist/tagify.min.js') }}"></script>

    {{-- Select2 --}}
    <script src="{{ asset('assets/global/select2/select2.min.js') }}"></script>

    {{-- Date range picker --}}
    <script src="{{ asset('assets/backend/vendors/daterangepicker/moment.min.js') }}"></script>
    <script src="{{ asset('assets/backend/vendors/daterangepicker/daterangepicker.js') }}"></script>

    {{-- Html to PDF --}}
    <script src="{{ asset('assets/backend/js/html2pdf.bundle.min.js') }}"></script>

    {{-- Duration Picker --}}
    <script src="{{ asset('assets/global/duration-picker/DurationPickerMaker.js') }}"></script>

    <script src="{{ asset('assets/backend/js/script.js') }}"></script>

    @include('admin.toaster')
    @include('admin.common_scripts')
    @include('admin.init')

    <!-- System Expiry Check -->
    @if(session('system_expired'))
        <script>
            $(document).ready(function() {
                // Hiển thị popup hết hạn nếu chưa hiển thị trong session này
                if (!sessionStorage.getItem('expiry_popup_shown')) {
                    showSystemExpiredModal();
                    sessionStorage.setItem('expiry_popup_shown', 'true');
                }

                // Chặn tất cả các thao tác khi hết hạn
                blockSystemActions();
            });

            function showSystemExpiredModal() {
                const modal = `
                    <div class="modal fade" id="systemExpiredModal" tabindex="-1" aria-labelledby="systemExpiredModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
                        <div class="modal-dialog modal-dialog-centered">
                            <div class="modal-content border-0 shadow-lg">
                                <div class="modal-header bg-danger text-white border-0">
                                    <h5 class="modal-title" id="systemExpiredModalLabel">
                                        <i class="fi-rr-triangle-warning me-2"></i>
                                        Hệ thống đã hết hạn
                                    </h5>
                                </div>
                                <div class="modal-body text-center py-4">
                                    <div class="mb-3">
                                        <i class="fi-rr-calendar-exclamation text-danger" style="font-size: 48px;"></i>
                                    </div>
                                    <h6 class="mb-3">Hệ thống đã hết hạn vào ngày {{ session('expiry_date') }}</h6>
                                    <p class="text-muted mb-4">
                                        Vui lòng liên hệ với nhà cung cấp để gia hạn hệ thống.
                                        Trong thời gian này, bạn chỉ có thể xem dashboard và không thể thực hiện các thao tác khác.
                                    </p>
                                    <div class="d-flex gap-2 justify-content-center">
                                        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                                            <i class="fi-rr-eye me-1"></i>
                                            Tiếp tục xem
                                        </button>
                                        <a href="{{ route('logout') }}" class="btn btn-danger">
                                            <i class="fi-rr-sign-out me-1"></i>
                                            Đăng xuất
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                $('body').append(modal);
                $('#systemExpiredModal').modal('show');
            }

            function blockSystemActions() {
                // Chặn tất cả các link trừ dashboard và logout
                $('a:not([href*="dashboard"]):not([href*="logout"]):not([href="#"]):not([data-bs-dismiss])').on('click', function(e) {
                    e.preventDefault();
                    alert('Hệ thống đã hết hạn. Vui lòng gia hạn để tiếp tục sử dụng.');
                });

                // Chặn tất cả form submit trừ logout
                $('form:not([action*="logout"])').on('submit', function(e) {
                    e.preventDefault();
                    alert('Hệ thống đã hết hạn. Vui lòng gia hạn để tiếp tục sử dụng.');
                });

                // Chặn AJAX requests
                $(document).ajaxComplete(function(event, xhr, settings) {
                    if (xhr.status === 403) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response.system_expired) {
                                alert(response.message);
                            }
                        } catch (e) {
                            // Ignore parsing errors
                        }
                    }
                });
            }
        </script>
    @endif

    @stack('js')
</body>

</html>
